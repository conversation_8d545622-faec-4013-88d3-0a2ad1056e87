package com.sankuai.dz.product.detail.page.config.page.config.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/1/23 02:47
 */
@Data
public class BuildModuleVORequest implements Serializable {

    /**
     * key:moduleKey
     * value:该module是否可用（response不为null，code=200，VO不为null）
     */
    private Map<String, Boolean> moduleResponseMap;

    public BuildModuleVORequest() {
    }

    /**
     * 该module是否可用（response不为null，code=200，VO不为null）
     */
    public boolean isModuleKeyResponseValid(String moduleKey) {
        if (moduleResponseMap == null) {
            return false;
        }
        Boolean isSuccess = moduleResponseMap.get(moduleKey);
        return isSuccess != null && isSuccess;
    }

}
