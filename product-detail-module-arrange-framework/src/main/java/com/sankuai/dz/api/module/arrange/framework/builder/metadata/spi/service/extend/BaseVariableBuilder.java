package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.VariableBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;

import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 17:14
 */
public abstract class BaseVariableBuilder<T extends AbstractModuleVO> extends AbstractBuilder<T, VariableBuilderDefinition> {

}
