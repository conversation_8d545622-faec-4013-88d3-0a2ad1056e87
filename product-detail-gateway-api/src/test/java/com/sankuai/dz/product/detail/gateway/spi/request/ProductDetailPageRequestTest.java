package com.sankuai.dz.product.detail.gateway.spi.request;

import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageRequestTest {

    @InjectMocks
    private ProductDetailPageRequest request;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    /**
     * Test when all parameters are valid
     */
    @Test
    public void testCheckParamAllValid() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        ShepherdGatewayParam mockParam = mock(ShepherdGatewayParam.class);
        request.setShepherdGatewayParam(mockParam);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        assertNotNull("shepherdGatewayParam should not be null", request.getShepherdGatewayParam());
        assertTrue("productId should be positive", request.getProductId() > 0);
        assertTrue("productType should be valid", ProductTypeEnum.containsCode(request.getProductType()));
    }

    /**
     * Test when productType is valid (DEAL)
     */
    @Test
    public void testCheckParamProductTypeValidDeal() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        ShepherdGatewayParam mockParam = mock(ShepherdGatewayParam.class);
        request.setShepherdGatewayParam(mockParam);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert
        assertEquals("Product type should be DEAL", ProductTypeEnum.DEAL.getCode(), request.getProductType());
        assertSame("Should be valid DEAL product type", ProductTypeEnum.DEAL, ProductTypeEnum.fromCode(request.getProductType()));
    }

    /**
     * Test when productType is valid (RESERVE)
     */
    @Test
    public void testCheckParamProductTypeValidReserve() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        ShepherdGatewayParam mockParam = mock(ShepherdGatewayParam.class);
        request.setShepherdGatewayParam(mockParam);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.RESERVE.getCode());
        // act
        request.checkParam();
        // assert
        assertEquals("Product type should be RESERVE", ProductTypeEnum.RESERVE.getCode(), request.getProductType());
        assertSame("Should be valid RESERVE product type", ProductTypeEnum.RESERVE, ProductTypeEnum.fromCode(request.getProductType()));
    }

    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamShepherdGatewayParamNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setShepherdGatewayParam(null);
        request.setProductId(1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert - exception expected
    }

    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamProductIdZero() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(0L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert - exception expected
    }

    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamProductIdNegative() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(-1L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        request.checkParam();
        // assert - exception expected
    }

    @Test(expected = ProductDetailRequestIllegalException.class)
    public void testCheckParamProductTypeInvalid() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setShepherdGatewayParam(mock(ShepherdGatewayParam.class));
        request.setProductId(1L);
        // invalid product type
        request.setProductType(999);
        // act
        request.checkParam();
        // assert - exception expected
    }

    @Test
    public void testGetProductTypeEnumWhenProductTypeEnumIsNotNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductType(1);
        // act
        ProductTypeEnum result = request.getProductTypeEnum();
        // assert
        assertEquals(ProductTypeEnum.DEAL, result);
    }

    @Test
    public void testGetProductTypeEnumWhenProductTypeEnumIsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductType(1);
        // act
        ProductTypeEnum result = request.getProductTypeEnum();
        // assert
        assertEquals(ProductTypeEnum.DEAL, result);
    }

    private void setClientTypeEnum(ProductDetailPageRequest request, ClientTypeEnum value) throws Exception {
        Field field = ProductDetailPageRequest.class.getDeclaredField("clientTypeEnum");
        field.setAccessible(true);
        field.set(request, value);
    }

    private ClientTypeEnum getClientTypeEnum(ProductDetailPageRequest request) throws Exception {
        Field field = ProductDetailPageRequest.class.getDeclaredField("clientTypeEnum");
        field.setAccessible(true);
        return (ClientTypeEnum) field.get(request);
    }

    @Test
    public void testGetClientTypeEnumWhenCached() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        ClientTypeEnum expected = ClientTypeEnum.DP_APP;
        setClientTypeEnum(request, expected);
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertSame(expected, actual);
    }

    @Test
    public void testGetClientTypeEnumWhenNotCachedWithValidClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DP_APP的code
        request.setClientType(100);
        ClientTypeEnum expected = ClientTypeEnum.DP_APP;
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertEquals(expected, actual);
        // 验证已缓存
        assertSame(expected, getClientTypeEnum(request));
    }

    @Test
    public void testGetClientTypeEnumWhenNotCachedWithInvalidClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // 无效的code
        request.setClientType(-1);
        ClientTypeEnum expected = ClientTypeEnum.UNKNOWN;
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertEquals(expected, actual);
        // 验证已缓存
        assertSame(expected, getClientTypeEnum(request));
    }

    @Test
    public void testGetClientTypeEnumWithMinValidClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // UNKNOWN的code
        request.setClientType(0);
        ClientTypeEnum expected = ClientTypeEnum.UNKNOWN;
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertEquals(expected, actual);
        assertSame(expected, getClientTypeEnum(request));
    }

    @Test
    public void testGetClientTypeEnumWithMaxValidClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // MT_WAN_WU_XCX的code
        request.setClientType(504);
        ClientTypeEnum expected = ClientTypeEnum.MT_WAN_WU_XCX;
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertEquals(expected, actual);
        assertSame(expected, getClientTypeEnum(request));
    }

    @Test
    public void testGetClientTypeEnumWithOutOfRangeClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // 超出范围的code
        request.setClientType(Integer.MAX_VALUE);
        ClientTypeEnum expected = ClientTypeEnum.UNKNOWN;
        // act
        ClientTypeEnum actual = request.getClientTypeEnum();
        // assert
        assertEquals(expected, actual);
        assertSame(expected, getClientTypeEnum(request));
    }

    @Test
    public void testGetCustomParamWhenCustomParamIsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setCustomParam(null);
        // act
        String result = request.getCustomParam(RequestCustomParamEnum.productFirstCategoryId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCustomParamWhenKeyNotExist() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        CustomParam customParam = new CustomParam();
        request.setCustomParam(customParam);
        // act
        String result = request.getCustomParam(RequestCustomParamEnum.productFirstCategoryId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetCustomParamWhenKeyExist() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        CustomParam customParam = new CustomParam();
        request.setCustomParam(customParam);
        String expectedValue = "123";
        customParam.addParam(RequestCustomParamEnum.productFirstCategoryId.name(), expectedValue);
        // act
        String result = request.getCustomParam(RequestCustomParamEnum.productFirstCategoryId);
        // assert
        assertEquals(expectedValue, result);
    }

    @Test
    public void testGetCustomParamWithNullKey() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        CustomParam customParam = mock(CustomParam.class);
        request.setCustomParam(customParam);
        when(customParam.getParam(null)).thenThrow(NullPointerException.class);
        // act & assert
        try {
            request.getCustomParam(null);
            fail("Should throw NullPointerException");
        } catch (NullPointerException e) {
            // expected
        }
    }

    @Test
    public void testGetGpsCoordinateTypeEnumNotNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // Assuming 1 is a valid code for GpsCoordinateTypeEnum
        request.setGpsCoordinateType(1);
        // act
        GpsCoordinateTypeEnum result = request.getGpsCoordinateTypeEnum();
        // assert
        // Assuming 1 corresponds to GCJ02
        assertEquals(GpsCoordinateTypeEnum.GCJ02, result);
    }

    @Test
    public void testGetGpsCoordinateTypeEnumNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // Assuming 1 is a valid code for GpsCoordinateTypeEnum
        request.setGpsCoordinateType(1);
        // act
        GpsCoordinateTypeEnum result = request.getGpsCoordinateTypeEnum();
        // assert
        // Assuming 1 corresponds to GCJ02
        assertEquals(GpsCoordinateTypeEnum.GCJ02, result);
    }

    private void setMobileOSType(MobileOSTypeEnum value) throws Exception {
        Field field = ProductDetailPageRequest.class.getDeclaredField("mobileOSType");
        field.setAccessible(true);
        field.set(request, value);
    }

    private MobileOSTypeEnum getMobileOSType() throws Exception {
        Field field = ProductDetailPageRequest.class.getDeclaredField("mobileOSType");
        field.setAccessible(true);
        return (MobileOSTypeEnum) field.get(request);
    }

    @Test
    public void testGetMobileOSTypeWhenCached() throws Throwable {
        // arrange
        MobileOSTypeEnum cachedValue = MobileOSTypeEnum.IOS;
        setMobileOSType(cachedValue);
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(cachedValue, result);
        verifyNoInteractions(shepherdGatewayParam);
    }

    @Test
    public void testGetMobileOSTypeWhenNotCachedWithValidIOSValue() throws Throwable {
        // arrange
        setMobileOSType(null);
        when(shepherdGatewayParam.getMobileOSType()).thenReturn("ios");
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(MobileOSTypeEnum.IOS, result);
        assertSame(MobileOSTypeEnum.IOS, getMobileOSType());
        verify(shepherdGatewayParam).getMobileOSType();
    }

    @Test
    public void testGetMobileOSTypeWhenNotCachedWithValidAndroidValue() throws Throwable {
        // arrange
        setMobileOSType(null);
        when(shepherdGatewayParam.getMobileOSType()).thenReturn("android");
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(MobileOSTypeEnum.ANDROID, result);
        assertSame(MobileOSTypeEnum.ANDROID, getMobileOSType());
        verify(shepherdGatewayParam).getMobileOSType();
    }

    @Test
    public void testGetMobileOSTypeWhenNotCachedWithNullMobileOSType() throws Throwable {
        // arrange
        setMobileOSType(null);
        when(shepherdGatewayParam.getMobileOSType()).thenReturn(null);
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(MobileOSTypeEnum.UNKNOWN, result);
        assertSame(MobileOSTypeEnum.UNKNOWN, getMobileOSType());
        verify(shepherdGatewayParam).getMobileOSType();
    }

    @Test
    public void testGetMobileOSTypeWhenNotCachedWithEmptyMobileOSType() throws Throwable {
        // arrange
        setMobileOSType(null);
        when(shepherdGatewayParam.getMobileOSType()).thenReturn("");
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(MobileOSTypeEnum.UNKNOWN, result);
        assertSame(MobileOSTypeEnum.UNKNOWN, getMobileOSType());
        verify(shepherdGatewayParam).getMobileOSType();
    }

    @Test
    public void testGetMobileOSTypeWhenNotCachedWithInvalidValue() throws Throwable {
        // arrange
        setMobileOSType(null);
        when(shepherdGatewayParam.getMobileOSType()).thenReturn("invalid_os");
        // act
        MobileOSTypeEnum result = request.getMobileOSType();
        // assert
        assertEquals(MobileOSTypeEnum.UNKNOWN, result);
        assertSame(MobileOSTypeEnum.UNKNOWN, getMobileOSType());
        verify(shepherdGatewayParam).getMobileOSType();
    }

    @Test(expected = NullPointerException.class)
    public void testGetMobileOSTypeWhenShepherdGatewayParamIsNull() throws Throwable {
        // arrange
        setMobileOSType(null);
        request.setShepherdGatewayParam(null);
        // act
        // 应该抛出 NullPointerException
        request.getMobileOSType();
    }
}
