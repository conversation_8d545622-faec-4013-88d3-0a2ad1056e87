package com.sankuai.dz.product.detail.gateway.domain.pagetype;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.product.detail.gateway.api.page.type.request.PageTypeRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.CategoryRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserAgent;
import com.sankuai.dz.product.detail.gateway.domain.biz.DeviceInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.biz.ProductCategoryHandler;
import com.sankuai.dz.product.detail.gateway.domain.biz.UserInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.context.PageTypeContext;
import com.sankuai.dz.product.detail.gateway.domain.utils.DealLinkUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.HeaderUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.UserAgentUtil;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class PageTypeService {

    @Resource
    private ProductCategoryHandler productCategoryHandler;
    @Resource
    private UserInfoResolver userInfoResolver;
    @Resource
    private DeviceInfoResolver deviceInfoResolver;

    public String process(final PageTypeContext context) {
        // 1.参数校验
        if (context.getPageTypeRequest() == null || context.getPageTypeRequest().getProductId() <= 0 || MapUtils.isEmpty(context.getPageTypeRequest().getHeaderMap())) {
            return "";
        }

        // 解析header信息
        String userAgentStr = HeaderUtil.getUserAgent(context.getPageTypeRequest().getHeaderMap());
        UserAgent userAgent = UserAgentUtil.parseUserAgent(userAgentStr);
        boolean isDp = UserAgentUtil.DP_PLATFORM.equals(userAgent.getPlatform());
        context.setDp(isDp);
        context.setDeviceId(HeaderUtil.getDeviceId(isDp, context.getPageTypeRequest().getHeaderMap()));
        context.setUnionId(HeaderUtil.getUnionId(context.getPageTypeRequest().getHeaderMap()));

        // 并发查询（团购分类和用户ID）
        CompletableFuture<ProductCategory> categoryFuture = productCategoryHandler.getCategoryAsync(
                toCategoryRequest(context.getPageTypeRequest(), isDp)
        );
        CompletableFuture<Long> userFuture = getUserInfo(context);

        CompletableFuture<Void> allTasks = CompletableFuture.allOf(categoryFuture, userFuture);
        allTasks.thenRun(() -> {
            context.setProductCategory(categoryFuture.join());
            Long userId = userFuture.join();
            context.setUserId(userId == null ? 0L : userId);
        }).join();
        return DealLinkUtil.getPageType(context);
    }

    private CompletableFuture<Long> getUserInfo(PageTypeContext context) {
        if (!Lion.getBoolean(MdpContextUtils.getAppKey(), "deal.detail.exp.need.user.id", false)) {
            return CompletableFuture.completedFuture(0L);
        }
        return userInfoResolver.getUserId(context.isDp(), context.getPageTypeRequest().getHeaderMap());

    }

    private CategoryRequest toCategoryRequest(PageTypeRequest pageTypeRequest, boolean isDp) {
        return new CategoryRequest(pageTypeRequest.getProductId(), ProductTypeEnum.DEAL.getCode(), isDp ? PlatformEnum.DP : PlatformEnum.MT);
    }

}
