package com.sankuai.dz.product.detail.gateway.domain.page.config;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.context.InvokerInfo;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.ProductDetailPageConfigService;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/4/28 19:07
 */
@Slf4j
public abstract class PageConfigBaseHandler {

    @Resource
    private ProductDetailPageConfigService productDetailPageConfigService;

    public abstract Set<PageRegionEnum> getSupportedPageRegion();

    public void process(final GatewayContext context) {
        try {
            long startTime = System.currentTimeMillis();
            //构建PageConfigRoutingKey
            PageConfigRoutingKey routingKey = buildPageConfigRoutingKey(context.getProductCategory(), context.getSpiRequest());
            context.getSpiRequest().setPageConfigRoutingKey(routingKey);//填充到SPI的请求中，下游框架需要使用
            // 查询页面模块配置
            PageConfigDataDTO pageConfig = productDetailPageConfigService.getPageConfig(routingKey);
            context.setPageConfigData(pageConfig);
            Map<String, BizModuleConfigDTO> bizModuleMap = Optional.ofNullable(pageConfig).map(PageConfigDataDTO::getAllBizModule).orElse(Maps.newHashMap());
            checkPageConfig(pageConfig, bizModuleMap);
            //获取模块对应的SPI接口
            List<InvokerInfo> invokerInfoList = buildInvokerInfo(
                    Lists.newArrayList(bizModuleMap.values()), context.getSpiRequest(), context.getUnifiedRequest()
            );
            context.setInvokerInfoList(invokerInfoList);
            Cat.newCompletedTransactionWithDuration("ProductUnifiedPageService", this.getClass().getSimpleName(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("{}, Exception", this.getClass().getSimpleName(), e);
            throw e;
        }
    }

    protected abstract void checkPageConfig(final PageConfigDataDTO pageConfig,
                                            final Map<String, BizModuleConfigDTO> bizModuleMap) throws SPIConfigFatalException;

    /**
     * 分场景定制路由规则
     */
    protected abstract PageConfigSceneEnum getPageConfigSceneEnum();

    private PageConfigRoutingKey buildPageConfigRoutingKey(final ProductCategory productCategory,
                                                           final ProductDetailPageRequest detailPageRequest) {
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(getPageConfigSceneEnum().name());
        pageConfigRoutingKey.setProductType(detailPageRequest.getProductType());
        // 类目可以为空，会获取商品类型维度的兜底配置
        pageConfigRoutingKey.setProductFirstCategoryId(Optional.ofNullable(productCategory).map(ProductCategory::getFirstId).orElse(0));
        pageConfigRoutingKey.setProductSecondCategoryId(Optional.ofNullable(productCategory).map(ProductCategory::getSecondId).orElse(0));
        pageConfigRoutingKey.setProductThirdCategoryId(Optional.ofNullable(productCategory).map(ProductCategory::getThirdId).orElse(0));
        Map<String, String> optionalMap = Maps.newHashMap();
        optionalMap.put(OptionalRoutingKeyEnum.ClientType.name(), String.valueOf(detailPageRequest.getClientType()));
        optionalMap.put(OptionalRoutingKeyEnum.PageSource.name(), detailPageRequest.getPageSource());
        optionalMap.put(OptionalRoutingKeyEnum.TradeType.name(), "");
        optionalMap.put(OptionalRoutingKeyEnum.ShopCategoryId.name(), "");
        optionalMap.put(OptionalRoutingKeyEnum.AB.name(), "");
        pageConfigRoutingKey.setOptionalRoutingKeyMap(optionalMap);
        return pageConfigRoutingKey;
    }

    private List<InvokerInfo> buildInvokerInfo(List<BizModuleConfigDTO> bizModuleConfigDTOList,
                                               ProductDetailPageRequest pageRequest,
                                               UnifiedPageRequest unifiedRequest) {
        if (CollectionUtils.isEmpty(bizModuleConfigDTOList)) {
            return Lists.newArrayList();
        }
        Map<String, Set<String>> interfaceMap = Maps.newHashMap();
        for (BizModuleConfigDTO bizModuleConfigDTO : bizModuleConfigDTOList) {
            if (bizModuleConfigDTO == null) {
                continue;
            }
            String alias = bizModuleConfigDTO.getInterfaceAlias();
            // 未指定接口直接过滤
            if (StringUtils.isBlank(alias) || StringUtils.isBlank(bizModuleConfigDTO.getModuleKey())) {
                continue;
            }
            // 定制化过滤
            if (!isNeed(unifiedRequest, pageRequest, bizModuleConfigDTO)) {
                continue;
            }
            // 接口下模块聚合
            if (!interfaceMap.containsKey(alias)) {
                interfaceMap.put(alias, Sets.newHashSet(bizModuleConfigDTO.getModuleKey()));
            } else {
                interfaceMap.get(alias).add(bizModuleConfigDTO.getModuleKey());
            }
        }
        if (MapUtils.isEmpty(interfaceMap)) {
            return Lists.newArrayList();
        }
        List<InvokerInfo> invokerInfoList = Lists.newArrayListWithExpectedSize(interfaceMap.size());
        for (Map.Entry<String, Set<String>> entry : interfaceMap.entrySet()) {
            invokerInfoList.add(new InvokerInfo(entry.getKey(), entry.getValue()));
        }
        return invokerInfoList;
    }

    /**
     * 定制化过滤
     */
    protected abstract boolean isNeed(final UnifiedPageRequest unifiedPageRequest,
                                      final ProductDetailPageRequest detailPageRequest,
                                      final BizModuleConfigDTO bizModuleConfigDTO);

}
