package com.sankuai.dz.product.detail.gateway.spi.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.GpsCoordinateTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/10 11:10
 */
@Data
@TypeDoc(description = "SPI接口基础请求入参")
public class BaseRequest implements Serializable {

    @FieldDoc(description = "商品详情页业务身份（唯一路由标识），在网关层创建，用于拉取页面配置与接口路由规则",
            requiredness = Requiredness.REQUIRED, type = ClientTypeEnum.class)
    protected PageConfigRoutingKey pageConfigRoutingKey;

    @FieldDoc(description = "客户端类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = ClientTypeEnum.class)
    protected int clientType;

    @FieldDoc(description = "指定模块请求", rule = "优先级高于pageRegion", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/**********，暂时用文档维护，后续会用系统维护")
    protected Set<String> moduleKeys;

    @FieldDoc(description = " 网关层解析参数信息，比如用户信息，设备信息等", rule = "别用继承，继承容易造成参数的冲突", requiredness = Requiredness.REQUIRED)
    protected ShepherdGatewayParam shepherdGatewayParam;

    @FieldDoc(description = "统一商品Id", rule = "for后续团泛融合，优先使用", requiredness = Requiredness.OPTIONAL)
    protected long unifiedProductId;

    @FieldDoc(description = "统一SkuId", rule = "for后续团泛融合，优先使用", requiredness = Requiredness.OPTIONAL)
    protected long unifiedSkuId;

    @FieldDoc(description = "商品id", rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分", requiredness = Requiredness.REQUIRED)
    protected long productId;

    @FieldDoc(description = "skuId", rule = "根据productType传对应商品id，团购id区分平台，泛商品id不区分", requiredness = Requiredness.OPTIONAL)
    protected long skuId;

    @FieldDoc(description = "商品类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = ProductTypeEnum.class)
    protected int productType;

    @FieldDoc(description = "门店id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    protected long poiId;

    @FieldDoc(description = "门店id加密串", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    protected String poiIdEncrypt;

    @FieldDoc(description = "首页选择城市id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    protected int cityId;

    @FieldDoc(description = "定位城市id", rule = "区分平台", requiredness = Requiredness.OPTIONAL)
    protected int gpsCityId;

    @FieldDoc(description = "用户定位经度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    protected double userLng;

    @FieldDoc(description = "用户定位纬度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    protected double userLat;

    @FieldDoc(description = "选择城市定位经度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    protected double cityLng;

    @FieldDoc(description = "选择城市定位纬度", rule = "坐标系类型参考gpsCoordinateType", requiredness = Requiredness.OPTIONAL)
    protected double cityLat;

    @FieldDoc(description = "定位类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = GpsCoordinateTypeEnum.class)
    protected int gpsCoordinateType;

    @FieldDoc(description = "渠道", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2418267775，暂时用文档维护，后续会用系统维护")
    protected String pageSource;

    @FieldDoc(description = "业务定制参数", requiredness = Requiredness.OPTIONAL,
            typeName = "参考https://km.sankuai.com/collabpage/2695177049，暂时用文档维护，后续会用系统维护")
    protected CustomParam customParam;

    @FieldDoc(description = "价格一致性加密串", requiredness = Requiredness.REQUIRED, typeName="https://km.sankuai.com/collabpage/1830735597")
    protected String pricecipher;

    @FieldDoc(description = "诚信字段", requiredness = Requiredness.REQUIRED)
    protected String cx;

}
