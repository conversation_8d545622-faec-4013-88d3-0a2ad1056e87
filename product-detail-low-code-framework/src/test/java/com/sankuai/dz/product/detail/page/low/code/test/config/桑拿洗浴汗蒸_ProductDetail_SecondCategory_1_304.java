package com.sankuai.dz.product.detail.page.low.code.test.config;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.PojoTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.version.ProductVersionConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.utils.json.JacksonUtils;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.HashSet;

/**
 * @Author: guangyujie
 * @Date: 2025/6/10 21:59
 */

public class 桑拿洗浴汗蒸_ProductDetail_SecondCategory_1_304 extends BaseConfigTester {

    @Test
    public void testConfig() {
        ModuleDisplayConfigBO moduleDisplayConfigBO = buildStructuredDetailConfig(
                new ProductVersionConfigBO(20408612L, 76L, null),
                buildCardContainer(
                        buildStructBizComponent(
                                "Description",
                                Lists.newArrayList(
                                        buildBaseTypeField("description", "'测试固定文案返回'")
                                ),
                                new DataSourceConfigBO("ProductAttrFetcher", Sets.newHashSet("aaa", "bbb", "ccc")),
                                new DataSourceConfigBO("ProductOriginDetailFetcher", new HashSet<>())
                        )
                ),
                buildStructBizComponent(
                        "Service_Project_MN",
                        Lists.newArrayList(
                                buildBaseTypeField("titleWithPrefixDot", "'浴资票'"),
                                buildBaseTypeField("optionalCount", "2"),
                                new PojoTypeFieldDisplayConfigBO(
                                        "serviceProjectItems",
                                        true,
                                        new RuleConfigDTO(RuleEngineType.AVIATOR, "XXFetcher.list"),
                                        Lists.newArrayList(
                                                buildBaseTypeField("itemName", "let a = XXFetcher.list[index0];a.itemName"),
                                                buildBaseTypeField("itemDesc", "let a = XXFetcher.list[index0];a.itemDesc"),
                                                buildBaseTypeField("itemExtra", "let a = XXFetcher.list[index0];a.itemExtra"),
                                                new PojoTypeFieldDisplayConfigBO(
                                                        "serviceProjectItems",
                                                        true,
                                                        new RuleConfigDTO(RuleEngineType.AVIATOR, "let a = XXFetcher.list[index0];a.serviceProjectItems"),
                                                        Lists.newArrayList(
                                                                buildBaseTypeField("itemName", "let a = XXFetcher.list[index0];let b = a.serviceProjectItems[index1]; b.itemName")
                                                        )
                                                )
                                        )
                                )
                        ),
                        new DataSourceConfigBO("XXFetcher", new HashSet<>())
//                        new DataSourceConfigBO("ProductPurchaseNoteFetcher", Sets.newHashSet("aaa", "bbb"))
                )
        );
        System.out.println(JacksonUtils.serialize(buildPageConfig(
                moduleDisplayConfigBO
        )));
        assert moduleDisplayConfigBO != null;
    }

}