package com.sankuai.dz.product.detail.gateway.domain.context;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.domain.bean.DeviceInfo;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserInfo;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 18:22
 */
@Data
public class GatewayContext {

    /**
     * 0.前端请求入参
     */
    private final UnifiedPageRequest unifiedRequest;
    /**
     * 1.1 SPI请求入参
     */
    private ProductDetailPageRequest spiRequest;
    /**
     * 1.2 商品类目信息
     */
    private ProductCategory productCategory;
    /**
     * 1.2 用户信息
     */
    private UserInfo userInfo;
    /**
     * 1.2 设备信息
     */
    private DeviceInfo deviceInfo;
    /**
     * 1.3 通用打点维度
     */
    private Map<String, String> logMetricDimension;
    /**
     * 2.1 SPI调用信息
     */
    private List<InvokerInfo> invokerInfoList;
    /**
     * 2.3 页面配置
     */
    private PageConfigDataDTO pageConfigData;
    /**
     * 3.SPI调用结果
     */
    private List<GenericProductDetailPageResponse> spiResponseList;
    /**
     * 4.页面最终结果
     */
    private ProductDetailPageMergeResponse pageResponse;

    public GatewayContext(UnifiedPageRequest unifiedRequest) {
        this.unifiedRequest = unifiedRequest;
    }

}
