package com.sankuai.dz.product.detail.metadata.sdk.page.config.dto;

import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import lombok.Data;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/4/25 11:47
 */
@Data
public class MergeResultCache {

    private final ConcurrentHashMap<String, ConcurrentHashMap<String, PageConfigDataDTO>> mergeResultCache = new ConcurrentHashMap<>();

    public PageConfigDataDTO get(String mainKey, String optionalKey) {
        ConcurrentHashMap<String, PageConfigDataDTO> map = mergeResultCache.get(mainKey);
        if (map == null) {
            return null;
        }
        return map.get(optionalKey);
    }

    public void put(String mainKey, String optionalKey, PageConfigDataDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("聚合后的配置为null，不能存入缓存");
        }
        ConcurrentHashMap<String, PageConfigDataDTO> map = mergeResultCache.computeIfAbsent(mainKey, k -> new ConcurrentHashMap<>());
        map.put(optionalKey, dto);
    }

    public synchronized void clean(String lionKey) {
        mergeResultCache.remove(lionKey);
    }

}
