package com.sankuai.dz.product.detail.page.config.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/1/16 11:29
 */
@Getter
public enum InterfaceTypeEnum {

    //HTTP("http", "http请求"),
    PIGEON("pigeon", "pigeon-rpc请求"),
    //THRIFT("thrift", "thrift-rpc请求")
    ;

    private final String code;
    private final String desc;

    InterfaceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}