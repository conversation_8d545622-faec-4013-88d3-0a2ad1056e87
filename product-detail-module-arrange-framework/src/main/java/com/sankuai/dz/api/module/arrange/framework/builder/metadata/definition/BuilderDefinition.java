package com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.component.annotation.BizComponent;
import com.sankuai.dz.api.module.arrange.framework.component.definition.ComponentDefinition;
import com.sankuai.dz.api.module.arrange.framework.component.spi.BaseComponent;
import com.sankuai.dz.api.module.arrange.framework.component.storage.ComponentDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.utils.FieldsWithAnnotationResolver;
import com.sankuai.dz.api.module.arrange.framework.utils.GenericTypeResolver;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/3 11:52
 */
@Getter
@SuppressWarnings("rawtypes")
public abstract class BuilderDefinition {//所有属性只读

    /**
     * 模块key，全局唯一
     * 参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护
     */
    private final String moduleKey;

    /**
     * Builder的注解
     */
    private final Builder annotation;

    /**
     * builder的Class
     * 不能用作唯一标识，有可能ClassLoader不同，唯一标识只有moduleKey！！！
     */
    private final Class<? extends AbstractBuilder> builderClass;

    /**
     * Builder返回值类型，必须返回强类型
     */
    private final Class<? extends AbstractModuleVO> moduleVOClass;

    /**
     * 依赖DAG的起始fetcher
     */
    private final FetcherDefinition startFetcherDefinition;

    /**
     * 依赖的所有fetcher名称
     */
    private final Set<String> dependentFetcherNames;//已转换为只读

    public abstract BuilderTypeEnum getBuilderType();

    /**
     * 获取运行时依赖的Fetcher
     */
    public Set<String> getRuntimeDependentFetcherNames(final ProductDetailPageRequest request) {
        return dependentFetcherNames;
    }

    public BuilderDefinition(final Class<? extends AbstractBuilder> builderClass) {
        if (builderClass == null) {
            throw new BuilderFatalError("初始化Builder的入参为空");
        }
        Builder annotation = builderClass.getAnnotation(Builder.class);
        if (annotation == null) {
            throw new BuilderFatalError(String.format("非法Builder定义!!!该Builder(%s)没有标注注解", builderClass.getName()));
        }
        if (annotation.builderType() != getBuilderType()) {
            throw new BuilderFatalError(String.format("非法Builder定义!!!该Builder(%s)注解上的BuilderType与类静态定义不符", builderClass.getName()));
        }
        if (StringUtils.isBlank(annotation.moduleKey())) {
            throw new BuilderFatalError("非法Builder定义!!!该Builder(%s)的moduleKey为空");
        }
        this.moduleKey = annotation.moduleKey();
        this.annotation = annotation;
        this.builderClass = builderClass;
        this.moduleVOClass = parseBuilderModuleVOClass(builderClass);
        this.startFetcherDefinition = buildStartFetcherName();
        this.dependentFetcherNames = buildDependentFetchers();
    }

    @SuppressWarnings("unchecked")
    private static Class<? extends AbstractModuleVO> parseBuilderModuleVOClass(Class<? extends AbstractBuilder> builderClass) {
        Class<?> builderModuleVOClass = GenericTypeResolver.getGenericActualType(
                builderClass, AbstractBuilder.class, 0
        );
        if (!AbstractModuleVO.class.isAssignableFrom(builderModuleVOClass)) {
            throw new BuilderFatalError("FATAL ERROR!!!BaseBuilder的第一个泛型不是moduleVO类型了!!!");
        }
        return (Class<? extends AbstractModuleVO>) builderModuleVOClass;
    }

    private FetcherDefinition buildStartFetcherName() {
        Class<? extends BaseFetcherContext> startFetcherClass = this.annotation.startFetcher();
        if (startFetcherClass == null) {
            throw new BuilderFatalError("builder没有标注startFetcher");
        }
        return FetcherDefinitionStorage.getFetcherDefinition(startFetcherClass);
    }

    private Set<String> buildDependentFetchers() {
        final Set<String> dependentFetchers = new HashSet<>();
        //先解析builder自身
        final DAG dag = FetcherDAGStorage.getDAG(this.startFetcherDefinition.getFetcherName());
        for (Class<? extends BaseFetcherContext> fetcherClass : this.annotation.dependentFetchers()) {
            addDependentFetchers(FetcherDefinition.getFetcherName(fetcherClass), dag, dependentFetchers);
        }
        //循环解析父类
        Class<?> currentClass = builderClass.getSuperclass();
        while (currentClass != null) {
            if (!AbstractBuilder.class.isAssignableFrom(currentClass)) {
                break;
            }
            Builder parentAnnotation = currentClass.getAnnotation(Builder.class);
            if (parentAnnotation != null) {
                if (!this.annotation.moduleKey().equals(parentAnnotation.moduleKey())
                        || this.annotation.startFetcher() != parentAnnotation.startFetcher()) {
                    throw new BuilderFatalError(String.format("该Builder的父类(%s)的moduleKey与startFetcher与子类不相同!!!", currentClass.getName()));
                }
                for (Class<? extends BaseFetcherContext> fetcherClass : parentAnnotation.dependentFetchers()) {
                    addDependentFetchers(FetcherDefinition.getFetcherName(fetcherClass), dag, dependentFetchers);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        //获取被标记了@BizComponent的组件类的依赖
        List<Class<? extends BaseComponent>> fields = FieldsWithAnnotationResolver.getFields(
                this.builderClass, BizComponent.class, BaseComponent.class
        );
        for (Class<? extends BaseComponent> field : fields) {
            ComponentDefinition componentDefinition = ComponentDefinitionStorage.componentStorageOfModuleClass.get(field);
            if (componentDefinition == null) {
                throw new BuilderFatalError(String.format("获取不到ComponentDefinition(%s)", field.getName()));
            }
            for (String fetcherName : componentDefinition.getDependentFetcherNames()) {
                addDependentFetchers(fetcherName, dag, dependentFetchers);
            }
        }
        //转化为不可编辑的集合返回
        return Collections.unmodifiableSet(dependentFetchers);
    }

    private void addDependentFetchers(final String fetcherName, final DAG dag, final Set<String> dependentFetchers) {
        if (!dag.contains(fetcherName)) {
            throw new BuilderFatalError(String.format("该Builder(%s)依赖的Fetcher(%s)不在该Builder所属的DAG中", moduleKey, fetcherName));
        }
        dependentFetchers.add(fetcherName);
    }

}
