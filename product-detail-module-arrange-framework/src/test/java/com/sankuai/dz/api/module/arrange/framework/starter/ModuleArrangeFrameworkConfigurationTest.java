package com.sankuai.dz.api.module.arrange.framework.starter;

import com.sankuai.dz.product.detail.page.low.code.starter.LowCodeBeanConfiguration;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.lang.reflect.Method;

import static org.junit.Assert.*;

/**
 * ModuleArrangeFrameworkConfiguration 单元测试
 * 确保100%的代码覆盖率
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 14:00
 */
@RunWith(MockitoJUnitRunner.class)
public class ModuleArrangeFrameworkConfigurationTest {

    @InjectMocks
    private ModuleArrangeFrameworkConfiguration configuration;

    /**
     * 测试类的注解配置
     */
    @Test
    public void testClassAnnotations() {
        Class<ModuleArrangeFrameworkConfiguration> clazz = ModuleArrangeFrameworkConfiguration.class;
        
        // 验证 @Configuration 注解
        assertTrue("类应该有 @Configuration 注解", 
                clazz.isAnnotationPresent(Configuration.class));
        
        // 验证 @AutoConfigureAfter 注解
        assertTrue("类应该有 @AutoConfigureAfter 注解", 
                clazz.isAnnotationPresent(AutoConfigureAfter.class));
        
        AutoConfigureAfter autoConfigureAfter = clazz.getAnnotation(AutoConfigureAfter.class);
        Class<?>[] afterClasses = autoConfigureAfter.value();
        assertEquals("@AutoConfigureAfter 应该指定一个类", 1, afterClasses.length);
        assertEquals("@AutoConfigureAfter 应该指定 LowCodeBeanConfiguration", 
                LowCodeBeanConfiguration.class, afterClasses[0]);
    }

    /**
     * 测试 ModuleArrangeFrameworkStarter Bean 方法
     */
    @Test
    public void testModuleArrangeFrameworkStarterBean() {
        // 调用 Bean 方法
        ModuleArrangeFrameworkStarter starter = configuration.ModuleArrangeFrameworkStarter();
        
        // 验证返回值不为空
        assertNotNull("ModuleArrangeFrameworkStarter 不应该为空", starter);
        
        // 验证返回的是正确的类型
        assertTrue("返回值应该是 ModuleArrangeFrameworkStarter 类型", 
                starter instanceof ModuleArrangeFrameworkStarter);
        
        // 验证每次调用都返回新的实例（因为没有使用 @Singleton）
        ModuleArrangeFrameworkStarter starter2 = configuration.ModuleArrangeFrameworkStarter();
        assertNotSame("每次调用应该返回新的实例", starter, starter2);
    }

    /**
     * 测试 Bean 方法的注解配置
     */
    @Test
    public void testBeanMethodAnnotations() throws NoSuchMethodException {
        Method method = ModuleArrangeFrameworkConfiguration.class
                .getMethod("ModuleArrangeFrameworkStarter");
        
        // 验证 @Bean 注解
        assertTrue("方法应该有 @Bean 注解", 
                method.isAnnotationPresent(Bean.class));
        
        // 验证 @DependsOn 注解
        assertTrue("方法应该有 @DependsOn 注解", 
                method.isAnnotationPresent(DependsOn.class));
        
        DependsOn dependsOn = method.getAnnotation(DependsOn.class);
        String[] dependencies = dependsOn.value();
        assertEquals("@DependsOn 应该指定一个依赖", 1, dependencies.length);
        assertEquals("@DependsOn 应该指定 lowCodeStarter", 
                "lowCodeStarter", dependencies[0]);
    }

    /**
     * 测试方法的返回类型
     */
    @Test
    public void testBeanMethodReturnType() throws NoSuchMethodException {
        Method method = ModuleArrangeFrameworkConfiguration.class
                .getMethod("ModuleArrangeFrameworkStarter");
        
        // 验证返回类型
        assertEquals("方法返回类型应该是 ModuleArrangeFrameworkStarter", 
                ModuleArrangeFrameworkStarter.class, method.getReturnType());
    }

    /**
     * 测试方法的访问修饰符
     */
    @Test
    public void testBeanMethodModifiers() throws NoSuchMethodException {
        Method method = ModuleArrangeFrameworkConfiguration.class
                .getMethod("ModuleArrangeFrameworkStarter");
        
        // 验证方法是 public 的
        assertTrue("Bean 方法应该是 public 的", 
                java.lang.reflect.Modifier.isPublic(method.getModifiers()));
        
        // 验证方法不是 static 的
        assertFalse("Bean 方法不应该是 static 的", 
                java.lang.reflect.Modifier.isStatic(method.getModifiers()));
        
        // 验证方法不是 final 的
        assertFalse("Bean 方法不应该是 final 的", 
                java.lang.reflect.Modifier.isFinal(method.getModifiers()));
    }

    /**
     * 测试类的构造函数
     */
    @Test
    public void testConstructor() {
        // 验证可以创建实例
        ModuleArrangeFrameworkConfiguration config = new ModuleArrangeFrameworkConfiguration();
        assertNotNull("应该能够创建配置类实例", config);
    }

    /**
     * 测试类的包名和类名
     */
    @Test
    public void testClassMetadata() {
        Class<ModuleArrangeFrameworkConfiguration> clazz = ModuleArrangeFrameworkConfiguration.class;
        
        // 验证包名
        assertEquals("包名应该正确", 
                "com.sankuai.dz.api.module.arrange.framework.starter", 
                clazz.getPackage().getName());
        
        // 验证类名
        assertEquals("类名应该正确", 
                "ModuleArrangeFrameworkConfiguration", 
                clazz.getSimpleName());
    }

    /**
     * 测试类的继承关系
     */
    @Test
    public void testClassHierarchy() {
        Class<ModuleArrangeFrameworkConfiguration> clazz = ModuleArrangeFrameworkConfiguration.class;
        
        // 验证父类是 Object
        assertEquals("父类应该是 Object", 
                Object.class, clazz.getSuperclass());
        
        // 验证没有实现接口
        assertEquals("不应该实现任何接口", 
                0, clazz.getInterfaces().length);
    }

    /**
     * 测试多次调用 Bean 方法的一致性
     */
    @Test
    public void testMultipleBeanMethodCalls() {
        ModuleArrangeFrameworkConfiguration config = new ModuleArrangeFrameworkConfiguration();
        
        // 多次调用 Bean 方法
        ModuleArrangeFrameworkStarter starter1 = config.ModuleArrangeFrameworkStarter();
        ModuleArrangeFrameworkStarter starter2 = config.ModuleArrangeFrameworkStarter();
        ModuleArrangeFrameworkStarter starter3 = config.ModuleArrangeFrameworkStarter();
        
        // 验证每次都返回新实例
        assertNotNull("第一次调用应该返回非空实例", starter1);
        assertNotNull("第二次调用应该返回非空实例", starter2);
        assertNotNull("第三次调用应该返回非空实例", starter3);
        
        assertNotSame("第一次和第二次调用应该返回不同实例", starter1, starter2);
        assertNotSame("第二次和第三次调用应该返回不同实例", starter2, starter3);
        assertNotSame("第一次和第三次调用应该返回不同实例", starter1, starter3);
        
        // 验证所有实例都是相同类型
        assertEquals("所有实例应该是相同类型", 
                starter1.getClass(), starter2.getClass());
        assertEquals("所有实例应该是相同类型", 
                starter2.getClass(), starter3.getClass());
    }
}
