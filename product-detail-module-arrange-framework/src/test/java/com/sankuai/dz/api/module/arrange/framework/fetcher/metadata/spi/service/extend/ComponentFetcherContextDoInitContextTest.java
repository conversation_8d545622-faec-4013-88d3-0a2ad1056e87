package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.ComponentFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ComponentFetcherContextDoInitContextTest {

    @Mock
    private FetcherEngine fetcherEngine;

    @Mock
    private ComponentFetcherDefinition mockFetcherDefinition;

    private TestComponentFetcherContext componentFetcherContext;

    @BeforeEach
    void setUp() throws Exception {
        componentFetcherContext = new TestComponentFetcherContext();
        // Use reflection to set protected field
        setFetcherDefinition(componentFetcherContext, mockFetcherDefinition);
    }

    /**
     * Helper method to set protected fetcherDefinition field using reflection
     */
    private void setFetcherDefinition(TestComponentFetcherContext context, ComponentFetcherDefinition definition) throws Exception {
        Field field = context.getClass().getSuperclass().getSuperclass().getDeclaredField("fetcherDefinition");
        field.setAccessible(true);
        field.set(context, definition);
    }

    /**
     * Test case to verify doInitContext returns valid FetcherContextInitResult
     * with proper context reference
     */
    @Test
    void testDoInitContextReturnsValidResult() throws Throwable {
        // arrange
        // act
        FetcherContextInitResult result = componentFetcherContext.doInitContext(fetcherEngine);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(componentFetcherContext, result.getSelf(), "Result should contain reference to the context");
        assertNull(result.getOthers(), "Others list should be null as not initialized");
    }

    /**
     * Test case to verify doInitContext maintains proper state
     */
    @Test
    void testDoInitContextMaintainsState() throws Throwable {
        // arrange
        // act
        FetcherContextInitResult result = componentFetcherContext.doInitContext(fetcherEngine);
        // assert
        assertEquals(mockFetcherDefinition, componentFetcherContext.getFetcherDefinition(), "FetcherDefinition reference should be maintained");
        assertNotNull(result, "Result should not be null");
    }

    /**
     * Test case to verify doInitContext with null engine parameter
     */
    @Test
    void testDoInitContextWithNullEngine() throws Throwable {
        // act
        FetcherContextInitResult result = componentFetcherContext.doInitContext(null);
        // assert
        assertNotNull(result, "Result should not be null even with null engine");
        assertEquals(componentFetcherContext, result.getSelf(), "Result should contain reference to the context even with null engine");
    }

    // Test implementation class
    private static class TestComponentFetcherContext extends ComponentFetcherContext<Object, TestReturnValue, TestReturnValue> {

        @Override
        public void fulfillRequest(Object request) {
            // Not needed for testing doInitContext
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            // Not needed for testing doInitContext
            return null;
        }

        @Override
        protected FetcherResponse<TestReturnValue> mapResult(FetcherResponse<TestReturnValue> aggregateResult) {
            // Not needed for testing doInitContext
            return null;
        }
    }

    // Test DTO implementation
    private static class TestReturnValue extends FetcherReturnValueDTO {
    }
}
