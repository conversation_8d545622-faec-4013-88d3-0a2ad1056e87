package com.sankuai.dz.product.detail.page.config.management;

import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigTemplateDTO;

import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/1/17 14:38
 */
public interface ProductDetailPageConfigRpcService {

    /**
     * 查询页面配置模板，用于计算页面配置
     */
    PageConfigTemplateDTO queryConfigTemplate(PageConfigRoutingKey routingKey);

    /**
     * 查询计算好的页面配置
     */
    PageConfigDataDTO getPageConfig(PageConfigRoutingKey routingKey);

    /**
     * 获取当前机器中所有配置的key
     */
    Set<String> getAllPageRoutingKey();

}
