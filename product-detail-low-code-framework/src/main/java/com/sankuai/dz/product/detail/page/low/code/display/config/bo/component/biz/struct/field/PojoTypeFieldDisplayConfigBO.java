package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field;

import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/5/30 16:00
 */
@Getter
public class PojoTypeFieldDisplayConfigBO extends FieldDisplayConfigBO {

    /**
     * 组件数据模型，内部递归实现Pojo嵌套，其元数据来源于代码定义（被@BizComponent注解标记的类）
     */
    private final List<FieldDisplayConfigBO> fieldConfigs;

    public PojoTypeFieldDisplayConfigBO(final String fieldName,
                                        final boolean collection,
                                        final List<FieldDisplayConfigBO> fieldConfigs) {
        super(fieldName, collection);
        this.fieldConfigs = Collections.unmodifiableList(fieldConfigs);
    }

    @Override
    public boolean isPojo() {
        return true;
    }

}
