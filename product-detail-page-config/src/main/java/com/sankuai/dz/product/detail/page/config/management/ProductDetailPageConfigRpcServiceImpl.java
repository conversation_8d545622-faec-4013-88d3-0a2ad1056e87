package com.sankuai.dz.product.detail.page.config.management;

import com.dianping.lion.Environment;
import com.dianping.pigeon.remoting.ServiceFactory;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.page.config.page.config.ProductDetailPageConfigService;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigTemplateDTO;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/1/17 14:20
 */
@Component
public class ProductDetailPageConfigRpcServiceImpl implements ProductDetailPageConfigRpcService, InitializingBean {

    @Resource
    private ProductDetailPageConfigService productDetailPageConfigService;

    @Override
    public PageConfigTemplateDTO queryConfigTemplate(PageConfigRoutingKey routingKey) {
        return productDetailPageConfigService.getPageConfigTemplate(routingKey);
    }

    @Override
    public PageConfigDataDTO getPageConfig(PageConfigRoutingKey routingKey) {
        return productDetailPageConfigService.getPageConfig(routingKey);
    }

    @Override
    public Set<String> getAllPageRoutingKey() {
        return productDetailPageConfigService.getAllPageConfigTemplateCacheKeys();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ServiceFactory.addService(
                "com.sankuai.digital.arch." + Environment.getAppName() + "ProductDetailPageConfigRpcService",
                ProductDetailPageConfigRpcService.class,
                this
        );
    }

}
