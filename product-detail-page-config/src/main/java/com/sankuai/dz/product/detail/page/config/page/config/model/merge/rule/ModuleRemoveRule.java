package com.sankuai.dz.product.detail.page.config.page.config.model.merge.rule;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.page.config.enums.ConfigMergeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 15:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "模块删除策略")
public class ModuleRemoveRule extends AbstractMergeRule {

    @FieldDoc(description = "指定groupName", requiredness = Requiredness.OPTIONAL)
    private String groupName;

    @FieldDoc(description = "模块key", requiredness = Requiredness.REQUIRED,
            typeName = "参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护")
    private String removeModuleKey;

    @Override
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_REMOVE;
    }
}
