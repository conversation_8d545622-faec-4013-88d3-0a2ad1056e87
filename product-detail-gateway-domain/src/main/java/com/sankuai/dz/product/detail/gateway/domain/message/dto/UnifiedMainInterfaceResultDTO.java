package com.sankuai.dz.product.detail.gateway.domain.message.dto;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/25
 */
@Data
public class UnifiedMainInterfaceResultDTO implements Serializable {
    /**
     * 前端请求入参
     */
    private UnifiedPageRequest unifiedRequest;

    /**
     * SPI调用结果
     */
    private List<GenericProductDetailPageResponse> spiResponseList;

    /**
     * SPI调用入参
     */
    private ProductDetailPageRequest spiRequest;
}
