package com.sankuai.dz.api.module.arrange.framework.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

/**
 * @Author: guangyujie
 * @Date: 2025/3/6 22:36
 */
@Slf4j
public class MethodOverrideChecker {

    /**
     * 判断子类是否重写了父类的指定方法
     *
     * @param parentClass    父类
     * @param childClass     子类
     * @param methodName     方法名
     * @param parameterTypes 方法参数类型
     * @return true表示重写，false表示未重写
     */
    public static boolean isMethodOverridden(Class<?> parentClass, Class<?> childClass, String methodName, Class<?>... parameterTypes) {
        try {
            // 获取父类方法
            Method parentMethod = parentClass.getDeclaredMethod(methodName, parameterTypes);
            // 获取子类方法
            Method childMethod = childClass.getDeclaredMethod(methodName, parameterTypes);

            // 判断方法的声明类是否为子类
            return !childMethod.getDeclaringClass().equals(parentMethod.getDeclaringClass());
        } catch (NoSuchMethodException e) {
            log.error("MethodOverrideChecker error, parentClass={}, childClass={}, methodName={}", parentClass, childClass, methodName, e);
            // 如果子类或父类没有该方法，返回false
            return false;
        }
    }

}