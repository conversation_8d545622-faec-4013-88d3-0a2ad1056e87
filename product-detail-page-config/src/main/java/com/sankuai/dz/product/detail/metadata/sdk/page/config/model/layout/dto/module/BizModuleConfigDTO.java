package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 20:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "业务模块，需要请求服务端获取数据")
public abstract class BizModuleConfigDTO extends ModuleConfigDTO {

    @FieldDoc(description = "是否首屏模块", rule = "1首屏，0非首屏 @see ScreenFlagEnum", requiredness = Requiredness.REQUIRED)
    private int firstScreenFlag;

    @FieldDoc(description = "服务别名，通过SPIConfigService可以获取该模块对应的SPI信息", requiredness = Requiredness.REQUIRED)
    private String interfaceAlias;

}
