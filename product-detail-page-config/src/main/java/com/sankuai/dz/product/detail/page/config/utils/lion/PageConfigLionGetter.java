package com.sankuai.dz.product.detail.page.config.utils.lion;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.fileconfig.FileConfigClient;
import com.sankuai.dz.product.detail.page.config.error.LayoutConfigFatalException;
import com.sankuai.dz.product.detail.page.config.utils.json.JacksonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

import static com.sankuai.dz.product.detail.page.config.utils.lion.PageConfigLionConstant.APP_KEY;

/**
 * @Author: guangyujie
 * @Date: 2024/7/10 16:30
 */
@Slf4j
public class PageConfigLionGetter<T> {

    private final Class<T> tClass;

    public PageConfigLionGetter(Class<T> tClass) {
        this.tClass = tClass;
    }

    private static final FileConfigClient FILE_CONFIG_CLIENT = Lion.createFileConfigClient(APP_KEY);

    private final Map<String, T> dataCache = new HashMap<>();

    private final Map<String, ConfigListener> configListenerMap = new HashMap<>();

    public Set<String> getAllKeysFromDataCache() {
        return dataCache.keySet();
    }

    public Set<String> getAllKeysFromListener() {
        return configListenerMap.keySet();
    }

    public T get(String key) {
        return dataCache.get(key);
    }

    /**
     * 如果已经初始化了就不重新初始化
     */
    public synchronized void initSingleKey(final String key, final Consumer<String> customListener) {
        if (configListenerMap.containsKey(key)) {
            return;
        }
        forceInitSingleKey(key, customListener);
    }

    /**
     * 强制更新
     */
    public synchronized void forceInitSingleKey(final String key, final Consumer<String> customListener) {
        try {
            if (StringUtils.isBlank(key)) {
                throw new LayoutConfigFatalException("key can't be blank");
            }
            //创建Listener
            ConfigListener configListener = configEvent -> {
                String value = configEvent.getValue();
                buildCache(key, value);
                if (customListener != null) {
                    customListener.accept(key);
                }
            };
            if (configListenerMap.containsKey(key)) {
                FILE_CONFIG_CLIENT.removeListener(key, configListenerMap.get(key));
                configListenerMap.remove(key);
            }
            FILE_CONFIG_CLIENT.addListener(key, configListener);
            configListenerMap.put(key, configListener);
            //反序列化json，存入本地缓存
            String fileContent = FILE_CONFIG_CLIENT.getFileContent(key);
            buildCache(key, fileContent);
        } catch (Throwable throwable) {
            String className = this.getClass().getSimpleName();
            Cat.logError(className, new LayoutConfigFatalException(throwable));
            log.error("{}<{}> forceInitSingleKey 失败", className, key, throwable);
            throw throwable;
        }
    }

    private void buildCache(final String key, final String json) {
        T data = JacksonUtils.deserialize(json, tClass);
        if (data != null) {
            dataCache.put(key, data);
        }
    }

}
