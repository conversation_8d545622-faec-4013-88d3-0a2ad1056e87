package com.sankuai.dz.product.detail.gateway.domain.data.merge.dto;

import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.page.config.error.DataMergeFatalException;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class DataMergeRequestTest {

    /**
     * Test when all fields are non-null - should pass without exception
     */
    @Test
    public void testCheckParamAllFieldsValid() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        PageConfigDataDTO mockPageConfigData = mock(PageConfigDataDTO.class);
        ABResultDTO mockAbResult = mock(ABResultDTO.class);
        Map<String, GenericModuleResponse> mockModuleResponse = new HashMap<>();
        List<ABResultDTO> abResultList = Collections.singletonList(mockAbResult);
        request.setSpiRequest(mockRequest);
        request.setPageConfigData(mockPageConfigData);
        request.setAbResultList(abResultList);
        request.setModuleResponse(mockModuleResponse);
        // act
        request.checkParam();
        // assert
        assertSame("Request should remain the same", mockRequest, request.getSpiRequest());
        assertSame("PageConfigData should remain the same", mockPageConfigData, request.getPageConfigData());
        assertEquals("ABResultList should remain the same", abResultList, request.getAbResultList());
        assertSame("ModuleResponse should remain the same", mockModuleResponse, request.getModuleResponse());
    }

    /**
     * Test when request field is null - should throw DataMergeFatalException
     */
    @Test(expected = DataMergeFatalException.class)
    public void testCheckParamRequestNull() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(null);
        request.setPageConfigData(mock(PageConfigDataDTO.class));
        request.setAbResultList(Collections.singletonList(mock(ABResultDTO.class)));
        request.setModuleResponse(new HashMap<>());
        // act
        request.checkParam();
        // assert
        // exception expected
    }

    /**
     * Test when pageConfigData field is null - should throw DataMergeFatalException
     */
    @Test(expected = DataMergeFatalException.class)
    public void testCheckParamPageConfigDataNull() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(mock(ProductDetailPageRequest.class));
        request.setPageConfigData(null);
        request.setAbResultList(Collections.singletonList(mock(ABResultDTO.class)));
        request.setModuleResponse(new HashMap<>());
        // act
        request.checkParam();
        // assert
        // exception expected
    }

    /**
     * Test when abResultList field is null - should throw DataMergeFatalException
     */
    @Test(expected = DataMergeFatalException.class)
    public void testCheckParamAbResultListNull() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(mock(ProductDetailPageRequest.class));
        request.setPageConfigData(mock(PageConfigDataDTO.class));
        request.setAbResultList(null);
        request.setModuleResponse(new HashMap<>());
        // act
        request.checkParam();
        // assert
        // exception expected
    }

    /**
     * Test when moduleResponse field is null - should throw DataMergeFatalException
     */
    @Test(expected = DataMergeFatalException.class)
    public void testCheckParamModuleResponseNull() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(mock(ProductDetailPageRequest.class));
        request.setPageConfigData(mock(PageConfigDataDTO.class));
        request.setAbResultList(Collections.singletonList(mock(ABResultDTO.class)));
        request.setModuleResponse(null);
        // act
        request.checkParam();
        // assert
        // exception expected
    }

    /**
     * Test when multiple fields are null - should throw DataMergeFatalException on first null check
     */
    @Test(expected = DataMergeFatalException.class)
    public void testCheckParamMultipleFieldsNull() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(null);
        request.setPageConfigData(null);
        request.setAbResultList(null);
        request.setModuleResponse(null);
        // act
        request.checkParam();
        // assert
        // exception expected
    }

    /**
     * Test exception message when fields are null
     */
    @Test
    public void testCheckParamExceptionMessage() throws Throwable {
        // arrange
        DataMergeRequest request = new DataMergeRequest();
        request.setSpiRequest(null);
        request.setPageConfigData(mock(PageConfigDataDTO.class));
        request.setAbResultList(Collections.singletonList(mock(ABResultDTO.class)));
        request.setModuleResponse(new HashMap<>());
        try {
            // act
            request.checkParam();
            fail("Expected DataMergeFatalException");
        } catch (DataMergeFatalException e) {
            // assert
            assertEquals("merge方法入参异常", e.getMessage());
        }
    }
}
