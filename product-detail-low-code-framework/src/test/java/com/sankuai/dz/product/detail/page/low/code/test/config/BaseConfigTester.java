package com.sankuai.dz.product.detail.page.low.code.test.config;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.coding.AICodingComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.StructComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.BaseTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.FieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.card.CardContainerComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.version.ProductVersionConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/10 22:01
 */
public class BaseConfigTester {

    protected PageDisplayConfigBO buildPageConfig(final ModuleDisplayConfigBO... moduleDisplayConfigBOS) {
        return new PageDisplayConfigBO(
                Arrays.stream(moduleDisplayConfigBOS)
                        .collect(Collectors.groupingBy(ModuleDisplayConfigBO::getModuleKey))
        );
    }

    protected ModuleDisplayConfigBO buildStructuredDetailConfig(final ProductVersionConfigBO productVersionConfigBO,
                                                                final ComponentDisplayConfigBO... componentConfigs) {
        return new ModuleDisplayConfigBO(
                "module_detail_structured_detail",
                productVersionConfigBO,
                Arrays.asList(componentConfigs)
        );
    }

    protected CardContainerComponentDisplayConfigBO buildCardContainer(final BizComponentDisplayConfigBO... bizComponentConfig) {
        return new CardContainerComponentDisplayConfigBO(
                null,
                Arrays.asList(bizComponentConfig)
        );
    }

    protected StructComponentDisplayConfigBO buildStructBizComponent(final String componentKey,
                                                                     final List<FieldDisplayConfigBO> fieldConfigs,
                                                                     final DataSourceConfigBO... dataSources) {
        return new StructComponentDisplayConfigBO(
                componentKey,
                Arrays.stream(dataSources)
                        .collect(Collectors.toMap(DataSourceConfigBO::getFetcherName, ds -> ds)),
                fieldConfigs
        );
    }

    protected AICodingComponentDisplayConfigBO buildAICodingComponentDisplayConfigBO(String componentKey, final RuleConfigDTO ruleConfig,
                                                                                     final DataSourceConfigBO... dataSources ) {
        return new AICodingComponentDisplayConfigBO(
                componentKey,
                Arrays.stream(dataSources)
                        .collect(Collectors.toMap(DataSourceConfigBO::getFetcherName, ds -> ds)),
                ruleConfig
        );
    }

    protected BaseTypeFieldDisplayConfigBO buildBaseTypeFieldDisplayConfigBO(String fieldName, String dsl) {
        return new BaseTypeFieldDisplayConfigBO(
                fieldName, false, null, new RuleConfigDTO(RuleEngineType.AVIATOR, dsl)
        );
    }

    protected BaseTypeFieldDisplayConfigBO buildBaseTypeField(String fieldName, String dsl) {
        return new BaseTypeFieldDisplayConfigBO(
                fieldName, false, null, new RuleConfigDTO(RuleEngineType.AVIATOR, dsl)
        );
    }

    protected BaseTypeFieldDisplayConfigBO buildBaseTypeFieldByGroovy(String fieldName, String dsl) {
        return new BaseTypeFieldDisplayConfigBO(
                fieldName, false, null, new RuleConfigDTO(RuleEngineType.GROOVY, dsl)
        );
    }
    protected AICodingComponentDisplayConfigBO buildAICodingComponent(final String componentKey,
                                                                      final String dsl,
                                                                      final DataSourceConfigBO... dataSources) {
        return new AICodingComponentDisplayConfigBO(
                componentKey,
                Arrays.stream(dataSources)
                        .collect(Collectors.toMap(DataSourceConfigBO::getFetcherName, ds -> ds)),
                new RuleConfigDTO(RuleEngineType.AVIATOR, dsl)
        );
    }

    protected AICodingComponentDisplayConfigBO buildAICodingComponentGroovy(final String componentKey,
                                                                      final String dsl,
                                                                      final DataSourceConfigBO... dataSources) {
        return new AICodingComponentDisplayConfigBO(
                componentKey,
                Arrays.stream(dataSources)
                        .collect(Collectors.toMap(DataSourceConfigBO::getFetcherName, ds -> ds)),
                new RuleConfigDTO(RuleEngineType.GROOVY, dsl)
        );
    }

}
