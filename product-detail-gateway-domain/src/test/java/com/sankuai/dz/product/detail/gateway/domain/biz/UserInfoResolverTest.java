package com.sankuai.dz.product.detail.gateway.domain.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.account.UserAccountService;
import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserInfo;
import com.sankuai.dz.product.detail.gateway.domain.utils.HeaderUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.ThreadPoolUtils;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.FlattedBindRelationAggregateResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.thrift.TException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import java.util.concurrent.ExecutionException;
import org.apache.commons.lang3.StringUtils;

@RunWith(MockitoJUnitRunner.class)
public class UserInfoResolverTest {

    @InjectMocks
    private UserInfoResolver userInfoResolver;

    @Mock
    private AccountValidationService accountValidationService;

    @Mock
    private UserAccountService userAccountService;

    @Mock
    private UserMergeQueryService.Iface userMergeQueryService;

    private Map<String, String> headerMap;

    @Before
    public void setUp() {
        headerMap = new HashMap<>();
    }

    /**
     * 测试 headerMap 中 token 为空的情况
     */
    @Test
    public void testGetUserIdTokenIsNull() throws Throwable {
        // arrange
        headerMap.put("token", "");
        // act
        CompletableFuture<Long> result = userInfoResolver.getUserId(true, headerMap);
        // assert
        assertEquals(Long.valueOf(0L), result.get());
    }

    /**
     * 测试 headerMap 中 token 不为空，isDp 为 true 的情况
     */
    @Test
    public void testGetUserIdIsDpTrue() throws Throwable {
        // arrange
        headerMap.put("token", "dpToken");
        AccountValidationResult validationResult = new AccountValidationResult(true, 1L, 0);
        when(accountValidationService.validateDper(anyString())).thenReturn(validationResult);
        // act
        CompletableFuture<Long> result = userInfoResolver.getUserId(true, headerMap);
        // assert
        assertEquals(Long.valueOf(1L), result.get());
    }

    /**
     * 测试 headerMap 中 token 不为空，isDp 为 false 的情况
     */
    @Test
    public void testGetUserIdIsDpFalse() throws Throwable {
        // arrange
        headerMap.put("token", "mtToken");
        // act
        CompletableFuture<Long> result = userInfoResolver.getUserId(false, headerMap);
        // assert
        assertEquals(Long.valueOf(0), result.get());
    }

    @Test
    public void testGetUserInfo_EmptyToken() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        // act
        UserInfo result = userInfoResolver.getUserInfo(true, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpUserId());
        assertEquals(0L, result.getMtUserId());
        verifyNoInteractions(accountValidationService, userAccountService, userMergeQueryService);
    }

    @Test
    public void testGetUserInfo_InvalidDpToken() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "invalid-token");
        when(accountValidationService.validateDper("invalid-token")).thenReturn(new AccountValidationResult(false, 0L, 1));
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = false;
        when(userMergeQueryService.getFlattedBindAggregateByDpUserId(0L)).thenReturn(resp);
        // act
        UserInfo result = userInfoResolver.getUserInfo(true, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpUserId());
        assertEquals(0L, result.getMtUserId());
    }

    @Test
    public void testGetUserInfo_InvalidMtToken() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "invalid-token");
        headers.put("X-Real-IP", "*******");
        when(userAccountService.loadUserByToken(eq("invalid-token"), eq("*******"), any())).thenReturn(null);
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = false;
        when(userMergeQueryService.getFlattedBindAggregateByMtUserId(0L)).thenReturn(resp);
        // act
        UserInfo result = userInfoResolver.getUserInfo(false, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpUserId());
        assertEquals(0L, result.getMtUserId());
    }

    @Test
    public void testGetUserInfo_DpServiceException() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "dp-token");
        when(accountValidationService.validateDper("dp-token")).thenThrow(new RuntimeException("Service error"));
        // act
        UserInfo result = userInfoResolver.getUserInfo(true, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpUserId());
        assertEquals(0L, result.getMtUserId());
    }

    @Test
    public void testGetUserInfo_BindServiceException() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "dp-token");
        when(accountValidationService.validateDper("dp-token")).thenReturn(new AccountValidationResult(true, 123L, 0));
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = false;
        when(userMergeQueryService.getFlattedBindAggregateByDpUserId(123L)).thenReturn(resp);
        // act
        UserInfo result = userInfoResolver.getUserInfo(true, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpUserId());
        assertEquals(0L, result.getMtUserId());
    }

    @Test
    public void testGetUserInfo_TokenPriority() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("pragma-newtoken", "new-token");
        headers.put("pragma-token", "app-token");
        headers.put("token", "normal-token");
        when(accountValidationService.validateDper("new-token")).thenReturn(new AccountValidationResult(false, 0L, 1));
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = false;
        when(userMergeQueryService.getFlattedBindAggregateByDpUserId(0L)).thenReturn(resp);
        // act
        userInfoResolver.getUserInfo(true, headers, "query-token").get();
        // assert
        verify(accountValidationService).validateDper("new-token");
        verifyNoMoreInteractions(accountValidationService);
    }

    @Test
    public void testGetUserInfo_ValidDpToken() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "dp-token");
        headers.put("X-Real-IP", "*******");
        when(accountValidationService.validateDper("dp-token")).thenReturn(new AccountValidationResult(true, 123L, 0));
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = true;
        FlattedBindRelation bindRelation = new FlattedBindRelation();
        bindRelation.setTargetRealUserId(123L);
        bindRelation.setMtRealUserId(456L);
        resp.flattedAggregateData = bindRelation;
        when(userMergeQueryService.getFlattedBindAggregateByDpUserId(123L)).thenReturn(resp);
        // act
        UserInfo result = userInfoResolver.getUserInfo(true, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getDpUserId());
        assertEquals(456L, result.getMtUserId());
    }

    @Test
    public void testGetUserInfo_ValidMtToken() throws Throwable {
        // arrange
        Map<String, String> headers = new HashMap<>();
        headers.put("token", "mt-token");
        headers.put("X-Real-IP", "*******");
        VirtualBindUserInfoDTO mtUserInfo = new VirtualBindUserInfoDTO();
        mtUserInfo.setMtid(456L);
        when(userAccountService.loadUserByToken(eq("mt-token"), eq("*******"), any())).thenReturn(mtUserInfo);
        FlattedBindRelationAggregateResp resp = new FlattedBindRelationAggregateResp();
        resp.success = true;
        FlattedBindRelation bindRelation = new FlattedBindRelation();
        bindRelation.setTargetRealUserId(123L);
        bindRelation.setMtRealUserId(456L);
        resp.flattedAggregateData = bindRelation;
        when(userMergeQueryService.getFlattedBindAggregateByMtUserId(456L)).thenReturn(resp);
        // act
        UserInfo result = userInfoResolver.getUserInfo(false, headers, "").get();
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getDpUserId());
        assertEquals(456L, result.getMtUserId());
    }
}
