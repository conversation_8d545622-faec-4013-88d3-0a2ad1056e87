package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.tab;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.LayoutConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleConfigVO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 11:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "tab模块")
public class TabModuleConfigDTO extends BizModuleConfigDTO {

    @FieldDoc(description = "默认tab栏，如果服务端没有特殊处理，可直接返回这个数据", requiredness = Requiredness.REQUIRED)
    private List<TabModuleItemDTO> tabItemList;

    @Override
    @JsonIgnore
    public ModuleTypeEnum getModuleType() {
        return ModuleTypeEnum.tab;
    }

    @Override
    @JsonIgnore
    public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
        if (depth >= 10) {
            throw new LayoutConfigFatalException("超过模块最大嵌套层数，请检查配置");
        }
        List<ModuleConfigDTO> all = new ArrayList<>();
        all.add(this);
        List<ModuleConfigDTO> childModuleList = tabItemList.stream()
                .map(TabModuleItemDTO::getGroupConfig)
                .flatMap(Collection::stream)
                .map(ModuleGroupConfigDTO::getModuleList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .flatMap(config -> config.getAllModuleConfigs(depth + 1).stream())
                .collect(Collectors.toList());
        all.addAll(childModuleList);
        return all;
    }

    @Override
    @JsonIgnore
    public ModuleConfigVO buildVO(final BuildModuleVORequest request, final int depth) {
        if (depth >= 10) {
            throw new LayoutConfigFatalException("超过模块最大嵌套层数，请检查配置");
        }
        TabModuleConfigVO vo = new TabModuleConfigVO();
        vo.setTabConfigs(tabItemList.stream().map(dto -> dto.buildVO(request, depth + 1)).collect(Collectors.toList()));
        vo.setFirstScreenFlag(this.getFirstScreenFlag());
        vo.setModuleKey(this.getModuleKey());
        vo.setModuleType(this.getModuleType().name());
        return vo;
    }

}
