package com.sankuai.dz.product.detail.gateway.spi.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/1/10 15:10
 */
@Data
@TypeDoc(description = "模块基类")
public abstract class AbstractModuleVO implements Serializable {

    @FieldDoc(description = "模块key", requiredness = Requiredness.REQUIRED,
            typeName = "参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护")
    public abstract String getModuleKey();

}
