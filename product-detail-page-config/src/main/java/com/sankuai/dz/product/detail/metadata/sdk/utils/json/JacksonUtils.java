package com.sankuai.dz.product.detail.metadata.sdk.utils.json;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.type.TypeFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.fasterxml.jackson.databind.ObjectMapper.DefaultTyping.NON_FINAL;

/**
 * @Author: guangyujie
 * @Date: 2024/6/18 17:03
 */
public class JacksonUtils {


    private static final ObjectMapper mapper = new ObjectMapper();

    public static final String CLASS_KEY = "@class";

    public static final String BASE_VALUE_KEY = "\"value\"";
    public static final String BASE_VALUE_NODE_KEY = "value";

    static {
        SimpleModule module = new SimpleModule();
        mapper.enableDefaultTypingAsProperty(NON_FINAL, CLASS_KEY);
        module.setKeyDeserializers(new MapKeyDeserializers());
        module.addKeySerializer(Object.class, new MapKeySerializer());
        // 新增mapper.getFactory().disable(JsonFactory.Feature.INTERN_FIELD_NAMES)禁用String.intern方法，
        // 降低StringTable表膨胀对young gc耗时的影响
        mapper.getFactory().disable(JsonFactory.Feature.INTERN_FIELD_NAMES);
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(MapperFeature.USE_ANNOTATIONS, true);
        mapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        mapper.registerModule(module);
    }

    public static <T> T copyToOtherClass(Object t, Class<T> tClass) {
        return JSON.parseObject(JSON.toJSONString(t), tClass);
    }

    public static <T> T copy(T t, Class<T> tClass) {
        return deserialize(serialize(t), tClass);
    }


    public static String serialize(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String jsonString, Class<T> clazz) {
        try {
            if (jsonString == null) {
                return null;
            }
            return mapper.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T deserialize(String jsonString, JavaType type) {
        try {
            if (jsonString == null) {
                return null;
            }
            return mapper.readValue(jsonString, type);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static JsonNode readNode(String jsonString) {
        try {
            return mapper.readTree(jsonString);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static JsonNode valueToTree(Object o) {
        return mapper.valueToTree(o);
    }

    public static TypeFactory typeFactory() {
        return mapper.getTypeFactory();
    }

    public static Map<String, Double> jsonString2DoubleValMap(String jsonString) {
        Map<String, Double> doubleValMap = new HashMap<>();
        try {
            Map<String, String> map = mapper.readValue(jsonString, Map.class);
            for (Map.Entry<String, String> entry : map.entrySet()) {
                try {
                    doubleValMap.put(entry.getKey(), Double.valueOf(entry.getValue()));
                } catch (Exception e) {
                    // ignore.
                }
            }
        } catch (IOException e) {

        }
        return doubleValMap;
    }

}
