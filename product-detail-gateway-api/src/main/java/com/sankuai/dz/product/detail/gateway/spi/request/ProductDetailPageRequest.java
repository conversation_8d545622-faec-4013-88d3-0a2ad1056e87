package com.sankuai.dz.product.detail.gateway.spi.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.*;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;

/**
 * @Author: guangyujie
 * @Date: 2025/1/8 15:34
 */
@TypeDoc(description = "SPI接口请求入参")
public class ProductDetailPageRequest extends BaseRequest {

    @JsonIgnore
    public void checkParam() {
        if (shepherdGatewayParam == null) {
            throw new ProductDetailRequestIllegalException("shepherdGatewayParam is null");
        }
        if (productId <= 0) {
            throw new ProductDetailRequestIllegalException("productId is null");
        }
        if (!ProductTypeEnum.containsCode(productType)) {
            throw new ProductDetailRequestIllegalException("productType is Illegal");
        }
    }

    @JsonIgnore
    public String getCustomParam(RequestCustomParamEnum key) {
        if (customParam == null) {
            return null;
        }
        return customParam.getParam(key);
    }

    @JsonIgnore
    public long getDpUserId() {
        return shepherdGatewayParam.getDpUserId();
    }

    @JsonIgnore
    public long getMtUserId() {
        return shepherdGatewayParam.getMtUserId();
    }

    @JsonIgnore
    public long getUserId() {
        return this.getClientTypeEnum().isDpClientType() ? shepherdGatewayParam.getDpUserId() : shepherdGatewayParam.getMtUserId();
    }

    @JsonIgnore
    private MobileOSTypeEnum mobileOSType;

    @JsonIgnore
    public MobileOSTypeEnum getMobileOSType() {
        if (mobileOSType != null) {
            return mobileOSType;
        }
        mobileOSType = MobileOSTypeEnum.fromCode(shepherdGatewayParam.getMobileOSType());
        return mobileOSType;
    }

    @JsonIgnore
    private ClientTypeEnum clientTypeEnum;

    @JsonIgnore
    public ClientTypeEnum getClientTypeEnum() {
        if (clientTypeEnum != null) {
            return clientTypeEnum;
        }
        clientTypeEnum = ClientTypeEnum.fromCode(clientType);
        return clientTypeEnum;
    }

    @JsonIgnore
    private ProductTypeEnum productTypeEnum;

    @JsonIgnore
    public ProductTypeEnum getProductTypeEnum() {
        if (productTypeEnum != null) {
            return productTypeEnum;
        }
        productTypeEnum = ProductTypeEnum.fromCode(productType);
        return productTypeEnum;
    }

    @JsonIgnore
    private GpsCoordinateTypeEnum gpsCoordinateTypeEnum;

    @JsonIgnore
    public GpsCoordinateTypeEnum getGpsCoordinateTypeEnum() {
        if (gpsCoordinateTypeEnum != null) {
            return gpsCoordinateTypeEnum;
        }
        gpsCoordinateTypeEnum = GpsCoordinateTypeEnum.fromCode(gpsCoordinateType);
        return gpsCoordinateTypeEnum;
    }

    @JsonIgnore
    public PlatformEnum getPlatformEnum() {
        return getClientTypeEnum().getPlatform();
    }

}
