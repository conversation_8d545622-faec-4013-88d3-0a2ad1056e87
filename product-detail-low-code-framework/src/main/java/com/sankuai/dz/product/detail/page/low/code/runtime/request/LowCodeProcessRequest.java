package com.sankuai.dz.product.detail.page.low.code.runtime.request;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/5/27 14:35
 */
@Data
public class LowCodeProcessRequest {

    private List<ComponentDisplayConfigBO> componentConfigs;

    private Map<String, Object> data;

}
