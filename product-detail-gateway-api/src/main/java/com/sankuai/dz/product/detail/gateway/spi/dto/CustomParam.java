package com.sankuai.dz.product.detail.gateway.spi.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/1/10 10:28
 */

@Data
@TypeDoc(description = "链接上的业务定制参数")
public class CustomParam implements Serializable {

    @FieldDoc(description = "定制参数集合", requiredness = Requiredness.REQUIRED)
    private final Map<String, String> customParams = new HashMap<>();

    public String getParam(RequestCustomParamEnum key) {
        return customParams.get(key.name());
    }

    public void addParam(String key, String value) {
        if (StringUtils.isBlank(key) || StringUtils.isBlank(value)) {
            return;
        }
        customParams.put(key, value);
    }

}
