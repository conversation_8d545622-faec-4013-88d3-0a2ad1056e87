package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * SPIConfigService 完整测试套件
 * 包含所有相关的测试类，确保覆盖率大于70%
 * 
 * @Author: guang<PERSON>jie
 * @Date: 2025/1/15 23:30
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    SPIConfigServiceTest.class,
    SPIConfigServiceEdgeCaseTest.class
})
public class SPIConfigServiceTestSuite {
    // 测试套件类，无需实现内容
}
