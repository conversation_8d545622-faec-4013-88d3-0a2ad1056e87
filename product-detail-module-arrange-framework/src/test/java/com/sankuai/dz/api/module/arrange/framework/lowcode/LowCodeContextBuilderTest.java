package com.sankuai.dz.api.module.arrange.framework.lowcode;

import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigStorage;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * LowCodeContextBuilder 单元测试
 * 目标覆盖率: >60%
 *
 * @Author: guangyujie
 * @Date: 2025/1/16 10:00
 */
@RunWith(MockitoJUnitRunner.class)
public class LowCodeContextBuilderTest {

    private ModuleArrangeRequest validRequest;
    private PageConfigRoutingKey validRoutingKey;
    private ProductDetailPageRequest validPageRequest;
    private PageDisplayConfigBO validPageConfig;
    private ConfigQueryResponse validConfigResponse;

    @Before
    public void setUp() {
        // 创建有效的路由key
        validRoutingKey = new PageConfigRoutingKey();
        validRoutingKey.setScene("detail");
        validRoutingKey.setProductType(1);
        validRoutingKey.setProductFirstCategoryId(100);

        // 创建有效的页面请求
        validPageRequest = new ProductDetailPageRequest();
        validPageRequest.setPageConfigRoutingKey(validRoutingKey);

        // 创建有效的模块编排请求
        Set<String> moduleKeys = new HashSet<>(Arrays.asList("module1", "module2", "module3"));
        validRequest = new ModuleArrangeRequest(validPageRequest, moduleKeys);

        // 创建有效的页面配置
        validPageConfig = createMockPageDisplayConfig();

        // 创建有效的配置响应
        validConfigResponse = new ConfigQueryResponse("test-key", validPageConfig);
    }

    /**
     * 测试 build 方法 - 正常场景
     */
    @Test
    public void testBuildSuccess() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 模拟 DisplayConfigStorage.getConfig() 返回有效配置
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(validConfigResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(validRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);

            // 验证模块配置被正确过滤
            assertNotNull("应该能获取到module1配置", result.getModuleDisplayConfig("module1"));
            assertNotNull("应该能获取到module2配置", result.getModuleDisplayConfig("module2"));
            assertNull("不应该获取到module4配置", result.getModuleDisplayConfig("module4"));

            // 验证数据源配置被正确合并
            assertNotNull("应该能获取到fetcher1数据源", result.getDataSourceConfig("fetcher1"));
            assertNotNull("应该能获取到fetcher2数据源", result.getDataSourceConfig("fetcher2"));

            // 验证 DisplayConfigStorage.getConfig 被调用
            displayConfigMock.verify(() -> DisplayConfigStorage.getConfig(validRoutingKey), times(1));
        }
    }

    /**
     * 测试 build 方法 - 配置为空场景
     */
    @Test
    public void testBuildWithNullConfig() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 模拟 DisplayConfigStorage.getConfig() 返回空配置
            ConfigQueryResponse emptyResponse = new ConfigQueryResponse("test-key", null);
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(emptyResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(validRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNull("应该返回空的模块配置", result.getModuleDisplayConfig("module1"));
            assertNull("应该返回空的数据源配置", result.getDataSourceConfig("fetcher1"));
            assertTrue("所有数据源配置应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
            assertTrue("所有依赖的fetcher名称应该为空", result.getAllDependentFetcherNames().isEmpty());

            // 验证 DisplayConfigStorage.getConfig 被调用
            displayConfigMock.verify(() -> DisplayConfigStorage.getConfig(validRoutingKey), times(1));
        }
    }

    /**
     * 测试 build 方法 - 请求中的 moduleKeys 为空
     */
    @Test
    public void testBuildWithEmptyModuleKeys() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建包含空 moduleKeys 的请求
            Set<String> emptyModuleKeys = new HashSet<>();
            ModuleArrangeRequest emptyRequest = new ModuleArrangeRequest(validPageRequest, emptyModuleKeys);

            // 模拟 DisplayConfigStorage.getConfig() 返回有效配置
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(validConfigResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(emptyRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNull("不应该获取到任何模块配置", result.getModuleDisplayConfig("module1"));
            assertTrue("所有数据源配置应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
        }
    }

    /**
     * 测试 build 方法 - 请求中的 moduleKeys 部分匹配
     */
    @Test
    public void testBuildWithPartialMatchingModuleKeys() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建只包含部分匹配 moduleKeys 的请求
            Set<String> partialModuleKeys = new HashSet<>(Arrays.asList("module1", "non-existent-module"));
            ModuleArrangeRequest partialRequest = new ModuleArrangeRequest(validPageRequest, partialModuleKeys);

            // 模拟 DisplayConfigStorage.getConfig() 返回有效配置
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(validConfigResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(partialRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNotNull("应该能获取到module1配置", result.getModuleDisplayConfig("module1"));
            assertNull("不应该获取到module2配置", result.getModuleDisplayConfig("module2"));
            assertNull("不应该获取到不存在的模块配置", result.getModuleDisplayConfig("non-existent-module"));

            // 验证数据源配置只包含 module1 相关的
            List<DataSourceConfigBO> allDataSources = result.getAllRelatedDataSourceConfig();
            assertFalse("数据源配置不应该为空", allDataSources.isEmpty());
        }
    }

    /**
     * 测试 build 方法 - 页面配置中没有匹配的模块
     */
    @Test
    public void testBuildWithNoMatchingModules() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建空的页面配置
            PageDisplayConfigBO emptyPageConfig = new PageDisplayConfigBO(new HashMap<>());
            ConfigQueryResponse emptyConfigResponse = new ConfigQueryResponse("test-key", emptyPageConfig);

            // 模拟 DisplayConfigStorage.getConfig() 返回空的页面配置
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(emptyConfigResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(validRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNull("不应该获取到任何模块配置", result.getModuleDisplayConfig("module1"));
            assertTrue("所有数据源配置应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
        }
    }

    /**
     * 测试 build 方法 - 数据源合并功能
     */
    @Test
    public void testBuildWithDataSourceMerging() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建包含重复数据源的页面配置
            PageDisplayConfigBO configWithDuplicateDataSources = createPageConfigWithDuplicateDataSources();
            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", configWithDuplicateDataSources);

            // 模拟 DisplayConfigStorage.getConfig() 返回配置
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class))).thenReturn(configResponse);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(validRequest);

            // 验证结果
            assertNotNull("结果不应该为空", result);

            // 验证数据源被正确合并
            DataSourceConfigBO mergedDataSource = result.getDataSourceConfig("common-fetcher");
            assertNotNull("合并的数据源应该存在", mergedDataSource);

            // 验证 optionalFieldName 被正确合并
            Set<String> optionalFields = mergedDataSource.getOptionalFieldName();
            assertTrue("应该包含field1", optionalFields.contains("field1"));
            assertTrue("应该包含field2", optionalFields.contains("field2"));
            assertTrue("应该包含field3", optionalFields.contains("field3"));
        }
    }

    /**
     * 测试 build 方法 - null 请求
     */
    @Test
    public void testBuildWithNullRequest() {
        // 执行测试
        LowCodeContext result = LowCodeContextBuilder.build(null);

        // 验证结果 - 应该返回空的 LowCodeContext 而不是抛出异常
        assertNotNull("结果不应该为空", result);
        assertNull("应该返回空的模块配置", result.getModuleDisplayConfig("module1"));
        assertTrue("所有数据源配置应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
    }

    /**
     * 创建模拟的页面显示配置
     */
    private PageDisplayConfigBO createMockPageDisplayConfig() {
        Map<String, List<ModuleDisplayConfigBO>> moduleConfigs = new HashMap<>();

        // 创建 module1 配置
        ModuleDisplayConfigBO module1Config = mock(ModuleDisplayConfigBO.class);
        when(module1Config.getAllDataSource()).thenReturn(createDataSourceMap("fetcher1", "field1"));
        moduleConfigs.put("module1", Arrays.asList(module1Config));

        // 创建 module2 配置
        ModuleDisplayConfigBO module2Config = mock(ModuleDisplayConfigBO.class);
        when(module2Config.getAllDataSource()).thenReturn(createDataSourceMap("fetcher2", "field2"));
        moduleConfigs.put("module2", Arrays.asList(module2Config));

        // 创建 module4 配置（不在请求的 moduleKeys 中）
        ModuleDisplayConfigBO module4Config = mock(ModuleDisplayConfigBO.class);
        moduleConfigs.put("module4", Arrays.asList(module4Config));

        PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
        Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
        matchedConfigs.put("module1", module1Config);
        matchedConfigs.put("module2", module2Config);
        matchedConfigs.put("module4", module4Config);

        when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class))).thenReturn(matchedConfigs);

        return pageConfig;
    }

    /**
     * 创建包含重复数据源的页面配置
     */
    private PageDisplayConfigBO createPageConfigWithDuplicateDataSources() {
        ModuleDisplayConfigBO module1Config = mock(ModuleDisplayConfigBO.class);
        when(module1Config.getAllDataSource()).thenReturn(createDataSourceMap("common-fetcher", "field1", "field2"));

        ModuleDisplayConfigBO module2Config = mock(ModuleDisplayConfigBO.class);
        when(module2Config.getAllDataSource()).thenReturn(createDataSourceMap("common-fetcher", "field2", "field3"));

        PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
        Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
        matchedConfigs.put("module1", module1Config);
        matchedConfigs.put("module2", module2Config);

        when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class))).thenReturn(matchedConfigs);

        return pageConfig;
    }

    /**
     * 创建数据源配置Map
     */
    private Map<String, DataSourceConfigBO> createDataSourceMap(String fetcherName, String... fields) {
        Set<String> fieldSet = new HashSet<>(Arrays.asList(fields));
        DataSourceConfigBO dataSource = new DataSourceConfigBO(fetcherName, fieldSet);
        Map<String, DataSourceConfigBO> dataSourceMap = new HashMap<>();
        dataSourceMap.put(fetcherName, dataSource);
        return dataSourceMap;
    }
}
