package com.sankuai.dz.product.detail.metadata.sdk.utils.json;

import static org.mockito.Mockito.verify;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MapKeySerializerTest {

    @Mock
    private JsonGenerator jsonGenerator;

    @Mock
    private SerializerProvider serializerProvider;

    private final MapKeySerializer serializer = new MapKeySerializer();

    /**
     * Test serialization of Integer value
     */
    @Test
    public void testSerializeInteger() throws Throwable {
        // arrange
        Integer value = 123;
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"java.lang.Integer\",\"value\":123}");
    }

    /**
     * Test serialization of String value
     */
    @Test
    public void testSerializeString() throws Throwable {
        // arrange
        String value = "test";
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"java.lang.String\",\"value\":\"test\"}");
    }

    /**
     * Test serialization of Boolean value
     */
    @Test
    public void testSerializeBoolean() throws Throwable {
        // arrange
        Boolean value = true;
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"java.lang.Boolean\",\"value\":true}");
    }

    /**
     * Test serialization of Enum value
     */
    @Test
    public void testSerializeEnum() throws Throwable {
        // arrange
        TestEnum value = TestEnum.TEST;
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"" + TestEnum.class.getName() + "\",\"value\":\"TEST\"}");
    }

    /**
     * Test serialization of Array value
     */
    @Test
    public void testSerializeArray() throws Throwable {
        // arrange
        String[] value = new String[] { "test1", "test2" };
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"[Ljava.lang.String;\",\"value\":[\"test1\",\"test2\"]}");
    }

    /**
     * Test serialization of final class object
     */
    @Test
    public void testSerializeFinalClassObject() throws Throwable {
        // arrange
        FinalTestClass value = new FinalTestClass("test");
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"" + FinalTestClass.class.getName() + "\",\"value\":\"test\"}");
    }

    /**
     * Test serialization of non-final class object
     */
    @Test
    public void testSerializeNonFinalClassObject() throws Throwable {
        // arrange
        NonFinalTestClass value = new NonFinalTestClass("test");
        // act
        serializer.serialize(value, jsonGenerator, serializerProvider);
        // assert
        verify(jsonGenerator).writeFieldName("{\"@class\":\"" + NonFinalTestClass.class.getName() + "\",\"value\":\"test\"}");
    }

    // Test helper classes
    private enum TestEnum {

        TEST
    }

    private final static class FinalTestClass {

        private final String value;

        public FinalTestClass(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }
    }

    private static class NonFinalTestClass {

        private final String value;

        public NonFinalTestClass(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return value;
        }
    }
}
