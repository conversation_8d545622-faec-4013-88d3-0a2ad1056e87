package com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigIncorrectException;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/5/27 10:59
 */
@Getter
public class PageDisplayConfigBO implements Serializable {

    /**
     * 页面中所有模块的低代码配置
     * key:moduleKey，全局唯一
     * value:配置
     */
    private final Map<String, ModuleDisplayConfigBO> moduleConfigs;

    /**
     * 页面维度所需数据源与字段
     */
    private final Map<String, DataSourceConfigBO> allDataSource;

    public PageDisplayConfigBO(Map<String, ModuleDisplayConfigBO> moduleConfigs) {
        this.moduleConfigs = Collections.unmodifiableMap(moduleConfigs);
        this.allDataSource = Collections.unmodifiableMap(buildAllDataSource());
    }

    private Map<String, DataSourceConfigBO> buildAllDataSource() {
        if (CollectionUtils.isEmpty(moduleConfigs.values())) {
            return new HashMap<>();
        }
        return moduleConfigs.values().stream()
                .map(ModuleDisplayConfigBO::getComponentConfigList)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(ComponentDisplayConfigBO::getDataSourceConfigs)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(
                        // 按fetcherName分组，并合并optionalFieldName
                        DataSourceConfigBO::getFetcherName,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                dataSourceList -> {
                                    // 合并所有optionalFieldName
                                    Set<String> mergedOptionalFields = dataSourceList.stream()
                                            .map(DataSourceConfigBO::getOptionalFieldName)
                                            .filter(Objects::nonNull)
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toSet());
                                    return new DataSourceConfigBO(dataSourceList.get(0).getFetcherName(), mergedOptionalFields);
                                }
                        )
                ));
    }

    /**
     * 校验配置正确性
     */
    public void check() throws LowCodeConfigIncorrectException {
        if (MapUtils.isEmpty(this.moduleConfigs)) {
            throw new LowCodeConfigIncorrectException("模块配置为空");
        }
        if (this.allDataSource == null) {
            throw new LowCodeConfigIncorrectException("数据源为null");
        }
        for (ModuleDisplayConfigBO moduleDisplayConfig : this.moduleConfigs.values()) {
            moduleDisplayConfig.check();
        }
        for (DataSourceConfigBO dataSourceConfig : this.allDataSource.values()) {
            dataSourceConfig.check();
        }

    }

}
