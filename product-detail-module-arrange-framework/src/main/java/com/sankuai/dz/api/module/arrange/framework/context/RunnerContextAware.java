package com.sankuai.dz.api.module.arrange.framework.context;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.context.exception.RunnerContextFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.springframework.beans.factory.InitializingBean;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/4/17 10:23
 */
@SuppressWarnings("rawtypes")
public abstract class RunnerContextAware implements InitializingBean {

    /**
     * 公共参数，理论上只读
     * tips：代码上为了性能考虑设置为非只读，非全局变量，随意修改只会影响单次请求结果
     */
    protected ProductDetailPageRequest request;

    /**
     * Fetcher返回值
     * tips：代码上为了性能考虑设置为非只读，非全局变量，随意修改只会影响单次请求结果
     */
    private ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap;

    @Override
    public void afterPropertiesSet() throws Exception {
        final RunnerContext runnerContext = RunnerContext.getRunnerContext();
        if (runnerContext == null) {
            throw new RunnerContextFatalException("FATAL ERROR!!!无法获取上下文!!!请务必立刻马上检查代码");
        }
        //检查上下文有效性，如果有问题直接抛出异常，理论上不会，除非框架代码有bug
        runnerContext.checkParam();
        this.request = runnerContext.getRequest();
        this.fetcherContextMap = runnerContext.getFetcherContextMap();
        doInit(runnerContext);
    }

    protected abstract void doInit(final RunnerContext runnerContext);

    protected abstract boolean containsDependencyFetcher(final String dependencyFetcherName);

    @SuppressWarnings({"unchecked", "rawtypes"})
    protected <Result extends FetcherReturnValueDTO> Optional<Result> getDependencyResult(
            final Class<? extends BaseFetcherContext> dependencyFetcherClass,
            Class<Result> tClass) {
        FetcherResponse<FetcherReturnValueDTO> fetchResponse = getDependencyResponse(dependencyFetcherClass);
        if (!fetchResponse.isSuccess()) {
            return Optional.empty();
        } else {
            return Optional.ofNullable((Result) fetchResponse.getReturnValue());
        }
    }

    /**
     * 获取依赖fetcher的结果，注意需要显示定义在注解当中才能获取，不然会报错
     */
    @SuppressWarnings("unchecked")
    protected <Result extends FetcherReturnValueDTO> Result getDependencyResult(
            final Class<? extends BaseFetcherContext> dependencyFetcherClass) {
        FetcherResponse<FetcherReturnValueDTO> fetchResponse = getDependencyResponse(dependencyFetcherClass);
        if (!fetchResponse.isSuccess()) {
            return null;
        } else {
            return (Result) fetchResponse.getReturnValue();
        }
    }

    /**
     * 获取依赖fetcher的结果，注意需要显示定义在注解当中才能获取，不然会报错
     */
    protected <Result extends FetcherReturnValueDTO> FetcherResponse<Result> getDependencyResponse(
            final Class<? extends BaseFetcherContext> dependentFetcherClass) {
        String dependencyFetcherName = FetcherDefinition.getFetcherName(dependentFetcherClass);
        return getDependencyResponse(dependencyFetcherName);
    }

    @SuppressWarnings("unchecked")
    protected <Result extends FetcherReturnValueDTO> FetcherResponse<Result> getDependencyResponse(
            final String dependencyFetcherName) {
        if (containsDependencyFetcher(dependencyFetcherName)) {
            BaseFetcherContext baseFetcherContext = this.fetcherContextMap.get(dependencyFetcherName);
            if (baseFetcherContext == null) {
                throw new FetcherFatalException(String.format("依赖fetcher(%s)未执行且不在FetcherContext中", dependencyFetcherName));
            }
            return baseFetcherContext.getFetchResponse();
        } else {
            throw new BuilderFatalError(String.format(
                    "(%s)没有依赖(%s)，但是需要获取依赖的返回值", this.getClass().getSimpleName(), dependencyFetcherName
            ));
        }
    }

    @SuppressWarnings("unchecked")
    protected <Result extends FetcherReturnValueDTO> Result getDependencyResult(final String dependencyFetcherName) {
        FetcherResponse<FetcherReturnValueDTO> fetchResponse = getDependencyResponse(dependencyFetcherName);
        if (!fetchResponse.isSuccess()) {
            return null;
        } else {
            return (Result) fetchResponse.getReturnValue();
        }
    }

}
