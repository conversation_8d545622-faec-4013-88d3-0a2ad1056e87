package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler.SPICallHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 20:41
 */
@Slf4j
@Component
public class SPIConfigService implements InitializingBean {

    @Resource
    private SPICallFactory spiCallFactory;

    private static final String SPI_CONFIG_LION_KEY = "com.sankuai.dzshoppingguide.detail.gateway.spi.config";

    private Map<String, SPIConfigDTO> configMap = new HashMap<>();

    public SPIConfigDTO getSPIConfigDTO(String interfaceAlias) {
        return configMap.get(interfaceAlias);
    }

    private void initSPIConfig(String json) {
        try {
            Map<String, SPIConfigDTO> configMap = JSONObject.parseObject(json, new TypeReference<Map<String, SPIConfigDTO>>() {
            });
            if (MapUtils.isEmpty(configMap)) {
                throw new SPIConfigFatalException("FATAL ERROR!!!SPI配置为空!!!");
            }
            //所有接口类型的SPI服务集合，用于一次性刷新配置，避免出现部分更新的不一致问题
            Map<InterfaceTypeEnum, Map<String, Object>> allSPIMap = new HashMap<>();
            for (InterfaceTypeEnum interfaceType : InterfaceTypeEnum.values()) {
                //针对不同类型的interface构建spiMap
                SPICallHandler spiCallHandler = spiCallFactory.getHandler(interfaceType);
                Map<String, Object> spiMap = new HashMap<>();
                for (SPIConfigDTO spiConfigDTO : configMap.values()) {
                    if (spiConfigDTO.getInterfaceType() != interfaceType) {
                        continue;//不是该类型的接口直接跳过
                    }
                    Object spiService = spiCallHandler.getSpiService(spiConfigDTO);
                    spiMap.put(spiConfigDTO.getInterfaceAlias(), spiService);
                }
                if (MapUtils.isEmpty(spiMap)) {
                    throw new SPIConfigFatalException(interfaceType.name() + "类型的SPI服务集合为空!");
                }
                allSPIMap.put(interfaceType, spiMap);
            }
            //存储新的spiMap，全部加载完后一次性替换，避免出现部分更新的不一致问题
            for (Map.Entry<InterfaceTypeEnum, Map<String, Object>> entry : allSPIMap.entrySet()) {
                SPICallHandler spiCallHandler = spiCallFactory.getHandler(entry.getKey());
                spiCallHandler.refreshSPIMap(entry.getValue());
            }
            //更新configMap，只有更新了configMap才会发生实质性调用
            this.configMap = configMap;
        } catch (SPIConfigFatalException exception) {
            log.error("SPIConfigService.initSPIConfig", exception);
            throw exception;
        } catch (Throwable throwable) {
            log.error("SPIConfigService.initSPIConfig", new SPIConfigFatalException(throwable));
            throw throwable;
        }
    }

    @Override
    public void afterPropertiesSet() {
        initSPIConfig(Lion.getString(Environment.getAppName(), SPI_CONFIG_LION_KEY));
        Lion.addConfigListener(SPI_CONFIG_LION_KEY, configEvent -> initSPIConfig(configEvent.getValue()));
    }
}
