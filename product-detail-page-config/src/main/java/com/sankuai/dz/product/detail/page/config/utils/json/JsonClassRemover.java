package com.sankuai.dz.product.detail.page.config.utils.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 19:43
 */
public class JsonClassRemover {

    private static final ObjectMapper mapper = new ObjectMapper();

    public static String process(String originalJson) {
        try {
            JsonNode rootNode = mapper.readTree(originalJson);

            // 处理根节点
            removeClassFields(rootNode);

            // 处理 moduleResponse 的特殊键
            processModuleResponseKeys(rootNode, mapper);

            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static void removeClassFields(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objNode = (ObjectNode) node;
            objNode.remove("@class");

            objNode.fields().forEachRemaining(entry ->
                    removeClassFields(entry.getValue())
            );
        } else if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            arrayNode.forEach(JsonClassRemover::removeClassFields);
        }
    }

    private static void processModuleResponseKeys(JsonNode rootNode, ObjectMapper mapper) {
        JsonNode moduleResponse = rootNode.path("moduleResponse");
        if (moduleResponse.isObject()) {
            ObjectNode newModuleResponse = mapper.createObjectNode();
            ObjectNode originalModuleResponse = (ObjectNode) moduleResponse;

            originalModuleResponse.fields().forEachRemaining(entry -> {
                try {
                    String newKey = mapper.readTree(entry.getKey())
                            .path("value")
                            .asText();
                    newModuleResponse.set(newKey, entry.getValue());
                } catch (Exception e) {
                    newModuleResponse.set(entry.getKey(), entry.getValue());
                }
            });

            ((ObjectNode) rootNode).set("moduleResponse", newModuleResponse);
        }
    }
}
