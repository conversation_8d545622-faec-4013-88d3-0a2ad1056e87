package com.sankuai.dz.product.detail.page.config.model.layout.lion.config;

import com.sankuai.dz.product.detail.page.config.enums.TabItemTypeEnum;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.exclusive.ExclusiveModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.normal.NormalModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.overlay.OverlayModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.tab.TabModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.tab.TabModuleItemDTO;

import java.util.Arrays;

/**
 * @Author: guangyujie
 * @Date: 2025/4/28 17:41
 */
public abstract class BaseConfigTool {

    protected ModuleGroupConfigDTO buildModuleGroupConfigDTO(final String groupName,
                                                           final ModuleConfigDTO... moduleConfigDTOS) {
        ModuleGroupConfigDTO groupConfigDTO = new ModuleGroupConfigDTO();
        groupConfigDTO.setGroupName(groupName);
        groupConfigDTO.setModuleList(Arrays.asList(moduleConfigDTOS));
        return groupConfigDTO;
    }

    protected NormalModuleConfigDTO buildNormalModuleConfigDTO(final String moduleKey,
                                                             final String interfaceAlias,
                                                             final int firstScreenFlag) {
        NormalModuleConfigDTO normalModuleConfigDTO = new NormalModuleConfigDTO();
        normalModuleConfigDTO.setFirstScreenFlag(firstScreenFlag);
        normalModuleConfigDTO.setInterfaceAlias(interfaceAlias);
        normalModuleConfigDTO.setModuleKey(moduleKey);
        return normalModuleConfigDTO;
    }

    protected NormalModuleConfigDTO buildNormalModuleConfigDTO(final String moduleKey,
                                                             final String interfaceAlias,
                                                             final int firstScreenFlag,
                                                             final OverlayModuleConfigDTO... overlayModuleConfigDTOs) {
        NormalModuleConfigDTO normalModuleConfigDTO = new NormalModuleConfigDTO();
        normalModuleConfigDTO.setFirstScreenFlag(firstScreenFlag);
        normalModuleConfigDTO.setInterfaceAlias(interfaceAlias);
        normalModuleConfigDTO.setModuleKey(moduleKey);
        normalModuleConfigDTO.setOverlayModules(Arrays.asList(overlayModuleConfigDTOs));
        return normalModuleConfigDTO;
    }

    protected OverlayModuleConfigDTO buildOverlayModuleConfigDTO(final String moduleKey,
                                                               final String interfaceAlias,
                                                               final int firstScreenFlag) {
        OverlayModuleConfigDTO overlayModuleConfigDTO = new OverlayModuleConfigDTO();
        overlayModuleConfigDTO.setFirstScreenFlag(firstScreenFlag);
        overlayModuleConfigDTO.setInterfaceAlias(interfaceAlias);
        overlayModuleConfigDTO.setModuleKey(moduleKey);
        return overlayModuleConfigDTO;
    }

    protected ExclusiveModuleConfigDTO buildExclusiveModuleConfigDTO(final String moduleKey,
                                                                   final NormalModuleConfigDTO... normalModuleConfigDTOS) {
        ExclusiveModuleConfigDTO exclusiveModuleConfigDTO = new ExclusiveModuleConfigDTO();
        exclusiveModuleConfigDTO.setModuleList(Arrays.asList(normalModuleConfigDTOS));
        exclusiveModuleConfigDTO.setModuleKey(moduleKey);
        return exclusiveModuleConfigDTO;
    }

    protected TabModuleConfigDTO buildTabModuleConfigDTO(final String moduleKey,
                                                       final String interfaceAlias,
                                                       final int firstScreenFlag,
                                                       final TabModuleItemDTO... tabModuleItemDTOS) {
        TabModuleConfigDTO tabModuleConfigDTO = new TabModuleConfigDTO();
        tabModuleConfigDTO.setTabItemList(Arrays.asList(tabModuleItemDTOS));
        tabModuleConfigDTO.setFirstScreenFlag(firstScreenFlag);
        tabModuleConfigDTO.setInterfaceAlias(interfaceAlias);
        tabModuleConfigDTO.setModuleKey(moduleKey);
        return tabModuleConfigDTO;
    }

    protected TabModuleItemDTO buildTabModuleItemDTO(String tabKey, String tabName, ModuleGroupConfigDTO... moduleConfigDTOS) {
        TabModuleItemDTO tabModuleItemDTO = new TabModuleItemDTO();
        tabModuleItemDTO.setTabKey(tabKey);
        tabModuleItemDTO.setTabName(tabName);
        tabModuleItemDTO.setTabType(TabItemTypeEnum.normal);
        tabModuleItemDTO.setGroupConfig(Arrays.asList(moduleConfigDTOS));
        return tabModuleItemDTO;
    }

}
