package com.sankuai.dz.api.module.arrange.framework.fetcher.storage;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.AggregateFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.AggregateFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.ComponentFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.NormalFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import org.springframework.context.ApplicationContext;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 16:08
 */
@SuppressWarnings("rawtypes")
public class FetcherDefinitionStorage {

    private static ApplicationContext applicationContext;

    public static BaseFetcherContext getFetcherContext(final FetcherDefinition fetcherDefinition) {
        return applicationContext.getBean(fetcherDefinition.getFetcherClass());
    }

    private static final Map<String, FetcherDefinition> fetcherDefinitionMap = new HashMap<>();

    public static FetcherDefinition getFetcherDefinition(final Class<? extends BaseFetcherContext> fetcherClass) {
        return getFetcherDefinition(FetcherDefinition.getFetcherName(fetcherClass));
    }

    public static FetcherDefinition getFetcherDefinition(final String fetcherName) {
        FetcherDefinition fetcherDefinition = fetcherDefinitionMap.get(fetcherName);
        if (fetcherDefinition == null) {
            throw new FetcherFatalException("查不到FetcherDefinition:" + fetcherName);
        }
        return fetcherDefinition;
    }

    public static Collection<FetcherDefinition> getAllFetcherDefinition() {
        return fetcherDefinitionMap.values();
    }

    public static synchronized void initFetcherDefinitionStorage(final ApplicationContext applicationContext) throws Exception {
        FetcherDefinitionStorage.applicationContext = applicationContext;
        //第一步:构建普通FetcherDefinition
        buildNormalFetcherDefinitions(applicationContext);
        //第二步:构建ComponentFetcherDefinition
        buildComponentFetcherDefinitions(applicationContext);
        //第三步:构建AggregateFetcherDefinition，依赖ComponentFetcherDefinition
        buildAggregateFetcherDefinitions(applicationContext);
    }

    @SuppressWarnings("unchecked")
    private static void buildNormalFetcherDefinitions(final ApplicationContext applicationContext) throws Exception {
        Map<String, Object> allFetchers = applicationContext.getBeansWithAnnotation(Fetcher.class);
        for (Map.Entry<String, Object> entry : allFetchers.entrySet()) {
            Object fetcherContext = entry.getValue();
            String fetcherName = entry.getKey();
            Class<?> fetcherContextClass = fetcherContext.getClass();
            if (!(fetcherContext instanceof NormalFetcherContext) || !NormalFetcherContext.class.isAssignableFrom(fetcherContextClass)) {
                throw new IllegalArgumentException(String.format(
                        "被@Fetcher注解的类(%s)没有继承NormalFetcherContext", fetcherContextClass.getName()
                ));
            }
            if (!applicationContext.isPrototype(fetcherName)) {
                throw new IllegalArgumentException(String.format(
                        "Fetcher(%s)不是多例的(prototype)", fetcherContextClass.getName()
                ));
            }
            NormalFetcherDefinition fetcherDefinition = new NormalFetcherDefinition(
                    (Class<? extends NormalFetcherContext>) fetcherContextClass
            );
            if (fetcherDefinitionMap.containsKey(fetcherDefinition.getFetcherName())) {
                throw new IllegalArgumentException(String.format(
                        "出现重复定义的Fetcher:%s，需要注意fetcher唯一标识默认是FetcherClass#getSimpleName()，如果冲突会导致项目启动报错",
                        fetcherDefinition.getFetcherName()
                ));
            }
            fetcherDefinitionMap.put(fetcherDefinition.getFetcherName(), fetcherDefinition);
        }
    }

    @SuppressWarnings("unchecked")
    private static void buildComponentFetcherDefinitions(final ApplicationContext applicationContext) throws Exception {
        Map<String, Object> allFetchers = applicationContext.getBeansWithAnnotation(ComponentFetcher.class);
        for (Map.Entry<String, Object> entry : allFetchers.entrySet()) {
            Object fetcherContext = entry.getValue();
            String fetcherName = entry.getKey();
            Class<?> fetcherContextClass = fetcherContext.getClass();
            if (!(fetcherContext instanceof ComponentFetcherContext) || !ComponentFetcherContext.class.isAssignableFrom(fetcherContextClass)) {
                throw new IllegalArgumentException(String.format(
                        "被@ComponentFetcher注解的类(%s)没有继承ComponentFetcherContext", fetcherContextClass.getName()
                ));
            }
            if (!applicationContext.isPrototype(fetcherName)) {
                throw new IllegalArgumentException(String.format(
                        "ComponentFetcher(%s)不是多例的(prototype)", fetcherContextClass.getName()
                ));
            }
            ComponentFetcherDefinition fetcherDefinition = new ComponentFetcherDefinition(
                    (Class<? extends ComponentFetcherContext>) fetcherContextClass
            );
            if (fetcherDefinitionMap.containsKey(fetcherDefinition.getFetcherName())) {
                throw new IllegalArgumentException(String.format(
                        "出现重复定义的ComponentFetcher:%s，需要注意fetcher唯一标识默认是FetcherClass#getSimpleName()，如果冲突会导致项目启动报错",
                        fetcherDefinition.getFetcherName()
                ));
            }
            fetcherDefinitionMap.put(fetcherDefinition.getFetcherName(), fetcherDefinition);
        }
    }

    @SuppressWarnings("unchecked")
    private static void buildAggregateFetcherDefinitions(final ApplicationContext applicationContext) throws Exception {
        Map<String, Object> allFetchers = applicationContext.getBeansWithAnnotation(AggregateFetcher.class);
        for (Map.Entry<String, Object> entry : allFetchers.entrySet()) {
            Object fetcherContext = entry.getValue();
            String fetcherName = entry.getKey();
            Class<?> fetcherContextClass = fetcherContext.getClass();
            if (!(fetcherContext instanceof AggregateFetcherContext) || !AggregateFetcherContext.class.isAssignableFrom(fetcherContextClass)) {
                throw new IllegalArgumentException(String.format(
                        "被@AggregateFetcher注解的类(%s)没有继承AggregateFetcherContext", fetcherContextClass.getName()
                ));
            }
            if (!applicationContext.isPrototype(fetcherName)) {
                throw new IllegalArgumentException(String.format(
                        "AggregateFetcher(%s)不是多例的(prototype)", fetcherContextClass.getName()
                ));
            }
            List<ComponentFetcherDefinition> allComponentFetchers = fetcherDefinitionMap.values().stream()
                    .filter(fetcherDefinition -> fetcherDefinition.getFetcherType() == FetcherTypeEnum.COMPONENT_FETCHER)
                    .map(fetcherDefinition -> (ComponentFetcherDefinition) fetcherDefinition)
                    .collect(Collectors.toList());
            AggregateFetcherDefinition fetcherDefinition = new AggregateFetcherDefinition(
                    (Class<? extends AggregateFetcherContext>) fetcherContextClass, allComponentFetchers
            );
            if (fetcherDefinitionMap.containsKey(fetcherDefinition.getFetcherName())) {
                throw new IllegalArgumentException(String.format(
                        "出现重复定义的AggregateFetcher:%s，需要注意fetcher唯一标识默认是FetcherClass#getSimpleName()，如果冲突会导致项目启动报错",
                        fetcherDefinition.getFetcherName()
                ));
            }
            fetcherDefinitionMap.put(fetcherDefinition.getFetcherName(), fetcherDefinition);
        }
    }

}
