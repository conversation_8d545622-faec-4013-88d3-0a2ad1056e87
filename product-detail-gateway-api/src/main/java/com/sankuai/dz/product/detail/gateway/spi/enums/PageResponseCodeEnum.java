package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

/**
 * @Author: guang<PERSON><PERSON><PERSON>
 * @Date: 2025/1/10 14:25
 */
@Getter
public enum PageResponseCodeEnum {

    SUCCESS(200, "成功"),
    UNAUTHORIZED(40100, "商品详情页未登录拦截"),
    FAILURE(500, "失败"),
    PARTLY_FAIL(501, "部分失败");

    private final int code;

    private final String desc;

    PageResponseCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PageResponseCodeEnum fromCode(int code) {
        for (PageResponseCodeEnum value : PageResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (PageResponseCodeEnum value : PageResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}