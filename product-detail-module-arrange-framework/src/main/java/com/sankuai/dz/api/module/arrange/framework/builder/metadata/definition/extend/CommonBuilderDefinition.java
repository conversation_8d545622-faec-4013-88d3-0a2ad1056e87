package com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;

/**
 * @Author: guangyujie
 * @Date: 2025/3/9 17:17
 */
public class CommonBuilderDefinition extends BuilderDefinition {

    @Override
    public BuilderTypeEnum getBuilderType() {
        return BuilderTypeEnum.COMMON_BUILDER;
    }

    @SuppressWarnings("rawtypes")
    public CommonBuilderDefinition(Class<? extends BaseBuilder> builderClass) {
        super(builderClass);
    }

}
