package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 15:54
 */
@Getter
public class DAG {//所有属性只读

    /**
     * 开始节点，graph中只会包含从该Start节点出发路过的节点，如果一个节点包含了两个开始节点则会忽略
     */
    private final String start;

    /**
     * 子节点邻接矩阵, key:当前节点,value:当前节点的所有子节点
     */
    private final Map<String, Set<String>> childrenGraph;//已经转换为只读

    /**
     * 父节点邻接矩阵,key:当前节点,value:当前节点的所有父节点
     */
    private final Map<String, Set<String>> parentGraph;//已经转换为只读

    /**
     * 子图所有节点的拓扑排序，用于后续遍历DAG与服务编排
     */
    private final List<String> topologicalSort;//已经转换为只读

    /**
     * 一个节点所有的祖先节点，父节点，父节点的父节点 ...... 递归到开始节点
     */
    private final Map<String, Set<String>> ancestorSet;//已经转换为只读

    /**
     * 该DAG是否包含入参节点
     */
    public boolean contains(String key) {
        return childrenGraph.containsKey(key);
    }

    /**
     * 右边入参Fetcher 是否是 左边入参Fetcher 的祖先节点
     * 需要注意的是全路径上的依赖关系（族谱）
     * 区别于FetcherDefinition中的classOfDependencies和nameOfDependencies，它们仅代表上一层的依赖
     */
    public boolean isAncestor(String child, String ancestor) {
        return ancestorSet.getOrDefault(child, new HashSet<>()).contains(ancestor);
    }

    /**
     * 获取祖先节点
     * 需要注意返回结果不包含入参节点!!!
     */
    public Set<String> getAncestors(String child) {
        return ancestorSet.getOrDefault(child, new HashSet<>());
    }

    public DAG(final DAGBuilder builder) {
        try {
            builder.checkParam();
            if (builder.needCheckGraphIntegrity) {
                //检查DAGGraph是否完整，Map.Keys是否包含了所有Map.Values
                DAGBFSUtils.checkDAGGraphIntegrity(builder.entireDAGParentGraph);
                DAGBFSUtils.checkDAGGraphIntegrity(builder.entireDAGChildrenGraph);
            }
            if (builder.needCheckCycle) {
                //检查DAG是否有环
                DAGBFSCycleChecker.KahnResult kahnResult = DAGBFSCycleChecker.performKahnCheck(
                        builder.entireDAGChildrenGraph, builder.entireDAGParentGraph
                );
                kahnResult.checkCycle(builder.entireDAGChildrenGraph);
            }
            //裁剪全量DAG，获取跟起始点相关的子图
            DAGGraphCut.DAGGraphCutResult DAGGraphCutResult = DAGGraphCut.graphCut(
                    builder.start, builder.entireDAGChildrenGraph, builder.entireDAGParentGraph
            );
            //记录起始点
            this.start = builder.start;
            //DAG子图的childrenGraph格式，转换为只读
            this.childrenGraph = getUnmodifiableDagGraph(DAGGraphCutResult.getChildrenGraph());
            //DAG子图的parentGraph格式，转换为只读
            this.parentGraph = getUnmodifiableDagGraph(DAGGraphCutResult.getParentGraph());
            //DAG子图的拓扑排序，转换为只读
            this.topologicalSort = Collections.unmodifiableList(DAGGraphCutResult.getTopologicalSort());
            //构建每个节点的所有祖先节点，并转换为只读
            this.ancestorSet = getUnmodifiableAncestorSet(
                    DAGAncestorFinder.findAllAncestors(this.topologicalSort, this.parentGraph)
            );
        } catch (Throwable throwable) {
            throw new FetcherDAGException(String.format(
                    "起始节点(%s)的DAG构建失败，具体原因:%s", builder.start, throwable.getMessage()
            ), throwable);
        }
    }

    /**
     * 转换为只读
     */
    private static Map<String, Set<String>> getUnmodifiableDagGraph(Map<String, Set<String>> dagGraph) {
        Map<String, Set<String>> unmodifiableGraph = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : dagGraph.entrySet()) {
            unmodifiableGraph.put(entry.getKey(), Collections.unmodifiableSet(entry.getValue()));
        }
        return Collections.unmodifiableMap(unmodifiableGraph);
    }

    /**
     * 转换为只读
     */
    private static Map<String, Set<String>> getUnmodifiableAncestorSet(Map<String, Set<String>> ancestorSet) {
        Map<String, Set<String>> unmodifiableAncestorSet = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : ancestorSet.entrySet()) {
            unmodifiableAncestorSet.put(entry.getKey(), Collections.unmodifiableSet(entry.getValue()));
        }
        return Collections.unmodifiableMap(unmodifiableAncestorSet);
    }

    @Data
    @Builder
    public static class DAGBuilder {
        /**
         * 开始节点
         */
        final String start;
        /**
         * DAG子节点邻接矩阵全集，可能包含很多子图
         */
        final Map<String, Set<String>> entireDAGChildrenGraph;
        /**
         * DAG父节点邻接矩阵全集，可能包含很多子图
         */
        final Map<String, Set<String>> entireDAGParentGraph;
        /**
         * 是否需要检查有无环
         */
        final boolean needCheckCycle;
        /**
         * 是否需要检查entireDAGChildrenGraph和entireDAGParentGraph完整性
         */
        final boolean needCheckGraphIntegrity;

        public void checkParam() {
            if (StringUtils.isBlank(start)) {
                throw new FetcherDAGException("DAGBuilder异常，start is null");
            }
            if (MapUtils.isEmpty(entireDAGChildrenGraph)) {
                throw new FetcherDAGException("DAGBuilder异常，entireDAGChildrenGraph is empty");
            }
            if (MapUtils.isEmpty(entireDAGParentGraph)) {
                throw new FetcherDAGException("DAGBuilder异常，entireDAGParentGraph is empty");
            }
        }

    }

}
