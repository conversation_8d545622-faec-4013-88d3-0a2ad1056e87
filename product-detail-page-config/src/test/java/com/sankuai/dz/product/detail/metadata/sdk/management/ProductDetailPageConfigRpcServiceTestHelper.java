package com.sankuai.dz.product.detail.metadata.sdk.management;

import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.normal.NormalModuleConfigDTO;

import java.util.*;

/**
 * ProductDetailPageConfigRpcService 测试辅助工具类
 * 提供测试数据创建和验证方法
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 20:00
 */
public class ProductDetailPageConfigRpcServiceTestHelper {

    /**
     * 创建有效的美团路由key
     */
    public static PageConfigRoutingKey createValidMTRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(1); // 美团商品类型
        routingKey.setProductFirstCategoryId(100);
        routingKey.setProductSecondCategoryId(200);
        routingKey.setProductThirdCategoryId(300);

        // 添加可选路由参数
        Map<String, String> optionalMap = new HashMap<>();
        optionalMap.put("CLIENT_TYPE", "MT_APP");
        optionalMap.put("PAGE_REGION", "BEIJING");
        routingKey.setOptionalRoutingKeyMap(optionalMap);

        return routingKey;
    }

    /**
     * 创建有效的点评路由key
     */
    public static PageConfigRoutingKey createValidDPRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(2); // 点评商品类型
        routingKey.setProductFirstCategoryId(500);
        routingKey.setProductSecondCategoryId(600);
        routingKey.setProductThirdCategoryId(700);

        // 添加可选路由参数
        Map<String, String> optionalMap = new HashMap<>();
        optionalMap.put("CLIENT_TYPE", "DP_APP");
        optionalMap.put("PAGE_REGION", "SHANGHAI");
        routingKey.setOptionalRoutingKeyMap(optionalMap);

        return routingKey;
    }

    /**
     * 创建最小有效路由key
     */
    public static PageConfigRoutingKey createMinimalRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(1);
        return routingKey;
    }

    /**
     * 创建包含特殊字符的路由key
     */
    public static PageConfigRoutingKey createSpecialCharacterRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail-特殊场景");
        routingKey.setProductType(1);
        routingKey.setProductFirstCategoryId(100);

        Map<String, String> optionalMap = new HashMap<>();
        optionalMap.put("SPECIAL_KEY", "特殊值-123_test");
        routingKey.setOptionalRoutingKeyMap(optionalMap);

        return routingKey;
    }

    /**
     * 创建边界值路由key - 最大值
     */
    public static PageConfigRoutingKey createMaxValueRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(Integer.MAX_VALUE);
        routingKey.setProductFirstCategoryId(Integer.MAX_VALUE);
        routingKey.setProductSecondCategoryId(Integer.MAX_VALUE);
        routingKey.setProductThirdCategoryId(Integer.MAX_VALUE);
        return routingKey;
    }

    /**
     * 创建边界值路由key - 最小值
     */
    public static PageConfigRoutingKey createMinValueRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(1);
        routingKey.setProductFirstCategoryId(0);
        routingKey.setProductSecondCategoryId(0);
        routingKey.setProductThirdCategoryId(0);
        return routingKey;
    }

    /**
     * 创建模拟的配置模板
     */
    public static PageConfigTemplateDTO createMockConfigTemplate() {
        PageConfigTemplateDTO template = new PageConfigTemplateDTO();
        template.setMainConfigData(createMockPageConfigData());
        template.setOptionalRules(new ArrayList<>());
        return template;
    }

    /**
     * 创建模拟的页面配置数据
     */
    public static PageConfigDataDTO createMockPageConfigData() {
        PageConfigDataDTO configData = new PageConfigDataDTO();

        List<ModuleGroupConfigDTO> moduleGroups = new ArrayList<>();

        // 创建头部模块组
        ModuleGroupConfigDTO headerGroup = new ModuleGroupConfigDTO();
        headerGroup.setGroupName("header");
        headerGroup.setModuleList(Arrays.asList(createMockBizModule("header", "头部模块")));
        moduleGroups.add(headerGroup);

        // 创建详情模块组
        ModuleGroupConfigDTO detailGroup = new ModuleGroupConfigDTO();
        detailGroup.setGroupName("detail");
        detailGroup.setModuleList(Arrays.asList(
                createMockBizModule("product_info", "商品信息"),
                createMockBizModule("product_detail", "商品详情")
        ));
        moduleGroups.add(detailGroup);

        // 创建推荐模块组
        ModuleGroupConfigDTO recommendGroup = new ModuleGroupConfigDTO();
        recommendGroup.setGroupName("recommend");
        recommendGroup.setModuleList(Arrays.asList(createMockBizModule("recommend", "推荐模块")));
        moduleGroups.add(recommendGroup);

        configData.setModuleGroupConfigs(moduleGroups);
        return configData;
    }

    /**
     * 创建模拟的业务模块
     */
    private static BizModuleConfigDTO createMockBizModule(String moduleKey, String moduleName) {
        NormalModuleConfigDTO module = new NormalModuleConfigDTO();
        module.setFirstScreenFlag(1);
        module.setInterfaceAlias("sdfsdf");
        module.setModuleKey(moduleKey);
        return module;
    }

    /**
     * 创建模拟的路由key集合
     */
    public static Set<String> createMockRoutingKeySet() {
        Set<String> routingKeys = new HashSet<>();
        routingKeys.add("detail-default-1");
        routingKeys.add("detail-default-2");
        routingKeys.add("detail-first-1-100");
        routingKeys.add("detail-first-2-500");
        routingKeys.add("detail-second-1-200");
        routingKeys.add("detail-second-2-600");
        routingKeys.add("detail-third-1-300");
        routingKeys.add("detail-third-2-700");
        return routingKeys;
    }

    /**
     * 创建大型路由key集合（用于性能测试）
     */
    public static Set<String> createLargeRoutingKeySet() {
        Set<String> routingKeys = new HashSet<>();
        for (int i = 1; i <= 1000; i++) {
            routingKeys.add("detail-default-" + i);
            routingKeys.add("detail-first-" + i + "-" + (i * 100));
            routingKeys.add("detail-second-" + i + "-" + (i * 200));
            routingKeys.add("detail-third-" + i + "-" + (i * 300));
        }
        return routingKeys;
    }

    /**
     * 验证路由key的有效性
     */
    public static boolean isValidRoutingKey(PageConfigRoutingKey routingKey) {
        return routingKey != null
                && routingKey.getScene() != null
                && !routingKey.getScene().trim().isEmpty()
                && routingKey.getProductType() > 0;
    }

    /**
     * 验证配置模板的有效性
     */
    public static boolean isValidConfigTemplate(PageConfigTemplateDTO template) {
        return template != null
                && template.getMainConfigData() != null;
    }

    /**
     * 验证页面配置的有效性
     */
    public static boolean isValidPageConfig(PageConfigDataDTO config) {
        return config != null
                && config.getModuleGroupConfigs() != null
                && !config.getModuleGroupConfigs().isEmpty();
    }

    /**
     * 验证路由key集合的有效性
     */
    public static boolean isValidRoutingKeySet(Set<String> routingKeys) {
        return routingKeys != null;
    }

    /**
     * 获取路由key的描述信息
     */
    public static String getRoutingKeyDescription(PageConfigRoutingKey routingKey) {
        if (routingKey == null) {
            return "null routing key";
        }
        return String.format("RoutingKey[scene=%s, productType=%d, firstCategory=%d, secondCategory=%d, thirdCategory=%d]",
                routingKey.getScene(),
                routingKey.getProductType(),
                routingKey.getProductFirstCategoryId(),
                routingKey.getProductSecondCategoryId(),
                routingKey.getProductThirdCategoryId());
    }

    /**
     * 获取配置模板的描述信息
     */
    public static String getConfigTemplateDescription(PageConfigTemplateDTO template) {
        if (template == null) {
            return "null config template";
        }
        int ruleCount = template.getOptionalRules() != null ? template.getOptionalRules().size() : 0;
        boolean hasMainConfig = template.getMainConfigData() != null;
        return String.format("ConfigTemplate[hasMainConfig=%s, optionalRulesCount=%d]", hasMainConfig, ruleCount);
    }

    /**
     * 获取页面配置的描述信息
     */
    public static String getPageConfigDescription(PageConfigDataDTO config) {
        if (config == null) {
            return "null page config";
        }
        int groupCount = config.getModuleGroupConfigs() != null ? config.getModuleGroupConfigs().size() : 0;
        return String.format("PageConfig[moduleGroupCount=%d]", groupCount);
    }

    /**
     * 获取路由key集合的描述信息
     */
    public static String getRoutingKeySetDescription(Set<String> routingKeys) {
        if (routingKeys == null) {
            return "null routing key set";
        }
        return String.format("RoutingKeySet[size=%d]", routingKeys.size());
    }

    /**
     * 比较两个路由key是否相等
     */
    public static boolean areRoutingKeysEqual(PageConfigRoutingKey key1, PageConfigRoutingKey key2) {
        if (key1 == null && key2 == null) {
            return true;
        }
        if (key1 == null || key2 == null) {
            return false;
        }
        return Objects.equals(key1.getScene(), key2.getScene())
                && key1.getProductType() == key2.getProductType()
                && key1.getProductFirstCategoryId() == key2.getProductFirstCategoryId()
                && key1.getProductSecondCategoryId() == key2.getProductSecondCategoryId()
                && key1.getProductThirdCategoryId() == key2.getProductThirdCategoryId()
                && Objects.equals(key1.getOptionalRoutingKeyMap(), key2.getOptionalRoutingKeyMap());
    }

    /**
     * 创建路由key的深拷贝
     */
    public static PageConfigRoutingKey copyRoutingKey(PageConfigRoutingKey original) {
        if (original == null) {
            return null;
        }
        PageConfigRoutingKey copy = new PageConfigRoutingKey();
        copy.setScene(original.getScene());
        copy.setProductType(original.getProductType());
        copy.setProductFirstCategoryId(original.getProductFirstCategoryId());
        copy.setProductSecondCategoryId(original.getProductSecondCategoryId());
        copy.setProductThirdCategoryId(original.getProductThirdCategoryId());

        if (original.getOptionalRoutingKeyMap() != null) {
            copy.setOptionalRoutingKeyMap(new HashMap<>(original.getOptionalRoutingKeyMap()));
        }

        return copy;
    }
}
