package com.sankuai.dz.product.detail.gateway.domain.message.gray;

import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.message.dto.MessageSendGrayDTO;
import org.springframework.stereotype.Component;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2025/3/25
 */
@Component
public class MessageSendGrayService {
    public boolean hit(UnifiedPageRequest request) {
        MessageSendGrayDTO grayDTO = Lion.getBean(
                "com.sankuai.dzshoppingguide.detail.gateway", "message.send.gray.config", MessageSendGrayDTO.class
        );
        if (grayDTO == null) {
            return false;
        }

        // 灰度关闭
        if (grayDTO.getGrayLevel() == 0) {
            return false;
        }

        // 灰度全量
        if (grayDTO.getGrayLevel() == 2) {
            return true;
        }

        long productId = request.getProductId();
        String unionId = request.getHeaderMap().get("unionId");
        // 命中productId或者Iuniond白名单
        if (grayDTO.getProductIdWhiteList().contains(productId) || grayDTO.getUnionIdWhiteList().contains(unionId)) {
            return true;
        }

        // 灰度比例
        if (productId % 100 < grayDTO.getRatio()) {
            return true;
        }

        return false;
    }
}
