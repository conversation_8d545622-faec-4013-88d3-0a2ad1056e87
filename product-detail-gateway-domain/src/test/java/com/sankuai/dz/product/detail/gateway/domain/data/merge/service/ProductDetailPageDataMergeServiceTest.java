package com.sankuai.dz.product.detail.gateway.domain.data.merge.service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.dto.DataMergeRequest;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageDataMergeServiceTest {

    @InjectMocks
    private ProductDetailPageDataMergeService productDetailPageDataMergeService;

    @Mock
    private ModulePositionModifyService modulePositionModifyService;

    @Mock
    private CommonDataMergeService commonDataMergeService;

    private DataMergeRequest request;

    private Map<String, GenericModuleResponse> moduleResponse;

    private List<ABResultDTO> abResultList;

    @Before
    public void setUp() {
        request = new DataMergeRequest();
        moduleResponse = new HashMap<>();
        abResultList = new ArrayList<>();
        request.setModuleResponse(moduleResponse);
        request.setAbResultList(abResultList);
        // Initialize other required fields here if necessary
    }

    @Test
    public void testMergeNormal() throws Throwable {
        // Ensure all required fields are initialized
        // Assuming a constructor or setters are available
        request.setSpiRequest(new ProductDetailPageRequest());
        // Assuming a constructor or setters are available
        request.setPageConfigData(new PageConfigDataDTO());
        when(commonDataMergeService.merge(any())).thenReturn(new JSONObject());
        doNothing().when(modulePositionModifyService).execute(any(), any());
        ProductDetailPageMergeResponse response = productDetailPageDataMergeService.merge(request);
        assertNotNull(response);
        verify(commonDataMergeService, times(1)).merge(any());
        verify(modulePositionModifyService, times(1)).execute(any(), any());
    }

    @Test(expected = RuntimeException.class)
    public void testMergeRequestIsNull() throws Throwable {
        request = null;
        productDetailPageDataMergeService.merge(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMergeRequestParamNotSet() throws Throwable {
        request.setModuleResponse(null);
        productDetailPageDataMergeService.merge(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMergeBuildLayoutThrowsException() throws Throwable {
        productDetailPageDataMergeService.merge(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMergeBuildDataThrowsException() throws Throwable {
        productDetailPageDataMergeService.merge(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMergeBuildABResultThrowsException() throws Throwable {
        productDetailPageDataMergeService.merge(request);
    }

    @Test(expected = RuntimeException.class)
    public void testMergeExecuteThrowsException() throws Throwable {
        productDetailPageDataMergeService.merge(request);
    }
}
