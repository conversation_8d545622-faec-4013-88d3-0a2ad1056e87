package com.sankuai.dz.product.detail.gateway.api.page.type.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
@TypeDoc(description = "商品页面信息请求")
public class PageTypeRequest implements Serializable {

    @FieldDoc(description = "商品id", rule = "根据productType传对应商品id，团购id区分平台", requiredness = Requiredness.REQUIRED)
    private long productId;

    @FieldDoc(description = "HTTP Headers", requiredness = Requiredness.OPTIONAL)
    private Map<String, String> headerMap;

}
