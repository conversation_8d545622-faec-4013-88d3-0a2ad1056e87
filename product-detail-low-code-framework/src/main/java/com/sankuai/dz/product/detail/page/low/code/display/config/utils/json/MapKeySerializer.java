package com.sankuai.dz.product.detail.page.low.code.display.config.utils.json;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.lang.reflect.Modifier;


/**
 * Created by qi.yin on 17/12/5.
 */
public class MapKeySerializer extends JsonSerializer {


    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        String keyString = JacksonUtils.serialize(value);
        boolean isFinal = Modifier.isFinal(value.getClass().getModifiers());

        StringBuilder key = new StringBuilder();
        if (isFinal) {

            key.append("{\"").append(JacksonUtils.CLASS_KEY).append("\":\"").append(value.getClass().getName()).append("\",");

            if (value instanceof Integer || value instanceof Boolean
                    || value instanceof Byte || value instanceof Short
                    || value instanceof Long || value instanceof Float
                    || value instanceof Double || value instanceof String) {
                key.append(JacksonUtils.BASE_VALUE_KEY).append(":").append(keyString);
            } else if (value instanceof Enum) {
                key.append(JacksonUtils.BASE_VALUE_KEY).append(":").append(keyString);
            } else if (value.getClass().isArray()) {
                key.append(JacksonUtils.BASE_VALUE_KEY).append(":").append(keyString);
            } else if (keyString.startsWith("{")) {
                key.append(keyString.substring(1, keyString.length() - 1));
            } else {
                key.append(keyString);
            }

            key.append("}");
            keyString = key.toString();
        }

        gen.writeFieldName(keyString);
    }
}
