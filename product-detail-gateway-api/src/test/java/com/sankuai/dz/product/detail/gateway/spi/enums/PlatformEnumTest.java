package com.sankuai.dz.product.detail.gateway.spi.enums;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.junit.Test;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.Map;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for PlatformEnum.containsCode method.
 */
@RunWith(MockitoJUnitRunner.class)
public class PlatformEnumTest {

    @Mock
    private Map<Integer, String> mockMap;

    /**
     * Test to verify that containsCode returns true when the code exists in the enum.
     */
    @Test
    public void testContainsCodeWithExistingCode() {
        // arrange
        // Assuming 1 is a code for DP based on the provided class structure
        int existingCode = 1;
        // act
        boolean result = PlatformEnum.containsCode(existingCode);
        // assert
        assertTrue("Expected to find the code in the enum", result);
    }

    /**
     * Test to verify that containsCode returns false when the code does not exist in the enum.
     */
    @Test
    public void testContainsCodeWithNonExistingCode() {
        // arrange
        // Assuming 99 is not a code for any enum value
        int nonExistingCode = 99;
        // act
        boolean result = PlatformEnum.containsCode(nonExistingCode);
        // assert
        assertFalse("Expected not to find the code in the enum", result);
    }

    /**
     * Test to verify that containsCode handles the lowest boundary of integer values.
     */
    @Test
    public void testContainsCodeWithMinimumIntegerValue() {
        // arrange
        int minIntValue = Integer.MIN_VALUE;
        // act
        boolean result = PlatformEnum.containsCode(minIntValue);
        // assert
        assertFalse("Expected not to find the minimum integer value in the enum", result);
    }

    /**
     * Test to verify that containsCode handles the highest boundary of integer values.
     */
    @Test
    public void testContainsCodeWithMaximumIntegerValue() {
        // arrange
        int maxIntValue = Integer.MAX_VALUE;
        // act
        boolean result = PlatformEnum.containsCode(maxIntValue);
        // assert
        assertFalse("Expected not to find the maximum integer value in the enum", result);
    }

    @Test
    public void testBuildNameDescMapReturnsCorrectMapping() throws Throwable {
        // arrange
        // 不需要特殊准备
        // act
        Map<String, String> result = PlatformEnum.buildNameDescMap();
        // assert
        assertNotNull("返回的Map不应为null", result);
        assertEquals("Map大小应为4", 4, result.size());
        assertEquals("UNKNOWN映射不正确", "未知", result.get("UNKNOWN"));
        assertEquals("DP映射不正确", "点评", result.get("DP"));
        assertEquals("MT映射不正确", "美团", result.get("MT"));
        assertEquals("KAI_DIAN_BAO映射不正确", "开店宝B端", result.get("KAI_DIAN_BAO"));
    }

    @Test
    public void testBuildNameDescMapReturnsUnmodifiableMap() throws Throwable {
        // arrange
        // 不需要特殊准备
        // act
        Map<String, String> result = PlatformEnum.buildNameDescMap();
        // assert
        assertTrue("Map应包含UNKNOWN键", result.containsKey("UNKNOWN"));
        assertTrue("Map应包含DP键", result.containsKey("DP"));
        assertTrue("Map应包含MT键", result.containsKey("MT"));
        assertTrue("Map应包含KAI_DIAN_BAO键", result.containsKey("KAI_DIAN_BAO"));
    }

    @Test
    public void testBuildNameDescMapDoesNotContainInvalidEntries() throws Throwable {
        // arrange
        // 不需要特殊准备
        // act
        Map<String, String> result = PlatformEnum.buildNameDescMap();
        // assert
        assertFalse("Map不应包含无效key", result.containsKey("INVALID"));
        assertFalse("Map不应包含无效value", result.containsValue("无效描述"));
    }

    @Test
    public void testBuildNameDescMapWithMockedValues() throws Throwable {
        // arrange
        // 不需要mock，直接使用实际枚举值
        // act
        Map<String, String> result = PlatformEnum.buildNameDescMap();
        // assert
        for (PlatformEnum platform : PlatformEnum.values()) {
            assertEquals(String.format("%s的描述映射不正确", platform.name()), platform.getDesc(), result.get(platform.name()));
        }
    }

    @Test
    public void testFromCode_CodeEqualsEnumCode() {
        // arrange
        int code = 1;
        // act
        PlatformEnum result = PlatformEnum.fromCode(code);
        // assert
        assertEquals(PlatformEnum.DP, result);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_CodeNotEqualsAnyEnumCode() {
        // arrange
        int code = 4;
        // act
        PlatformEnum.fromCode(code);
        // assert is in the annotation
    }

    @Test
    public void testBuildCodeDescMapReturnsNonNull() throws Throwable {
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertNotNull("Returned map should not be null", result);
    }

    @Test
    public void testBuildCodeDescMapReturnsCorrectSize() throws Throwable {
        // arrange
        int expectedSize = PlatformEnum.values().length;
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertEquals("Map size should match enum values count", expectedSize, result.size());
    }

    @Test
    public void testBuildCodeDescMapContainsAllEnumEntries() throws Throwable {
        // arrange
        PlatformEnum[] values = PlatformEnum.values();
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        for (PlatformEnum value : values) {
            assertTrue("Map should contain enum code: " + value.getCode(), result.containsKey(value.getCode()));
            assertEquals("Map should have correct desc for code: " + value.getCode(), value.getDesc(), result.get(value.getCode()));
        }
    }

    @Test
    public void testBuildCodeDescMapUnknownMapping() throws Throwable {
        // arrange
        int unknownCode = PlatformEnum.UNKNOWN.getCode();
        String expectedDesc = PlatformEnum.UNKNOWN.getDesc();
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertEquals("UNKNOWN platform should have correct description", expectedDesc, result.get(unknownCode));
    }

    @Test
    public void testBuildCodeDescMapDPMapping() throws Throwable {
        // arrange
        int dpCode = PlatformEnum.DP.getCode();
        String expectedDesc = PlatformEnum.DP.getDesc();
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertEquals("DP platform should have correct description", expectedDesc, result.get(dpCode));
    }

    @Test
    public void testBuildCodeDescMapMTMapping() throws Throwable {
        // arrange
        int mtCode = PlatformEnum.MT.getCode();
        String expectedDesc = PlatformEnum.MT.getDesc();
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertEquals("MT platform should have correct description", expectedDesc, result.get(mtCode));
    }

    @Test
    public void testBuildCodeDescMapKaiDianBaoMapping() throws Throwable {
        // arrange
        int kdbCode = PlatformEnum.KAI_DIAN_BAO.getCode();
        String expectedDesc = PlatformEnum.KAI_DIAN_BAO.getDesc();
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertEquals("KAI_DIAN_BAO platform should have correct description", expectedDesc, result.get(kdbCode));
    }

    @Test
    public void testBuildCodeDescMapNonExistentCode() throws Throwable {
        // arrange
        int nonExistentCode = 999;
        // act
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        // assert
        assertNull("Non-existent code should return null", result.get(nonExistentCode));
    }

    @Test
    public void testBuildCodeDescMapModification() throws Throwable {
        // arrange
        Map<Integer, String> result = PlatformEnum.buildCodeDescMap();
        int initialSize = result.size();
        // act
        result.put(999, "Test");
        // assert
        assertEquals("Map size should be increased by 1", initialSize + 1, result.size());
        assertEquals("New value should be retrievable", "Test", result.get(999));
    }
}
