package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.tab;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.TabItemTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleItemVO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 20:40
 */
@Data
@TypeDoc(description = "tab项")
public class TabModuleItemDTO implements Serializable {

    @FieldDoc(description = "tab的唯一标识", requiredness = Requiredness.REQUIRED)
    private String tabKey;

    @FieldDoc(description = "tab名称，for展示", requiredness = Requiredness.REQUIRED)
    private String tabName;

    @FieldDoc(description = "tab类型", requiredness = Requiredness.REQUIRED)
    private TabItemTypeEnum tabType;

    @FieldDoc(description = "tab内的group，每个tab项必须是group类型", requiredness = Requiredness.REQUIRED)
    private List<ModuleGroupConfigDTO> groupConfig;

    public TabModuleItemVO buildVO(final BuildModuleVORequest request, final int depth) {
        TabModuleItemVO vo = new TabModuleItemVO();
        vo.setTabKey(tabKey);
        vo.setTabName(tabName);
        vo.setTabType(tabType.name());
        vo.setModuleList(
                groupConfig.stream()
                        .map(ModuleGroupConfigDTO::getModuleList)
                        .map(moduleList -> moduleList.stream()
                                .map(module -> module.buildVO(request, depth + 1))
                                .collect(Collectors.toList())
                        ).collect(Collectors.toList())
        );
        return vo;
    }

}
