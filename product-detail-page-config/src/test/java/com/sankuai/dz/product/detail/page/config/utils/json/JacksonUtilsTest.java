package com.sankuai.dz.product.detail.page.config.utils.json;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class JacksonUtilsTest {

    /**
     * 测试正常情况 - 有效JSON字符串
     */
    @Test
    void testReadNode_WithValidJson_ShouldReturnJsonNode() throws Throwable {
        // arrange
        String validJson = "{\"key\":\"value\"}";
        // act
        JsonNode result = JacksonUtils.readNode(validJson);
        // assert
        assertNotNull(result);
        assertEquals("value", result.get("key").asText());
    }

    /**
     * 测试异常情况 - 无效JSON字符串
     */
    @Test
    void testReadNode_WithInvalidJson_ShouldThrowRuntimeException() throws Throwable {
        // arrange
        String invalidJson = "{invalid}";
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> JacksonUtils.readNode(invalidJson));
        assertTrue(exception.getCause() instanceof JsonProcessingException);
    }

    /**
     * 测试边界情况 - 空字符串
     */
    @Test
    void testReadNode_WithEmptyString_ShouldReturnEmptyNode() throws Throwable {
        // arrange
        String emptyJson = "";
        // act
        JsonNode result = JacksonUtils.readNode(emptyJson);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试边界情况 - null输入
     */
    @Test
    void testReadNode_WithNullInput_ShouldThrowIllegalArgumentException() throws Throwable {
        // arrange
        String nullJson = null;
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> JacksonUtils.readNode(nullJson));
    }

    /**
     * 测试边界情况 - 空白字符串
     */
    @Test
    void testReadNode_WithWhitespaceString_ShouldReturnEmptyNode() throws Throwable {
        // arrange
        String whitespaceJson = "   ";
        // act
        JsonNode result = JacksonUtils.readNode(whitespaceJson);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试边界情况 - 只包含空对象的JSON
     */
    @Test
    void testReadNode_WithEmptyObject_ShouldReturnEmptyObjectNode() throws Throwable {
        // arrange
        String emptyObjectJson = "{}";
        // act
        JsonNode result = JacksonUtils.readNode(emptyObjectJson);
        // assert
        assertNotNull(result);
        assertTrue(result.isObject());
        assertEquals(0, result.size());
    }

    static class SourceClass {

        private String name;

        private int age;

        public SourceClass(String name, int age) {
            this.name = name;
            this.age = age;
        }

        public String getName() {
            return name;
        }

        public int getAge() {
            return age;
        }
    }

    static class TargetClass {

        private String name;

        private int age;

        public String getName() {
            return name;
        }

        public int getAge() {
            return age;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }

    @Test
    public void testCopyNormalCase() {
        // arrange
        TestData original = new TestData("test", 123);
        // act
        TestData copied = JacksonUtils.copy(original, TestData.class);
        // assert
        assertNotNull(copied);
        assertEquals(original.getName(), copied.getName());
        assertEquals(original.getValue(), copied.getValue());
        assertNotSame(original, copied);
    }

    @Test
    public void testCopyWithNullInput() {
        // arrange
        TestData original = null;
        // act
        TestData copied = JacksonUtils.copy(original, TestData.class);
        // assert
        assertNull(copied);
    }

    static class TestData {

        private String name;

        private int value;

        public TestData() {
        }

        public TestData(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }

    @Test
    public void testDeserialize_WhenJsonStringIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        String jsonString = null;
        // act
        TestDto result = JacksonUtils.deserialize(jsonString, TestDto.class);
        // assert
        assertNull(result);
    }

    @Test
    public void testDeserialize_WhenJsonStringIsValid_ShouldReturnDeserializedObject() throws Throwable {
        // arrange
        String jsonString = String.format("{\"@class\":\"%s\",\"stringField\":\"test\",\"intField\":123}", TestDto.class.getName());
        // act
        TestDto result = JacksonUtils.deserialize(jsonString, TestDto.class);
        // assert
        assertNotNull(result);
        assertEquals("test", result.getStringField());
        assertEquals(123, result.getIntField());
    }

    @Test
    public void testDeserialize_WhenJsonStringIsEmptyObject_ShouldReturnEmptyObject() throws Throwable {
        // arrange
        String jsonString = String.format("{\"@class\":\"%s\"}", TestDto.class.getName());
        // act
        TestDto result = JacksonUtils.deserialize(jsonString, TestDto.class);
        // assert
        assertNotNull(result);
        assertNull(result.getStringField());
        assertEquals(0, result.getIntField());
    }

    @Test
    public void testDeserialize_WhenJsonStringIsInvalid_ShouldThrowRuntimeException() throws Throwable {
        // arrange
        String jsonString = "invalid json";
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> JacksonUtils.deserialize(jsonString, TestDto.class));
        assertTrue(exception.getCause() instanceof com.fasterxml.jackson.core.JsonProcessingException);
    }

    @Test
    public void testDeserialize_WhenDeserializingPrimitiveType_ShouldReturnValue() throws Throwable {
        // arrange
        String jsonString = "42";
        // act
        Integer result = JacksonUtils.deserialize(jsonString, Integer.class);
        // assert
        assertNotNull(result);
        assertEquals(42, result);
    }

    @Test
    public void testDeserialize_WhenDeserializingString_ShouldReturnString() throws Throwable {
        // arrange
        String jsonString = "\"test string\"";
        // act
        String result = JacksonUtils.deserialize(jsonString, String.class);
        // assert
        assertEquals("test string", result);
    }

    public static class TestDto {

        private String stringField;

        private int intField;

        public String getStringField() {
            return stringField;
        }

        public void setStringField(String stringField) {
            this.stringField = stringField;
        }

        public int getIntField() {
            return intField;
        }

        public void setIntField(int intField) {
            this.intField = intField;
        }
    }

    @Test
    public void testSerializePrimitiveTypes() throws Throwable {
        // 测试整数
        assertEquals("123", JacksonUtils.serialize(123));
        // 测试浮点数
        assertEquals("123.45", JacksonUtils.serialize(123.45));
        // 测试字符串
        assertEquals("\"test\"", JacksonUtils.serialize("test"));
        // 测试布尔值
        assertEquals("true", JacksonUtils.serialize(true));
        assertEquals("false", JacksonUtils.serialize(false));
    }

    @Test
    public void testSerializeNull() throws Throwable {
        assertEquals("null", JacksonUtils.serialize(null));
    }

    @Test
    public void testSerializeSimpleObject() throws Throwable {
        // arrange
        SimpleTestObject obj = new SimpleTestObject();
        obj.setName("test");
        obj.setValue(100);
        // act
        String result = JacksonUtils.serialize(obj);
        System.out.println("Simple object result: " + result);
        // assert
        assertTrue(result.contains("SimpleTestObject"));
        assertTrue(result.contains("\"name\":\"test\""));
        assertTrue(result.contains("\"value\":100"));
    }

    @Test
    public void testSerializeMap() throws Throwable {
        // arrange
        Map<String, Object> map = new HashMap<>();
        map.put("key1", "value1");
        map.put("key2", 123);
        // act
        String result = JacksonUtils.serialize(map);
        System.out.println("Map result: " + result);
        // assert
        assertTrue(result.contains("HashMap"));
        assertTrue(result.contains("key1"));
        assertTrue(result.contains("value1"));
        assertTrue(result.contains("key2"));
        assertTrue(result.contains("123"));
    }

    @Test
    public void testSerializeList() throws Throwable {
        // arrange
        List<String> list = new ArrayList<>();
        list.add("item1");
        list.add("item2");
        // act
        String result = JacksonUtils.serialize(list);
        System.out.println("List result: " + result);
        // assert
        assertTrue(result.contains("ArrayList"));
        assertTrue(result.contains("item1"));
        assertTrue(result.contains("item2"));
    }

    @Test
    public void testSerializeComplexObject() throws Throwable {
        // arrange
        ComplexTestObject obj = new ComplexTestObject();
        obj.setId(1L);
        obj.setName("test");
        Map<String, Integer> attributes = new HashMap<>();
        attributes.put("key1", 100);
        attributes.put("key2", 200);
        obj.setAttributes(attributes);
        List<String> tags = new ArrayList<>();
        tags.add("tag1");
        tags.add("tag2");
        obj.setTags(tags);
        // act
        String result = JacksonUtils.serialize(obj);
        System.out.println("Complex object result: " + result);
        // assert
        assertTrue(result.contains("ComplexTestObject"));
        assertTrue(result.contains("\"id\":1"));
        assertTrue(result.contains("\"name\":\"test\""));
        assertTrue(result.contains("key1"));
        assertTrue(result.contains("key2"));
        assertTrue(result.contains("tag1"));
        assertTrue(result.contains("tag2"));
    }

    @Test
    public void testSerializeWithException() throws Throwable {
        // arrange
        Object problematicObject = new Object() {

            @Override
            public String toString() {
                throw new RuntimeException("Simulated error");
            }
        };
        // act & assert
        assertThrows(RuntimeException.class, () -> JacksonUtils.serialize(problematicObject));
    }

    private static class SimpleTestObject {

        private String name;

        private int value;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }

    private static class ComplexTestObject {

        private Long id;

        private String name;

        private Map<String, Integer> attributes;

        private List<String> tags;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Map<String, Integer> getAttributes() {
            return attributes;
        }

        public void setAttributes(Map<String, Integer> attributes) {
            this.attributes = attributes;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }
    }
}
