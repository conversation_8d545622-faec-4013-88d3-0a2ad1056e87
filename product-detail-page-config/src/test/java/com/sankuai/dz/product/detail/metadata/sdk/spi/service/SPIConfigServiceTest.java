package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler.SPICallHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SPIConfigService 单元测试
 * 目标覆盖率: >70%
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 22:00
 */
@RunWith(MockitoJUnitRunner.class)
public class SPIConfigServiceTest {

    @InjectMocks
    private SPIConfigService spiConfigService;

    @Mock
    private SPICallFactory spiCallFactory;

    @Mock
    private SPICallHandler mockHandler;

    private String validConfigJson;
    private Map<String, SPIConfigDTO> validConfigMap;

    @Before
    public void setUp() {
        // 创建有效的配置JSON
        validConfigMap = new HashMap<>();
        SPIConfigDTO config1 = new SPIConfigDTO("test-alias-1", InterfaceTypeEnum.PIGEON, "com.test.Service1");
        SPIConfigDTO config2 = new SPIConfigDTO("test-alias-2", InterfaceTypeEnum.PIGEON, "com.test.Service2");
        validConfigMap.put("test-alias-1", config1);
        validConfigMap.put("test-alias-2", config2);
        
        validConfigJson = JSONObject.toJSONString(validConfigMap);

        // 模拟 SPICallHandler 行为
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));
    }

    /**
     * 测试 getSPIConfigDTO 方法 - 正常场景
     */
    @Test
    public void testGetSPIConfigDTOSuccess() {
        // 先初始化配置
        initializeConfigWithValidJson();

        // 执行测试
        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("test-alias-1");
        SPIConfigDTO result2 = spiConfigService.getSPIConfigDTO("test-alias-2");
        SPIConfigDTO result3 = spiConfigService.getSPIConfigDTO("non-existent");

        // 验证结果
        assertNotNull("应该返回配置1", result1);
        assertEquals("配置1的别名应该正确", "test-alias-1", result1.getInterfaceAlias());
        assertEquals("配置1的类型应该正确", InterfaceTypeEnum.PIGEON, result1.getInterfaceType());
        assertEquals("配置1的URL应该正确", "com.test.Service1", result1.getInterfaceUrl());

        assertNotNull("应该返回配置2", result2);
        assertEquals("配置2的别名应该正确", "test-alias-2", result2.getInterfaceAlias());

        assertNull("不存在的别名应该返回null", result3);
    }

    /**
     * 测试 getSPIConfigDTO 方法 - 空别名
     */
    @Test
    public void testGetSPIConfigDTOWithNullAlias() {
        // 先初始化配置
        initializeConfigWithValidJson();

        // 执行测试
        SPIConfigDTO result = spiConfigService.getSPIConfigDTO(null);

        // 验证结果
        assertNull("null别名应该返回null", result);
    }

    /**
     * 测试 afterPropertiesSet 方法 - 正常场景
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Exception {
        try (MockedStatic<Environment> environmentMock = mockStatic(Environment.class);
             MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {

            // 模拟 Environment.getAppName()
            environmentMock.when(Environment::getAppName).thenReturn("test-app");

            // 模拟 Lion.getString()
            lionMock.when(() -> Lion.getString(eq("test-app"), anyString())).thenReturn(validConfigJson);

            // 模拟 Lion.addConfigListener()
            lionMock.when(() -> Lion.addConfigListener(anyString(), any())).thenAnswer(invocation -> null);

            // 执行测试
            spiConfigService.afterPropertiesSet();

            // 验证调用
            environmentMock.verify(Environment::getAppName, times(1));
            lionMock.verify(() -> Lion.getString(eq("test-app"), anyString()), times(1));
            lionMock.verify(() -> Lion.addConfigListener(anyString(), any()), times(1));

            // 验证配置已加载
            SPIConfigDTO result = spiConfigService.getSPIConfigDTO("test-alias-1");
            assertNotNull("配置应该已加载", result);
        }
    }

    /**
     * 测试 initSPIConfig 方法 - 空配置JSON
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testInitSPIConfigWithEmptyJson() {
        // 执行测试，期望抛出异常
        invokeInitSPIConfig("{}");
    }

    /**
     * 测试 initSPIConfig 方法 - null配置JSON
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testInitSPIConfigWithNullJson() {
        // 执行测试，期望抛出异常
        invokeInitSPIConfig(null);
    }

    /**
     * 测试 initSPIConfig 方法 - 无效JSON格式
     */
    @Test(expected = RuntimeException.class)
    public void testInitSPIConfigWithInvalidJson() {
        // 执行测试，期望抛出异常
        invokeInitSPIConfig("invalid json format");
    }

    /**
     * 测试 initSPIConfig 方法 - 空的SPI服务集合
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testInitSPIConfigWithEmptySPIMap() {
        // 创建配置，但模拟handler返回空的spiMap
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(null);

        // 执行测试，期望抛出异常
        invokeInitSPIConfig(validConfigJson);
    }

    /**
     * 测试 initSPIConfig 方法 - handler抛出异常
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testInitSPIConfigWithHandlerException() {
        // 模拟handler抛出异常
        when(mockHandler.getSpiService(any(SPIConfigDTO.class)))
                .thenThrow(new SPIConfigFatalException("Handler异常"));

        // 执行测试，期望抛出异常
        invokeInitSPIConfig(validConfigJson);
    }

    /**
     * 测试 initSPIConfig 方法 - refreshSPIMap抛出异常
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testInitSPIConfigWithRefreshException() {
        // 模拟refreshSPIMap抛出异常
        doThrow(new SPIConfigFatalException("刷新异常")).when(mockHandler).refreshSPIMap(any(Map.class));

        // 执行测试，期望抛出异常
        invokeInitSPIConfig(validConfigJson);
    }

    /**
     * 测试 initSPIConfig 方法 - 通用异常
     */
    @Test(expected = RuntimeException.class)
    public void testInitSPIConfigWithGeneralException() {
        // 模拟通用异常
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class)))
                .thenThrow(new RuntimeException("通用异常"));

        // 执行测试，期望抛出异常
        invokeInitSPIConfig(validConfigJson);
    }

    /**
     * 测试多种接口类型的配置
     */
    @Test
    public void testInitSPIConfigWithMultipleInterfaceTypes() {
        // 创建包含多种接口类型的配置（虽然目前只有PIGEON类型）
        Map<String, SPIConfigDTO> multiTypeConfig = new HashMap<>();
        SPIConfigDTO pigeonConfig = new SPIConfigDTO("pigeon-alias", InterfaceTypeEnum.PIGEON, "com.test.PigeonService");
        multiTypeConfig.put("pigeon-alias", pigeonConfig);

        String multiTypeJson = JSONObject.toJSONString(multiTypeConfig);

        // 执行测试
        invokeInitSPIConfig(multiTypeJson);

        // 验证配置已加载
        SPIConfigDTO result = spiConfigService.getSPIConfigDTO("pigeon-alias");
        assertNotNull("PIGEON配置应该已加载", result);
        assertEquals("接口类型应该正确", InterfaceTypeEnum.PIGEON, result.getInterfaceType());

        // 验证handler被调用
        verify(spiCallFactory, atLeastOnce()).getHandler(InterfaceTypeEnum.PIGEON);
        verify(mockHandler, atLeastOnce()).getSpiService(any(SPIConfigDTO.class));
        verify(mockHandler, atLeastOnce()).refreshSPIMap(any(Map.class));
    }

    /**
     * 测试配置更新场景
     */
    @Test
    public void testConfigUpdate() {
        // 初始化第一次配置
        initializeConfigWithValidJson();

        // 验证初始配置
        SPIConfigDTO initialConfig = spiConfigService.getSPIConfigDTO("test-alias-1");
        assertNotNull("初始配置应该存在", initialConfig);

        // 创建更新后的配置
        Map<String, SPIConfigDTO> updatedConfigMap = new HashMap<>();
        SPIConfigDTO updatedConfig = new SPIConfigDTO("test-alias-1", InterfaceTypeEnum.PIGEON, "com.test.UpdatedService");
        SPIConfigDTO newConfig = new SPIConfigDTO("test-alias-3", InterfaceTypeEnum.PIGEON, "com.test.NewService");
        updatedConfigMap.put("test-alias-1", updatedConfig);
        updatedConfigMap.put("test-alias-3", newConfig);

        String updatedJson = JSONObject.toJSONString(updatedConfigMap);

        // 重置mock
        reset(spiCallFactory, mockHandler);
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        // 执行配置更新
        invokeInitSPIConfig(updatedJson);

        // 验证更新后的配置
        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("test-alias-1");
        SPIConfigDTO result2 = spiConfigService.getSPIConfigDTO("test-alias-2");
        SPIConfigDTO result3 = spiConfigService.getSPIConfigDTO("test-alias-3");

        assertNotNull("更新的配置应该存在", result1);
        assertEquals("配置URL应该已更新", "com.test.UpdatedService", result1.getInterfaceUrl());
        assertNull("旧配置应该不存在", result2);
        assertNotNull("新配置应该存在", result3);
        assertEquals("新配置URL应该正确", "com.test.NewService", result3.getInterfaceUrl());
    }

    /**
     * 测试大量配置的处理
     */
    @Test
    public void testInitSPIConfigWithLargeConfigSet() {
        // 创建大量配置
        Map<String, SPIConfigDTO> largeConfigMap = new HashMap<>();
        for (int i = 1; i <= 100; i++) {
            SPIConfigDTO config = new SPIConfigDTO("alias-" + i, InterfaceTypeEnum.PIGEON, "com.test.Service" + i);
            largeConfigMap.put("alias-" + i, config);
        }

        String largeConfigJson = JSONObject.toJSONString(largeConfigMap);

        // 执行测试
        invokeInitSPIConfig(largeConfigJson);

        // 验证部分配置
        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("alias-1");
        SPIConfigDTO result50 = spiConfigService.getSPIConfigDTO("alias-50");
        SPIConfigDTO result100 = spiConfigService.getSPIConfigDTO("alias-100");

        assertNotNull("配置1应该存在", result1);
        assertNotNull("配置50应该存在", result50);
        assertNotNull("配置100应该存在", result100);

        assertEquals("配置1的URL应该正确", "com.test.Service1", result1.getInterfaceUrl());
        assertEquals("配置50的URL应该正确", "com.test.Service50", result50.getInterfaceUrl());
        assertEquals("配置100的URL应该正确", "com.test.Service100", result100.getInterfaceUrl());

        // 验证handler被调用了正确的次数
        verify(mockHandler, times(100)).getSpiService(any(SPIConfigDTO.class));
        verify(mockHandler, times(InterfaceTypeEnum.values().length)).refreshSPIMap(any(Map.class));
    }

    /**
     * 测试特殊字符的配置
     */
    @Test
    public void testInitSPIConfigWithSpecialCharacters() {
        // 创建包含特殊字符的配置
        Map<String, SPIConfigDTO> specialConfigMap = new HashMap<>();
        SPIConfigDTO config = new SPIConfigDTO("test-alias-特殊字符_123", InterfaceTypeEnum.PIGEON, "com.test.特殊Service");
        specialConfigMap.put("test-alias-特殊字符_123", config);

        String specialConfigJson = JSONObject.toJSONString(specialConfigMap);

        // 执行测试
        invokeInitSPIConfig(specialConfigJson);

        // 验证配置
        SPIConfigDTO result = spiConfigService.getSPIConfigDTO("test-alias-特殊字符_123");
        assertNotNull("特殊字符配置应该存在", result);
        assertEquals("特殊字符URL应该正确", "com.test.特殊Service", result.getInterfaceUrl());
    }

    /**
     * 辅助方法：初始化配置
     */
    private void initializeConfigWithValidJson() {
        invokeInitSPIConfig(validConfigJson);
    }

    /**
     * 辅助方法：通过反射调用私有的 initSPIConfig 方法
     */
    private void invokeInitSPIConfig(String json) {
        try {
            java.lang.reflect.Method method = SPIConfigService.class.getDeclaredMethod("initSPIConfig", String.class);
            method.setAccessible(true);
            method.invoke(spiConfigService, json);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }
}
