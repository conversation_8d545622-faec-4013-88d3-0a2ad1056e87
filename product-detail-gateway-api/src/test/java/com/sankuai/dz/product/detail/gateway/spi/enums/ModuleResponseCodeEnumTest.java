package com.sankuai.dz.product.detail.gateway.spi.enums;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ModuleResponseCodeEnumTest {

    /**
     * 测试传入成功code(200)返回SUCCESS枚举
     */
    @Test
    public void testFromCode_WhenCodeIs200_ThenReturnSuccessEnum() {
        // arrange
        int successCode = 200;
        // act
        ModuleResponseCodeEnum result = ModuleResponseCodeEnum.fromCode(successCode);
        // assert
        assertNotNull(result);
        assertEquals(ModuleResponseCodeEnum.SUCCESS, result);
        assertEquals(successCode, result.getCode());
    }

    /**
     * 测试传入失败code(500)返回FAILURE枚举
     */
    @Test
    public void testFromCode_WhenCodeIs500_ThenReturnFailureEnum() {
        // arrange
        int failureCode = 500;
        // act
        ModuleResponseCodeEnum result = ModuleResponseCodeEnum.fromCode(failureCode);
        // assert
        assertNotNull(result);
        assertEquals(ModuleResponseCodeEnum.FAILURE, result);
        assertEquals(failureCode, result.getCode());
    }

    /**
     * 测试传入不存在的code抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeNotExist_ThenThrowException() {
        // arrange
        int notExistCode = 404;
        // act & assert
        ModuleResponseCodeEnum.fromCode(notExistCode);
    }

    /**
     * 测试传入非法code(负数)抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeIsNegative_ThenThrowException() {
        // arrange
        int invalidCode = -1;
        // act & assert
        ModuleResponseCodeEnum.fromCode(invalidCode);
    }

    /**
     * 测试传入非法code(0)抛出IllegalArgumentException异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeIsZero_ThenThrowException() {
        // arrange
        int invalidCode = 0;
        // act & assert
        ModuleResponseCodeEnum.fromCode(invalidCode);
    }

    @Test
    public void testContainsCodeWhenCodeMatchesSuccessValue() throws Throwable {
        // arrange
        int successCode = 200;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(successCode);
        // assert
        assertTrue("Should return true for SUCCESS code (200)", result);
    }

    @Test
    public void testContainsCodeWhenCodeMatchesFailureValue() throws Throwable {
        // arrange
        int failureCode = 500;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(failureCode);
        // assert
        assertTrue("Should return true for FAILURE code (500)", result);
    }

    @Test
    public void testContainsCodeWhenCodeDoesNotMatchAnyValue() throws Throwable {
        // arrange
        int nonExistingCode = 404;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(nonExistingCode);
        // assert
        assertFalse("Should return false for non-existing code (404)", result);
    }

    @Test
    public void testContainsCodeWithMinimumIntegerValue() throws Throwable {
        // arrange
        int minCode = Integer.MIN_VALUE;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(minCode);
        // assert
        assertFalse("Should return false for minimum integer value", result);
    }

    @Test
    public void testContainsCodeWithMaximumIntegerValue() throws Throwable {
        // arrange
        int maxCode = Integer.MAX_VALUE;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(maxCode);
        // assert
        assertFalse("Should return false for maximum integer value", result);
    }

    @Test
    public void testContainsCodeWithZeroValue() throws Throwable {
        // arrange
        int zeroCode = 0;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(zeroCode);
        // assert
        assertFalse("Should return false for zero code", result);
    }

    @Test
    public void testContainsCodeWithNegativeValue() throws Throwable {
        // arrange
        int negativeCode = -100;
        // act
        boolean result = ModuleResponseCodeEnum.containsCode(negativeCode);
        // assert
        assertFalse("Should return false for negative code", result);
    }

}
