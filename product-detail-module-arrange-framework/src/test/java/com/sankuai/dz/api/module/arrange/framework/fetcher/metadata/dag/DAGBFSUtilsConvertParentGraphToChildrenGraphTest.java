package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.when;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Queue;
import java.util.function.BiConsumer;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import java.util.AbstractMap.SimpleEntry;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DAGBFSUtilsConvertParentGraphToChildrenGraphTest {

    /**
     * 测试正常情况：简单DAG图转换
     */
    @Test
    @DisplayName("Should correctly convert parent graph to children graph for normal DAG")
    void testConvertParentGraphToChildrenGraph_NormalDAG() {
        // Arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", Collections.emptySet());
        parentGraph.put("B", new HashSet<>(Arrays.asList("A")));
        parentGraph.put("C", new HashSet<>(Arrays.asList("A")));
        parentGraph.put("D", new HashSet<>(Arrays.asList("B", "C")));
        // Act
        Map<String, Set<String>> result = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        // Assert
        assertNotNull(result, "Result should not be null");
        assertEquals(4, result.size(), "Should contain all nodes");
        assertIterableEquals(Arrays.asList("B", "C"), result.get("A"), "A's children should be B and C");
        assertIterableEquals(Collections.singletonList("D"), result.get("B"), "B's child should be D");
        assertIterableEquals(Collections.singletonList("D"), result.get("C"), "C's child should be D");
        assertTrue(result.get("D").isEmpty(), "D should have no children");
    }

    /**
     * 测试边界情况：空图转换
     */
    @Test
    @DisplayName("Should return empty map when input graph is empty")
    void testConvertParentGraphToChildrenGraph_EmptyGraph() {
        // Arrange
        Map<String, Set<String>> parentGraph = Collections.emptyMap();
        // Act
        Map<String, Set<String>> result = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        // Assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * 测试边界情况：单节点图转换
     */
    @Test
    @DisplayName("Should handle single node graph correctly")
    void testConvertParentGraphToChildrenGraph_SingleNode() {
        // Arrange
        Map<String, Set<String>> parentGraph = Collections.singletonMap("A", Collections.emptySet());
        // Act
        Map<String, Set<String>> result = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        // Assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Should contain exactly one node");
        assertTrue(result.get("A").isEmpty(), "Single node should have no children");
    }

    /**
     * 测试边界情况：包含孤立节点的图转换
     */
    @Test
    @DisplayName("Should correctly handle graph with isolated nodes")
    void testConvertParentGraphToChildrenGraph_WithIsolatedNodes() {
        // Arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", Collections.emptySet());
        parentGraph.put("B", Collections.emptySet());
        parentGraph.put("C", Collections.singleton("A"));
        parentGraph.put("D", Collections.singleton("B"));
        // Act
        Map<String, Set<String>> result = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        // Assert
        assertNotNull(result, "Result should not be null");
        assertEquals(4, result.size(), "Should contain all nodes");
        assertIterableEquals(Collections.singletonList("C"), result.get("A"), "A's child should be C");
        assertIterableEquals(Collections.singletonList("D"), result.get("B"), "B's child should be D");
        assertTrue(result.get("C").isEmpty(), "C should have no children");
        assertTrue(result.get("D").isEmpty(), "D should have no children");
    }

    /**
     * 测试异常情况：输入为null
     */
    @Test
    @DisplayName("Should throw NullPointerException when input is null")
    void testConvertParentGraphToChildrenGraph_NullInput() {
        // Arrange & Act & Assert
        assertThrows(NullPointerException.class, () -> DAGBFSUtils.convertParentGraphToChildrenGraph(null), "Should throw NullPointerException for null input");
    }

    /**
     * 测试循环验证：子图转父图再转回子图应保持不变
     */
    @Test
    @DisplayName("Should maintain original graph after round-trip conversion")
    void testConvertParentGraphToChildrenGraph_RoundTrip() {
        // Arrange
        Map<String, Set<String>> originalChildrenGraph = new HashMap<>();
        originalChildrenGraph.put("A", new HashSet<>(Arrays.asList("B", "C")));
        originalChildrenGraph.put("B", new HashSet<>(Collections.singletonList("D")));
        originalChildrenGraph.put("C", new HashSet<>(Collections.singletonList("D")));
        originalChildrenGraph.put("D", Collections.emptySet());
        // Mock the first conversion (children to parent)
        Map<String, Set<String>> mockParentGraph = new HashMap<>();
        mockParentGraph.put("B", new HashSet<>(Collections.singletonList("A")));
        mockParentGraph.put("C", new HashSet<>(Collections.singletonList("A")));
        mockParentGraph.put("D", new HashSet<>(Arrays.asList("B", "C")));
        mockParentGraph.put("A", Collections.emptySet());
        // Act
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(originalChildrenGraph);
        Map<String, Set<String>> childrenGraph = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        // Assert
        assertEquals(originalChildrenGraph.size(), childrenGraph.size(), "Size should match original");
        assertIterableEquals(originalChildrenGraph.get("A"), childrenGraph.get("A"), "A's children should match");
        assertIterableEquals(originalChildrenGraph.get("B"), childrenGraph.get("B"), "B's children should match");
        assertIterableEquals(originalChildrenGraph.get("C"), childrenGraph.get("C"), "C's children should match");
        assertIterableEquals(originalChildrenGraph.get("D"), childrenGraph.get("D"), "D's children should match");
    }

    @Test
    @DisplayName("Should calculate correct in-degree for normal DAG")
    void testInitInDegreeMap_NormalDAG() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        Set<String> parentsA = new HashSet<>();
        parentsA.add("B");
        parentsA.add("C");
        parentGraph.put("A", parentsA);
        Set<String> parentsB = new HashSet<>();
        parentsB.add("C");
        parentGraph.put("B", parentsB);
        parentGraph.put("C", new HashSet<>());
        // act
        Map<String, Integer> result = DAGBFSUtils.initInDegreeMap(parentGraph);
        // assert
        assertAll(() -> assertEquals(3, result.size(), "Should contain all nodes"), () -> assertEquals(2, result.get("A"), "Node A should have in-degree 2"), () -> assertEquals(1, result.get("B"), "Node B should have in-degree 1"), () -> assertEquals(0, result.get("C"), "Node C should have in-degree 0"));
    }

    @Test
    @DisplayName("Should return empty map for empty graph")
    void testInitInDegreeMap_EmptyGraph() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        // act
        Map<String, Integer> result = DAGBFSUtils.initInDegreeMap(parentGraph);
        // assert
        assertAll(() -> assertNotNull(result, "Should not return null"), () -> assertTrue(result.isEmpty(), "Should return empty map"));
    }

    @Test
    @DisplayName("Should handle single node graph")
    void testInitInDegreeMap_SingleNode() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        // act
        Map<String, Integer> result = DAGBFSUtils.initInDegreeMap(parentGraph);
        // assert
        assertAll(() -> assertEquals(1, result.size(), "Should contain single node"), () -> assertEquals(0, result.get("A"), "Single node should have in-degree 0"));
    }

    @Test
    @DisplayName("Should throw NullPointerException when input is null")
    void testInitInDegreeMap_NullInput() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = null;
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> DAGBFSUtils.initInDegreeMap(parentGraph), "Should throw NullPointerException for null input");
    }

    @Test
    @DisplayName("Should throw NullPointerException when node has null parent set")
    void testInitInDegreeMap_NodeWithNullParents() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", null);
        parentGraph.put("B", new HashSet<>());
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> DAGBFSUtils.initInDegreeMap(parentGraph), "Should throw NullPointerException when parent set is null");
    }

    @Test
    @DisplayName("Should throw NullPointerException when any node has null parent set")
    void testInitInDegreeMap_MixedNodes() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        Set<String> parentsA = new HashSet<>();
        parentsA.add("B");
        parentsA.add("C");
        parentGraph.put("A", parentsA);
        parentGraph.put("B", new HashSet<>());
        // This will cause NullPointerException
        parentGraph.put("C", null);
        parentGraph.put("D", new HashSet<>());
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> DAGBFSUtils.initInDegreeMap(parentGraph), "Should throw NullPointerException when any parent set is null");
    }

    @Test
    @DisplayName("Should handle graph where all nodes have empty parent sets")
    void testInitInDegreeMap_AllNodesWithEmptyParents() throws Throwable {
        // arrange
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        parentGraph.put("B", new HashSet<>());
        parentGraph.put("C", new HashSet<>());
        // act
        Map<String, Integer> result = DAGBFSUtils.initInDegreeMap(parentGraph);
        // assert
        assertAll(() -> assertEquals(3, result.size(), "Should contain all nodes"), () -> assertEquals(0, result.get("A"), "Node A should have in-degree 0"), () -> assertEquals(0, result.get("B"), "Node B should have in-degree 0"), () -> assertEquals(0, result.get("C"), "Node C should have in-degree 0"));
    }

    @Test
    @DisplayName("Should return all zero-degree nodes when start is null")
    public void testInitQueue_NoStart_AllZeroDegreeNodes() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        inDegreeMap.put("A", 0);
        inDegreeMap.put("B", 0);
        inDegreeMap.put("C", 1);
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, null);
        // Assert
        assertEquals(2, result.size(), "Should contain 2 zero-degree nodes");
        assertTrue(result.contains("A"), "Should contain node A");
        assertTrue(result.contains("B"), "Should contain node B");
        assertFalse(result.contains("C"), "Should not contain node C");
    }

    @Test
    @DisplayName("Should return only matching start node when specified")
    public void testInitQueue_WithStart_MatchingZeroDegreeNode() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        inDegreeMap.put("A", 0);
        inDegreeMap.put("B", 0);
        inDegreeMap.put("C", 1);
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, "A");
        // Assert
        assertEquals(1, result.size(), "Should contain exactly 1 node");
        assertEquals("A", result.peek(), "Should contain only node A");
    }

    @Test
    @DisplayName("Should return empty queue for empty input map")
    public void testInitQueue_EmptyInputMap() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, null);
        // Assert
        assertTrue(result.isEmpty(), "Queue should be empty");
    }

    @Test
    @DisplayName("Should return empty queue when no zero-degree nodes exist")
    public void testInitQueue_NoZeroDegreeNodes() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        inDegreeMap.put("A", 1);
        inDegreeMap.put("B", 2);
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, null);
        // Assert
        assertTrue(result.isEmpty(), "Queue should be empty");
    }

    @Test
    @DisplayName("Should return empty queue when start node doesn't exist")
    public void testInitQueue_WithStart_NoMatchingNode() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        inDegreeMap.put("A", 0);
        inDegreeMap.put("B", 0);
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, "C");
        // Assert
        assertTrue(result.isEmpty(), "Queue should be empty");
    }

    @Test
    @DisplayName("Should return empty queue when start node has non-zero degree")
    public void testInitQueue_WithStart_MatchingNonZeroDegreeNode() throws Throwable {
        // Arrange
        Map<String, Integer> inDegreeMap = new HashMap<>();
        inDegreeMap.put("A", 1);
        inDegreeMap.put("B", 0);
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(inDegreeMap, "A");
        // Assert
        assertTrue(result.isEmpty(), "Queue should be empty");
    }

    @Test
    @DisplayName("Should iterate through all map entries")
    @SuppressWarnings("unchecked")
    public void testInitQueue_VerifyMapIteration() throws Throwable {
        // Arrange
        Map<String, Integer> mockedMap = mock(Map.class);
        ArgumentCaptor<BiConsumer> consumerCaptor = ArgumentCaptor.forClass(BiConsumer.class);
        // 模拟forEach的行为
        doAnswer(invocation -> {
            BiConsumer<String, Integer> consumer = invocation.getArgument(0);
            consumer.accept("A", 0);
            consumer.accept("B", 1);
            consumer.accept("C", 0);
            return null;
        }).when(mockedMap).forEach(any());
        // Act
        Queue<String> result = DAGBFSUtils.initQueue(mockedMap, null);
        // Assert
        verify(mockedMap, times(1)).forEach(consumerCaptor.capture());
        assertEquals(2, result.size(), "Should contain 2 zero-degree nodes");
        assertTrue(result.contains("A"), "Should contain node A");
        assertTrue(result.contains("C"), "Should contain node C");
        assertFalse(result.contains("B"), "Should not contain node B");
    }

    @Test
    void testCheckDAGGraphIntegrity_ValidGraph_NoExceptionThrown() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        Set<String> childrenA = new HashSet<>();
        childrenA.add("B");
        childrenA.add("C");
        dagGraph.put("A", childrenA);
        Set<String> childrenB = new HashSet<>();
        childrenB.add("D");
        dagGraph.put("B", childrenB);
        Set<String> childrenC = new HashSet<>();
        childrenC.add("D");
        dagGraph.put("C", childrenC);
        dagGraph.put("D", new HashSet<>());
        // act & assert
        assertDoesNotThrow(() -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
    }

    @Test
    void testCheckDAGGraphIntegrity_InvalidNode_ThrowsFetcherDAGException() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        Set<String> childrenA = new HashSet<>();
        childrenA.add("B");
        childrenA.add("C");
        dagGraph.put("A", childrenA);
        Set<String> childrenB = new HashSet<>();
        childrenB.add("D");
        dagGraph.put("B", childrenB);
        // act & assert
        FetcherDAGException exception = assertThrows(FetcherDAGException.class, () -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
        assert (exception.getMessage().contains("DAG完整性校验失败"));
    }

    @Test
    void testCheckDAGGraphIntegrity_WithMockedGraph_ThrowsWhenInvalid() throws Throwable {
        // arrange
        @SuppressWarnings("unchecked")
        Map<String, Set<String>> mockedGraph = mock(Map.class);
        @SuppressWarnings("unchecked")
        Set<String> mockedChildren = mock(Set.class);
        Set<Map.Entry<String, Set<String>>> entrySet = new HashSet<>();
        entrySet.add(new SimpleEntry<>("A", mockedChildren));
        when(mockedGraph.entrySet()).thenReturn(entrySet);
        Set<String> children = new HashSet<>();
        children.add("B");
        when(mockedChildren.iterator()).thenReturn(children.iterator());
        when(mockedGraph.containsKey("B")).thenReturn(false);
        // act & assert
        FetcherDAGException exception = assertThrows(FetcherDAGException.class, () -> DAGBFSUtils.checkDAGGraphIntegrity(mockedGraph));
        assert (exception.getMessage().contains("DAG完整性校验失败"));
    }

    @Test
    void testCheckDAGGraphIntegrity_EmptyGraph_NoExceptionThrown() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        // act & assert
        assertDoesNotThrow(() -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
    }

    @Test
    void testCheckDAGGraphIntegrity_SingleNode_NoExceptionThrown() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        dagGraph.put("A", new HashSet<>());
        // act & assert
        assertDoesNotThrow(() -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
    }

    @Test
    void testCheckDAGGraphIntegrity_SelfReferencingNode_NoExceptionThrown() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        Set<String> childrenA = new HashSet<>();
        // 自引用
        childrenA.add("A");
        dagGraph.put("A", childrenA);
        // act & assert
        assertDoesNotThrow(() -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
    }

    @Test
    void testCheckDAGGraphIntegrity_MultipleInvalidNodes_ThrowsFetcherDAGException() throws Throwable {
        // arrange
        Map<String, Set<String>> dagGraph = new HashMap<>();
        Set<String> childrenA = new HashSet<>();
        childrenA.add("B");
        childrenA.add("C");
        dagGraph.put("A", childrenA);
        Set<String> childrenB = new HashSet<>();
        childrenB.add("D");
        // 无效节点
        childrenB.add("E");
        dagGraph.put("B", childrenB);
        // act & assert
        FetcherDAGException exception = assertThrows(FetcherDAGException.class, () -> DAGBFSUtils.checkDAGGraphIntegrity(dagGraph));
        assert (exception.getMessage().contains("DAG完整性校验失败"));
    }

    @Test
    void testConvertChildrenGraphToParentGraph_NormalDAG() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>(Arrays.asList("B", "C")));
        childrenGraph.put("B", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("C", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("D", new HashSet<>());
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // assert
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.get("A").isEmpty());
        assertEquals(Collections.singleton("A"), result.get("B"));
        assertEquals(Collections.singleton("A"), result.get("C"));
        assertEquals(new HashSet<>(Arrays.asList("B", "C")), result.get("D"));
    }

    @Test
    void testConvertChildrenGraphToParentGraph_ComplexDAG() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("O", new HashSet<>(Arrays.asList("A", "E")));
        childrenGraph.put("A", new HashSet<>(Arrays.asList("B", "C", "F")));
        childrenGraph.put("B", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("C", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("D", new HashSet<>(Arrays.asList("F", "Z")));
        childrenGraph.put("E", new HashSet<>(Collections.singletonList("F")));
        childrenGraph.put("F", new HashSet<>(Collections.singletonList("Z")));
        childrenGraph.put("Z", new HashSet<>());
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // assert
        assertNotNull(result);
        assertEquals(8, result.size());
        assertTrue(result.get("O").isEmpty());
        assertEquals(Collections.singleton("O"), result.get("A"));
        assertEquals(Collections.singleton("A"), result.get("B"));
        assertEquals(Collections.singleton("A"), result.get("C"));
        assertEquals(new HashSet<>(Arrays.asList("B", "C")), result.get("D"));
        assertEquals(Collections.singleton("O"), result.get("E"));
        assertEquals(new HashSet<>(Arrays.asList("A", "D", "E")), result.get("F"));
        assertEquals(new HashSet<>(Arrays.asList("D", "F")), result.get("Z"));
    }

    @Test
    void testConvertChildrenGraphToParentGraph_EmptyGraph() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = Collections.emptyMap();
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertChildrenGraphToParentGraph_SingleNode() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", Collections.emptySet());
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get("A").isEmpty());
    }

    @Test
    void testConvertChildrenGraphToParentGraph_WithIsolatedNodes() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>(Collections.singletonList("B")));
        childrenGraph.put("B", Collections.emptySet());
        childrenGraph.put("C", Collections.emptySet());
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get("A").isEmpty());
        assertEquals(Collections.singleton("A"), result.get("B"));
        assertTrue(result.get("C").isEmpty());
    }

    @Test
    void testConvertChildrenGraphToParentGraph_NullInput() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        });
    }

    @Test
    void testConvertChildrenGraphToParentGraph_WithMock() throws Throwable {
        // arrange
        @SuppressWarnings("unchecked")
        Map<String, Set<String>> mockGraph = mock(Map.class);
        Set<String> nodes = new HashSet<>(Arrays.asList("A", "B"));
        Set<String> childrenA = Collections.singleton("B");
        when(mockGraph.keySet()).thenReturn(nodes);
        doAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            BiConsumer<String, Set<String>> action = invocation.getArgument(0);
            action.accept("A", childrenA);
            return null;
        }).when(mockGraph).forEach(any());
        // act
        Map<String, Set<String>> result = DAGBFSUtils.convertChildrenGraphToParentGraph(mockGraph);
        // assert
        verify(mockGraph, times(1)).keySet();
        verify(mockGraph, times(1)).forEach(any());
        assertNotNull(result);
        assertEquals(2, result.size());
    }
}
