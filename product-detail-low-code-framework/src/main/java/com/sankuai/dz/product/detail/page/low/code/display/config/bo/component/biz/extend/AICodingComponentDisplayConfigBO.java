package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.extend;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.DisplayRuleEngineFactory;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/6/5 20:04
 */
@Getter
public class AICodingComponentDisplayConfigBO extends BizComponentDisplayConfigBO {

    /**
     * 规则配置
     */
    private final RuleConfigDTO ruleConfig;

    public AICodingComponentDisplayConfigBO(final String componentKey,
                                            final Map<String, DataSourceConfigBO> dataSource,
                                            final RuleConfigDTO ruleConfig) {
        super(componentKey, dataSource);
        this.ruleConfig = ruleConfig;
    }

    @Override
    public List<RuleConfigDTO> getAllRuleConfigs() {
        return Lists.newArrayList(ruleConfig);
    }

    /**
     * 根据代码生成组件BO，用于实现AI-Coding
     */
    @Override
    public BizComponentBO processBizComponent(final ComponentDefinition componentDefinition,
                                              final Map<String, Object> data) {
        Object bizComponentBO = DisplayRuleEngineFactory.execute(
                ruleConfig.getRuleEngineType().getCode(), ruleConfig.getExpression(), data
        );
        return (BizComponentBO) bizComponentBO;
    }

}
