package com.sankuai.dz.product.detail.gateway.domain.message;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mtrace.util.StringUtils;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.message.dto.UnifiedMainInterfaceResultDTO;
import com.sankuai.dz.product.detail.gateway.domain.message.gray.MessageSendGrayService;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/25
 */
@Slf4j
@Component
public class MessageSendFunction {

    @Autowired
    @Qualifier("unifiedMainInterfaceResultProducer")
    private IProducerProcessor producer;

    @Resource
    private MessageSendGrayService messageSendGrayService;

    public void process(GatewayContext context) {
        try {
            // 入参或者返回为空，则直接返回
            if (Objects.isNull(context) || Objects.isNull(context.getUnifiedRequest())
                    || CollectionUtils.isEmpty(context.getSpiResponseList()) || Objects.isNull(context.getPageResponse())) {
                return;
            }

            UnifiedPageRequest request = context.getUnifiedRequest();
            // 如果moduleKeys不为空，表示重试流量，直接返回
            if (StringUtils.isNotBlank(request.getModuleKeys())) {
                log.info("retry flow, productId:{}", request.getProductId());
                return;
            }

            // 非200状态过滤
            if (context.getPageResponse().getCode() != PageResponseCodeEnum.SUCCESS.getCode()) return;

            // 非首屏接口，直接返回
            if (request.getPageRegion() != PageRegionEnum.FIRST_SCREEN.getCode()) return;

            // 灰度判定
            boolean hit = messageSendGrayService.hit(request);
            if (!hit) return;

            UnifiedMainInterfaceResultDTO resultDTO = new UnifiedMainInterfaceResultDTO();
            resultDTO.setUnifiedRequest(request);
            resultDTO.setSpiResponseList(context.getSpiResponseList());
            resultDTO.setSpiRequest(context.getSpiRequest());
            send(JSON.toJSONString(resultDTO));
        } catch (Throwable e) {
            UnifiedPageRequest request = context.getUnifiedRequest();
            log.error("send sync message error, request:{}", JSON.toJSONString(request), e);
        }
    }

    public void send(String msg) {
        try {
            producer.sendMessage(msg);
        } catch (Exception e) {
            log.error("send sync message error, msg:{}", msg, e);
        }
    }
}
