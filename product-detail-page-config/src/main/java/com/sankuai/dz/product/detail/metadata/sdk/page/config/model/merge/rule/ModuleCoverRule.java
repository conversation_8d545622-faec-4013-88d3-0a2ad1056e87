package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 15:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "模块覆盖策略")
public class ModuleCoverRule extends AbstractMergeRule {

    @FieldDoc(description = "指定groupName", requiredness = Requiredness.OPTIONAL)
    private String groupName;

    @FieldDoc(description = "目标覆盖模块", rule = "替换原配置中所有key相同模块的配置数据", requiredness = Requiredness.REQUIRED)
    private ModuleConfigDTO coverModule;

    @Override
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_COVER;
    }

}
