package com.sankuai.dz.product.detail.gateway.domain.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.domain.bean.DeviceInfo;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserAgent;
import com.sankuai.dz.product.detail.gateway.domain.utils.HeaderUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.UserAgentUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DeviceInfoResolverTest {

    @InjectMocks
    private DeviceInfoResolver deviceInfoResolver;

    private Map<String, String> headerMap;

    @Before
    public void setUp() {
        headerMap = new HashMap<>();
    }

    /**
     * Test case for empty header map
     * Expected: All fields in DeviceInfo should be empty strings
     */
    @Test
    public void testGetDeviceInfoWhenHeaderMapIsEmpty() throws Throwable {
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(true, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("", deviceInfo.getDeviceId());
        assertEquals("", deviceInfo.getUnionId());
        assertEquals("", deviceInfo.getAppVersion());
        assertEquals("", deviceInfo.getMobileOSType());
        assertEquals("", deviceInfo.getMpAppId());
        assertEquals("", deviceInfo.getUserAgent());
        assertEquals("", deviceInfo.getOpenId());
    }

    /**
     * Test case for header map containing deviceId and unionId
     * Expected: DeviceInfo should contain the provided deviceId and unionId
     */
    @Test
    public void testGetDeviceInfoWhenHeaderMapContainsDeviceIdAndUnionId() throws Throwable {
        // arrange
        headerMap.put("pragma-dpid", "test-dpid");
        headerMap.put("pragma-unionid", "test-unionid");
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(true, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("test-dpid", deviceInfo.getDeviceId());
        assertEquals("test-unionid", deviceInfo.getUnionId());
    }

    /**
     * Test case for header map containing User-Agent
     * Expected: DeviceInfo should contain parsed app version and mobile OS type
     */
    @Test
    public void testGetDeviceInfoWhenHeaderMapContainsUserAgent() throws Throwable {
        // arrange
        String userAgent = "mapi 1.0.0 (mtscope 2.0.0; iPhone; iOS 14.0)";
        headerMap.put("User-Agent", userAgent);
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(true, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("2.0.0", deviceInfo.getAppVersion());
        assertEquals("ios", deviceInfo.getMobileOSType());
        assertEquals(userAgent, deviceInfo.getUserAgent());
    }

    /**
     * Test case for header map containing mpAppId
     * Expected: DeviceInfo should contain the provided mpAppId
     */
    @Test
    public void testGetDeviceInfoWhenHeaderMapContainsMpAppId() throws Throwable {
        // arrange
        headerMap.put("mpAppId", "test-mpappid");
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(true, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("test-mpappid", deviceInfo.getMpAppId());
    }

    /**
     * Test case for header map containing openId
     * Expected: DeviceInfo should contain the provided openId
     */
    @Test
    public void testGetDeviceInfoWhenHeaderMapContainsOpenId() throws Throwable {
        // arrange
        headerMap.put("openId", "test-openid");
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(true, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("test-openid", deviceInfo.getOpenId());
    }

    /**
     * Test case for isDp flag affecting deviceId resolution
     * Expected: DeviceInfo should contain UUID when isDp is false
     */
    @Test
    public void testGetDeviceInfoWhenIsDpIsFalse() throws Throwable {
        // arrange
        headerMap.put("pragma-uuid", "test-uuid");
        headerMap.put("pragma-dpid", "test-dpid");
        // act
        CompletableFuture<DeviceInfo> result = deviceInfoResolver.getDeviceInfo(false, headerMap);
        // assert
        assertNotNull(result);
        DeviceInfo deviceInfo = result.get();
        assertEquals("test-uuid", deviceInfo.getDeviceId());
    }
}
