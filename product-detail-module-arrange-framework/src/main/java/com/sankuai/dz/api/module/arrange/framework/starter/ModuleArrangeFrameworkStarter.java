package com.sankuai.dz.api.module.arrange.framework.starter;

import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.component.storage.ComponentDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.management.rpc.MetadataManagementRpcServiceImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * @Author: guangyujie
 * @Date: 2025/1/27 21:36
 */
public class ModuleArrangeFrameworkStarter implements ApplicationContextAware, InitializingBean, BeanPostProcessor {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //初始化fetcher定义
        FetcherDefinitionStorage.initFetcherDefinitionStorage(this.applicationContext);
        //初始化Component
        ComponentDefinitionStorage.initComponentDefinitionStorage(this.applicationContext);
        //初始化fetcher的DAG
        FetcherDAGStorage.initFetcherDAGStorage();
        //初始化build定义
        BuilderDefinitionStorage.initBuilderDefinitionStorage(this.applicationContext);
        //发布对外管理接口
        MetadataManagementRpcServiceImpl.registerRpcService();
    }

}
