package com.sankuai.dz.product.detail.gateway.domain.page.config;

import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PageConfigMainFunctionTest {

    @InjectMocks
    private PageConfigMainFunction pageConfigMainFunction;

    @Mock
    private List<PageConfigBaseHandler> pageConfigHandlerList;

    @Before
    public void setUp() throws Exception {
        // Clear the map content before each test
        getPageConfigHandlerMap().clear();
    }

    @After
    public void tearDown() throws Exception {
        // Clear the map content after each test
        getPageConfigHandlerMap().clear();
    }

    private Map<PageRegionEnum, PageConfigBaseHandler> getPageConfigHandlerMap() throws Exception {
        Field field = PageConfigMainFunction.class.getDeclaredField("pageConfigHandlerMap");
        field.setAccessible(true);
        return (Map<PageRegionEnum, PageConfigBaseHandler>) field.get(null);
    }

    private PageConfigBaseHandler createMockHandler(PageRegionEnum... regions) {
        return new PageConfigBaseHandler() {

            @Override
            public Set<PageRegionEnum> getSupportedPageRegion() {
                Set<PageRegionEnum> supportedRegions = new HashSet<>();
                Collections.addAll(supportedRegions, regions);
                return supportedRegions;
            }

            @Override
            protected void checkPageConfig(PageConfigDataDTO pageConfig, Map<String, BizModuleConfigDTO> bizModuleMap) throws SPIConfigFatalException {

            }

            @Override
            protected PageConfigSceneEnum getPageConfigSceneEnum() {
                return PageConfigSceneEnum.ProductDetail;
            }

            @Override
            protected boolean isNeed(UnifiedPageRequest unifiedPageRequest, ProductDetailPageRequest detailPageRequest, BizModuleConfigDTO bizModuleConfigDTO) {
                return false;
            }
        };
    }

    /**
     * Test successful initialization with multiple handlers
     */
    @Test
    public void testAfterPropertiesSet_SuccessfulInitialization() throws Throwable {
        // Arrange
        PageConfigBaseHandler handler1 = createMockHandler(PageRegionEnum.FIRST_SCREEN);
        PageConfigBaseHandler handler2 = createMockHandler(PageRegionEnum.NOT_FIRST_SCREEN);
        List<PageConfigBaseHandler> handlers = Arrays.asList(handler1, handler2);
        when(pageConfigHandlerList.iterator()).thenReturn(handlers.iterator());
        // Act
        pageConfigMainFunction.afterPropertiesSet();
        // Assert
        Map<PageRegionEnum, PageConfigBaseHandler> map = getPageConfigHandlerMap();
        assertEquals(2, map.size());
        assertSame(handler1, map.get(PageRegionEnum.FIRST_SCREEN));
        assertSame(handler2, map.get(PageRegionEnum.NOT_FIRST_SCREEN));
    }

    /**
     * Test initialization with empty handler list
     */
    @Test
    public void testAfterPropertiesSet_EmptyHandlerList() throws Throwable {
        // Arrange
        when(pageConfigHandlerList.iterator()).thenReturn(Collections.emptyIterator());
        // Act
        pageConfigMainFunction.afterPropertiesSet();
        // Assert
        Map<PageRegionEnum, PageConfigBaseHandler> map = getPageConfigHandlerMap();
        assertEquals(0, map.size());
    }

    /**
     * Test initialization with handler having empty regions
     */
    @Test
    public void testAfterPropertiesSet_HandlerWithEmptyRegions() throws Throwable {
        // Arrange
        PageConfigBaseHandler handler = createMockHandler();
        when(pageConfigHandlerList.iterator()).thenReturn(Collections.singletonList(handler).iterator());
        // Act
        pageConfigMainFunction.afterPropertiesSet();
        // Assert
        Map<PageRegionEnum, PageConfigBaseHandler> map = getPageConfigHandlerMap();
        assertEquals(0, map.size());
    }

    /**
     * Test initialization with duplicate region handlers
     */
    @Test(expected = IllegalArgumentException.class)
    public void testAfterPropertiesSet_DuplicateRegionHandler() throws Throwable {
        // Arrange
        PageConfigBaseHandler handler1 = createMockHandler(PageRegionEnum.FIRST_SCREEN);
        PageConfigBaseHandler handler2 = createMockHandler(PageRegionEnum.FIRST_SCREEN);
        List<PageConfigBaseHandler> handlers = Arrays.asList(handler1, handler2);
        when(pageConfigHandlerList.iterator()).thenReturn(handlers.iterator());
        // Act & Assert
        pageConfigMainFunction.afterPropertiesSet();
    }
}
