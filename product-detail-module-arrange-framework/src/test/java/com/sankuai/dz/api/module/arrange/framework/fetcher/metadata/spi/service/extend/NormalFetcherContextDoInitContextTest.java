package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NormalFetcherContextDoInitContextTest {

    @Mock
    private FetcherEngine fetcherEngine;

    /**
     * Test successful execution of doInitContext
     * Verifies that:
     * 1. The method returns a valid FetcherContextInitResult
     * 2. The future is properly set and completes with success
     * 3. The result contains the expected value
     */
    @Test
    public void testDoInitContextSuccess() throws Throwable {
        // arrange
        TestNormalFetcherContext context = new TestNormalFetcherContext();
        TestReturnValueDTO expectedResult = new TestReturnValueDTO();
        context.setTestResult(expectedResult);
        // act
        FetcherContextInitResult result = context.doInitContext(fetcherEngine);
        // assert
        assertNotNull(result);
        assertNotNull(result.getSelf());
        assertEquals(context, result.getSelf());
        CompletableFuture<FetcherResponse<TestReturnValueDTO>> future = context.getFuture();
        assertNotNull(future);
        FetcherResponse<TestReturnValueDTO> response = future.get();
        assertTrue(response.isSuccess());
        assertEquals(expectedResult, response.getReturnValue());
        assertNull(response.getError());
    }

    /**
     * Test doInitContext when doFetch throws exception
     * Verifies that:
     * 1. The method returns a valid FetcherContextInitResult despite the exception
     * 2. The future is properly set and completes with failure
     * 3. The error is properly captured in the response
     */
    @Test
    public void testDoInitContextWhenDoFetchThrowsException() throws Throwable {
        // arrange
        TestNormalFetcherContext context = new TestNormalFetcherContext();
        RuntimeException expectedException = new RuntimeException("Test exception");
        context.setTestException(expectedException);
        // act
        FetcherContextInitResult result = context.doInitContext(fetcherEngine);
        // assert
        assertNotNull(result);
        assertNotNull(result.getSelf());
        assertEquals(context, result.getSelf());
        CompletableFuture<FetcherResponse<TestReturnValueDTO>> future = context.getFuture();
        assertNotNull(future);
        FetcherResponse<TestReturnValueDTO> response = future.get();
        assertFalse(response.isSuccess());
        assertNull(response.getReturnValue());
        Throwable actualError = response.getError();
        assertNotNull(actualError);
        assertTrue(actualError instanceof CompletionException);
        assertEquals(expectedException, actualError.getCause());
    }

    // Test implementation classes
    private static class TestReturnValueDTO extends FetcherReturnValueDTO {
    }

    private static class TestNormalFetcherContext extends NormalFetcherContext<TestReturnValueDTO> {

        private TestReturnValueDTO testResult;

        private RuntimeException testException;

        public void setTestResult(TestReturnValueDTO result) {
            this.testResult = result;
        }

        public void setTestException(RuntimeException exception) {
            this.testException = exception;
        }

        @Override
        protected CompletableFuture<TestReturnValueDTO> doFetch() throws Exception {
            if (testException != null) {
                CompletableFuture<TestReturnValueDTO> future = new CompletableFuture<>();
                future.completeExceptionally(testException);
                return future;
            }
            return CompletableFuture.completedFuture(testResult);
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return new FetcherContextInitResult(this);
        }
    }
}
