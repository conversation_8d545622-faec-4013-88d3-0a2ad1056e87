package com.sankuai.dz.product.detail.page.low.code.validation;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import org.junit.Test;

import java.util.*;

/**
 * 配置一致性校验器测试
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 11:00
 */
public class StructComponentDisplayConfigValidatorTest {

    @Test
    public void testGetAllDataSource() {
        // 创建测试数据
        List<ComponentDisplayConfigBO> componentConfigList = createTestComponentConfigs();
        
        // 调用方法
        Map<String, DataSourceConfigBO> result = ModuleDisplayConfigBO.getAllDataSource(componentConfigList);
        
        // 验证结果
        System.out.println("获取到的数据源数量: " + result.size());
        for (Map.Entry<String, DataSourceConfigBO> entry : result.entrySet()) {
            System.out.println("FetcherName: " + entry.getKey() + 
                             ", OptionalFields: " + entry.getValue().getOptionalFieldName());
        }
    }

    private List<ComponentDisplayConfigBO> createTestComponentConfigs() {
        List<ComponentDisplayConfigBO> configs = new ArrayList<>();
        
        // 创建模拟的组件配置
        // 注意：这里只是为了测试 getAllDataSource 方法，实际的组件配置创建会更复杂
        
        return configs;
    }

    @Test
    public void testEmptyComponentList() {
        // 测试空组件列表
        Map<String, DataSourceConfigBO> result = ModuleDisplayConfigBO.getAllDataSource(null);
        assert result.isEmpty() : "空列表应该返回空Map";
        
        result = ModuleDisplayConfigBO.getAllDataSource(new ArrayList<>());
        assert result.isEmpty() : "空列表应该返回空Map";
        
        System.out.println("空组件列表测试通过");
    }
}
