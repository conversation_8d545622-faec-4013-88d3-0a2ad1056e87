package com.sankuai.dz.product.detail.gateway.spi.response.generic;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.enums.ModuleResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 19:06
 */
@Data
@TypeDoc(description = "泛化调用模块返回值")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenericModuleResponse implements Serializable {

    @FieldDoc(description = "状态码", requiredness = Requiredness.REQUIRED, type = ModuleResponseCodeEnum.class)
    private int code;

    @FieldDoc(description = "失败原因", rule = "code != ResponseCodeEnum.SUCCESS.getCode()才有效", requiredness = Requiredness.OPTIONAL)
    private String msg;

    @FieldDoc(description = "模块key", requiredness = Requiredness.REQUIRED)
    private String moduleKey;

    @FieldDoc(description = "模块VO", rule = "泛型，具体看AbstractModuleVO.getModuleKey", requiredness = Requiredness.REQUIRED)
    private JSONObject moduleVO;

    @JsonIgnore
    public boolean isSuccess() {
        return code == ModuleResponseCodeEnum.SUCCESS.getCode();
    }

}
