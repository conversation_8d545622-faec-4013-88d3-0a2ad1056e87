package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 15:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "模块插入策略")
public class ModuleInsertRule extends AbstractMergeRule {

    @FieldDoc(description = "指定groupName", requiredness = Requiredness.OPTIONAL)
    private String groupName;

    @FieldDoc(description = "插入位置上一个模块的key",
            rule = "如果不需要判断上一个key则不配置，但至少formerKey和nextKey有一个不为空",
            requiredness = Requiredness.OPTIONAL)
    private String formerKey;

    @FieldDoc(description = "插入位置下一个模块的key",
            rule = "如果不需要判断下一个key则不配置，但至少formerKey和nextKey有一个不为空",
            requiredness = Requiredness.OPTIONAL)
    private String nextKey;

    @FieldDoc(description = "插入的模块", rule = "可以是多个", requiredness = Requiredness.REQUIRED)
    private List<ModuleConfigDTO> insertModule;

    @Override
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_INSERT;
    }

}
