package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;


import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGAncestorFinder;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSUtils;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGGraphCut;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/31 13:15
 */
public class DAGAncestorFinderTest {

    @Test
    public void test() {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A","W"));
            put("A", Sets.newHashSet("B", "C", "D", "E", "F"));
            put("B", Sets.newHashSet("C", "D", "E", "F"));
            put("C", Sets.newHashSet("D", "E", "F"));
            put("D", Sets.newHashSet("E", "F"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("Z"));
            put("Z", Sets.newHashSet());
            put("W", Sets.newHashSet("Z"));
            put("M", Sets.newHashSet("N"));
            put("N", Sets.newHashSet("L"));
            put("L", Sets.newHashSet());
        }};
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        DAGGraphCut.DAGGraphCutResult dagGraphCutResult = DAGGraphCut.graphCut("O", childrenGraph, parentGraph);
        Map<String, Set<String>> result = DAGAncestorFinder.findAllAncestors(
                dagGraphCutResult.getTopologicalSort(), dagGraphCutResult.getParentGraph()
        );
        printAncestors(result);
        Assert.assertNotNull(result);
    }

    // 可视化打印结果
    private static void printAncestors(Map<String, Set<String>> ancestors) {
        System.out.println("节点祖先关系表：");
        ancestors.forEach((node, anc) ->
                System.out.printf("%-5s -> %s%n",
                        node,
                        anc.stream().sorted().collect(Collectors.toList()))
        );
    }

}