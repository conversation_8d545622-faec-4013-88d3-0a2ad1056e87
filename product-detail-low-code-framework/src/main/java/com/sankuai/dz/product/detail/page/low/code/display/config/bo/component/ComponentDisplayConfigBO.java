package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigIncorrectException;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 17:14
 */
@Getter
public abstract class ComponentDisplayConfigBO implements Serializable {

    /**
     * 模块key，全局唯一
     */
    protected final String componentKey;

    protected ComponentDisplayConfigBO(final String componentKey) {
        this.componentKey = componentKey;
    }

    public abstract List<DataSourceConfigBO> getDataSourceConfigs();

    /**
     * 获取组件类型
     */
    public abstract ComponentGroupEnum getComponentGroup();

    /**
     * 获取支持的组件分类
     */
    public abstract Set<ComponentTypeEnum> getSupportedComponentType();

    /**
     * 构建ComponentBO
     * @param depth 递归深度，如果出现容器组件则需要递归
     */
    public abstract ComponentBO buildComponentBO(final Map<String, Object> data,
                                                 final int depth);

    /**
     * 校验配置正确性
     */
    public void check() throws LowCodeConfigIncorrectException {
        // 获取组件元数据，如果获取不到则会抛出异常
        ComponentDefinition componentDefinition = ComponentDefinitionStorage.getComponentMetadata(this.componentKey);
        check(componentDefinition);
    }

    protected abstract void check(final ComponentDefinition componentDefinition) throws LowCodeConfigIncorrectException;

}
