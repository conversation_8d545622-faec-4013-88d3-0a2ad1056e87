package com.sankuai.dz.api.module.arrange.framework.fetcher.storage;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import java.lang.reflect.Field;
import java.util.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@ExtendWith(MockitoExtension.class)
class FetcherDAGStorageTest {

    private static final String EXISTING_FETCHER = "existingFetcher";

    private static final String NON_EXISTENT_FETCHER = "nonExistentFetcher";

    private static final String CHILD_FETCHER = "childFetcher";

    private DAG mockDag;

    private Map<String, DAG> originalDagMap;

    private Field dagMapField;

    @SuppressWarnings("unchecked")
    @BeforeEach
    void setUp() throws Exception {
        // Access private static field using reflection
        dagMapField = FetcherDAGStorage.class.getDeclaredField("dagMap");
        dagMapField.setAccessible(true);
        // Store original map content
        originalDagMap = new HashMap<>((Map<String, DAG>) dagMapField.get(null));
        // Create a real DAG instance with valid graph structure
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put(EXISTING_FETCHER, new HashSet<>(Collections.singletonList(CHILD_FETCHER)));
        childrenGraph.put(CHILD_FETCHER, new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put(CHILD_FETCHER, new HashSet<>(Collections.singletonList(EXISTING_FETCHER)));
        parentGraph.put(EXISTING_FETCHER, new HashSet<>());
        mockDag = new DAG(DAG.DAGBuilder.builder().start(EXISTING_FETCHER).entireDAGChildrenGraph(childrenGraph).entireDAGParentGraph(parentGraph).needCheckCycle(false).needCheckGraphIntegrity(false).build());
        // Clear and setup test state
        Map<String, DAG> currentDagMap = (Map<String, DAG>) dagMapField.get(null);
        currentDagMap.clear();
        currentDagMap.put(EXISTING_FETCHER, mockDag);
    }

    @SuppressWarnings("unchecked")
    @AfterEach
    void tearDown() throws Exception {
        // Restore original static state
        Map<String, DAG> currentDagMap = (Map<String, DAG>) dagMapField.get(null);
        currentDagMap.clear();
        currentDagMap.putAll(originalDagMap);
        // Reset field accessibility
        dagMapField.setAccessible(false);
    }

    /**
     * 测试正常情况 - 存在对应的DAG对象
     */
    @Test
    void testGetDAGWhenDAGExists() throws Throwable {
        // arrange - already set up in @BeforeEach
        // act
        DAG result = FetcherDAGStorage.getDAG(EXISTING_FETCHER);
        // assert
        assertNotNull(result);
        assertEquals(mockDag, result);
        assertEquals(EXISTING_FETCHER, result.getStart());
    }

    /**
     * 测试异常情况 - 不存在对应的DAG对象
     */
    @Test
    void testGetDAGWhenDAGNotExists() throws Throwable {
        // arrange - already set up in @BeforeEach
        // act & assert
        FetcherFatalException exception = assertThrows(FetcherFatalException.class, () -> FetcherDAGStorage.getDAG(NON_EXISTENT_FETCHER));
        assertEquals(String.format("不存在该startFetcher(%s)对应的DAG", NON_EXISTENT_FETCHER), exception.getMessage());
    }

    /**
     * 测试边界情况 - 传入null参数
     */
    @Test
    void testGetDAGWithNullParameter() throws Throwable {
        // arrange - already set up in @BeforeEach
        // act & assert
        FetcherFatalException exception = assertThrows(FetcherFatalException.class, () -> FetcherDAGStorage.getDAG(null));
        assertEquals("不存在该startFetcher(null)对应的DAG", exception.getMessage());
    }

    /**
     * 测试静态方法调用情况
     */
    @Test
    void testGetDAGVerifyStaticMethodCalls() throws Throwable {
        // arrange - already set up in @BeforeEach
        // act
        DAG result = FetcherDAGStorage.getDAG(EXISTING_FETCHER);
        // assert
        assertNotNull(result);
        assertEquals(mockDag, result);
    }

}
