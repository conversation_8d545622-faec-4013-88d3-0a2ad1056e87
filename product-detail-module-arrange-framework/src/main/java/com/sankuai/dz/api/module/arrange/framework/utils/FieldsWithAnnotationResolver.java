package com.sankuai.dz.api.module.arrange.framework.utils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/4/29 10:36
 */
public class FieldsWithAnnotationResolver {

    /**
     * 获取指定类中所有被特定注解标记的字段
     *
     * @param clazz           目标类
     * @param annotationClass 目标注解，必须 @Retention(RetentionPolicy.RUNTIME)
     * @param parentClass     目标必须继承自该类型
     * @return 类及父类下所有被annotationClass标记的属性
     */
    @SuppressWarnings("unchecked")
    public static <T> List<Class<? extends T>> getFields(final Class<?> clazz,
                                                         final Class<? extends Annotation> annotationClass,
                                                         Class<T> parentClass) {
        List<Class<? extends T>> result = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            for (Field field : currentClass.getDeclaredFields()) {
                Class<?> fieldType = field.getType();
                if (fieldType.isAnnotationPresent(annotationClass)
                        && parentClass.isAssignableFrom(fieldType)) {
                    result.add((Class<? extends T>) fieldType);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        return result;
    }

}
