package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.validation;

import com.sankuai.athena.digital.arch.enums.DAFieldTypeEnum;
import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.DAGenericTypeBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAFieldInfoBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.FieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.PojoTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 配置一致性校验器
 * 用于校验StructComponentDisplayConfigBO的fieldConfigs递归结构与ComponentDefinition的classInfo递归结构的一致性
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 10:00
 */
@Slf4j
public class StructComponentDisplayConfigValidator {

    /**
     * 递归校验字段结构一致性
     *
     * @param componentKey 组件key，用于错误信息
     * @param fieldConfigs 配置中的字段列表
     * @param classInfo    元数据中的类信息
     * @param depth        递归深度
     */
    public static void validate(final String componentKey,
                                final List<FieldDisplayConfigBO> fieldConfigs,
                                final DAClassInfoBO classInfo,
                                final int depth) {
        // 检查递归深度
        if (depth >= ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new LowCodeConfigFatalException(String.format(
                    "配置嵌套层数过深，componentKey: %s, 最多允许 %d 层",
                    componentKey, ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH
            ));
        }
        // 获取元数据中的字段信息
        Map<String, DAFieldInfoBO> metadataFieldMap = classInfo.getFieldInfoMap();
        // 校验配置中的每个字段在元数据中都存在
        for (FieldDisplayConfigBO fieldConfig : fieldConfigs) {
            String fieldName = fieldConfig.getFieldName();
            DAFieldInfoBO metadataField = metadataFieldMap.get(fieldName);

            if (metadataField == null) {
                throw new LowCodeConfigFatalException(String.format(
                        "配置中的字段在元数据中不存在，componentKey: %s, fieldName: %s, class: %s",
                        componentKey, fieldName, classInfo.getTClass().getName()
                ));
            }

            // 校验字段类型一致性
            validateFieldType(componentKey, fieldConfig, metadataField, depth);
        }
    }

    /**
     * 校验单个字段的类型一致性
     *
     * @param componentKey  组件key
     * @param fieldConfig   配置中的字段
     * @param metadataField 元数据中的字段
     * @param depth         递归深度
     */
    private static void validateFieldType(final String componentKey,
                                          final FieldDisplayConfigBO fieldConfig,
                                          final DAFieldInfoBO metadataField,
                                          final int depth) {
        // 校验集合类型一致性
        boolean isConfigCollection = fieldConfig.isCollection();
        boolean isMetadataCollection = (metadataField.getGenericType().getRawTypeEnum() == DAFieldTypeEnum.COLLECTION);

        if (isConfigCollection != isMetadataCollection) {
            throw new LowCodeConfigFatalException(String.format(
                    "字段集合类型不一致，componentKey: %s, fieldName: %s, 配置中isCollection: %s, 元数据中isCollection: %s",
                    componentKey, fieldConfig.getFieldName(), isConfigCollection, isMetadataCollection
            ));
        }

        // 如果是POJO类型，需要递归校验
        if (fieldConfig.isPojo()) {
            validatePojoField(componentKey, fieldConfig, metadataField, depth);
        } else {
            // 基本类型字段，校验元数据中确实是基本类型
            validateBaseTypeField(componentKey, fieldConfig, metadataField);
        }
    }

    /**
     * 校验POJO类型字段
     */
    private static void validatePojoField(String componentKey,
                                          FieldDisplayConfigBO fieldConfig,
                                          DAFieldInfoBO metadataField,
                                          int depth) {
        if (!(fieldConfig instanceof PojoTypeFieldDisplayConfigBO)) {
            throw new LowCodeConfigFatalException(String.format(
                    "配置标记为POJO但类型不匹配，componentKey: %s, fieldName: %s",
                    componentKey, fieldConfig.getFieldName()
            ));
        }

        PojoTypeFieldDisplayConfigBO pojoConfig = (PojoTypeFieldDisplayConfigBO) fieldConfig;

        DAGenericTypeBO actualType = getActualType(componentKey, fieldConfig, metadataField);

        // 校验是否为POJO类型
        if (actualType.getRawTypeEnum() != DAFieldTypeEnum.POJO) {
            throw new LowCodeConfigFatalException(String.format(
                    "配置标记为POJO但元数据中不是POJO类型，componentKey: %s, fieldName: %s, 实际类型: %s",
                    componentKey, fieldConfig.getFieldName(), actualType.getRawTypeEnum()
            ));
        }

        // 获取POJO的类信息
        DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(actualType.getRawType().getTClass());
        if (subClassInfo == null) {
            throw new LowCodeConfigFatalException(String.format(
                    "无法获取POJO字段的元数据，componentKey: %s, fieldName: %s, class: %s",
                    componentKey, fieldConfig.getFieldName(), actualType.getRawType().getTClass().getName()
            ));
        }

        // 递归校验子字段
        validate(
                componentKey,
                pojoConfig.getFieldConfigs(),
                subClassInfo,
                depth + 1
        );
    }

    /**
     * 校验基本类型字段
     */
    private static void validateBaseTypeField(String componentKey,
                                              FieldDisplayConfigBO fieldConfig,
                                              DAFieldInfoBO metadataField) {
        DAGenericTypeBO actualType = getActualType(componentKey, fieldConfig, metadataField);

        // 校验是否为基本类型
        if (actualType.getRawTypeEnum() != DAFieldTypeEnum.BASE_TYPE) {
            throw new LowCodeConfigFatalException(String.format(
                    "配置标记为基本类型但元数据中不是基本类型，componentKey: %s, fieldName: %s, 实际类型: %s",
                    componentKey, fieldConfig.getFieldName(), actualType.getRawTypeEnum()
            ));
        }
    }

    /**
     * 获取实际类型，如果是集合则取第一个泛型类型，如果不是则直接返回
     */
    private static DAGenericTypeBO getActualType(final String componentKey,
                                                 final FieldDisplayConfigBO fieldConfig,
                                                 final DAFieldInfoBO metadataField) {
        DAGenericTypeBO genericType = metadataField.getGenericType();

        // 获取实际类型（如果是集合，取泛型参数）
        DAGenericTypeBO actualType = genericType;
        if (genericType.getRawTypeEnum() == DAFieldTypeEnum.COLLECTION) {
            if (genericType.getParameterizedTypeList().isEmpty()) {
                throw new LowCodeConfigFatalException(String.format(
                        "集合类型缺少泛型参数，componentKey: %s, fieldName: %s",
                        componentKey, fieldConfig.getFieldName()
                ));
            }
            actualType = genericType.getParameterizedTypeList().get(0);
        }
        return actualType;
    }

}
