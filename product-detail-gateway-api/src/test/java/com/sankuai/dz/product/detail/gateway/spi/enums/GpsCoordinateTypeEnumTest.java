package com.sankuai.dz.product.detail.gateway.spi.enums;

import org.junit.Test;

import java.util.Map;

import static org.junit.Assert.*;

public class GpsCoordinateTypeEnumTest {

    /**
     * Test buildCodeDescMap with normal conditions.
     * Verifies that the method correctly maps the enum code to its description.
     */
    @Test
    public void testBuildCodeDescMapNormalConditions() throws Throwable {
        // No need to mock static method since we want to use actual enum values
        Map<Integer, String> result = GpsCoordinateTypeEnum.buildCodeDescMap();
        // assert
        assertNotNull("Result map should not be null", result);
        assertEquals("Map should contain exactly one entry", 1, result.size());
        assertEquals("Description should match for code 1", "火星坐标系", result.get(1));
    }

    /**
     * Test buildCodeDescMap with multiple enum values.
     * This test verifies that all enum values are correctly mapped.
     */
    @Test
    public void testBuildCodeDescMapMultipleValues() throws Throwable {
        // No need to mock since we want to use actual enum values
        Map<Integer, String> result = GpsCoordinateTypeEnum.buildCodeDescMap();
        // assert
        assertNotNull("Result map should not be null", result);
        assertTrue("Map should contain at least one entry", result.size() >= 1);
        assertTrue("Map should contain code 1", result.containsKey(1));
        assertEquals("Description should match for code 1", "火星坐标系", result.get(1));
    }

    @Test
    public void testContainsCode_CodeEqualsEnumCode() {
        // arrange
        int code = 1;
        // act
        boolean result = GpsCoordinateTypeEnum.containsCode(code);
        // assert
        assertTrue(result);
    }

    @Test
    public void testContainsCode_CodeNotEqualsAnyEnumCode() {
        // arrange
        int code = 999;
        // act
        boolean result = GpsCoordinateTypeEnum.containsCode(code);
        // assert
        assertFalse(result);
    }
}
