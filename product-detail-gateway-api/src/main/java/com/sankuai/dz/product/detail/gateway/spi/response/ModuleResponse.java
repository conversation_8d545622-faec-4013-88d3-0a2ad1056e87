package com.sankuai.dz.product.detail.gateway.spi.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.enums.ModuleResponseCodeEnum;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/1/10 15:29
 */
@Data
@TypeDoc(description = "模块返回值")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ModuleResponse implements Serializable {

    @FieldDoc(description = "状态码", requiredness = Requiredness.REQUIRED, type = ModuleResponseCodeEnum.class)
    private int code;

    @FieldDoc(description = "失败原因", rule = "code != ResponseCodeEnum.SUCCESS.getCode()才有效", requiredness = Requiredness.OPTIONAL)
    private String msg;

    @FieldDoc(description = "模块key", requiredness = Requiredness.REQUIRED)
    private String moduleKey;

    @FieldDoc(description = "模块VO", rule = "泛型，具体看AbstractModuleVO.getModuleKey", requiredness = Requiredness.REQUIRED)
    private AbstractModuleVO moduleVO;

    @JsonIgnore
    public boolean isSuccess() {
        return code == ModuleResponseCodeEnum.SUCCESS.getCode();
    }

    public static ModuleResponse succeed(String moduleKey, AbstractModuleVO moduleVO) {
        return ModuleResponse.builder()
                .code(ModuleResponseCodeEnum.SUCCESS.getCode())
                .moduleKey(moduleKey)
                .moduleVO(moduleVO)
                .build();
    }

    public static ModuleResponse fail(String moduleKey, String invalidReason) {
        return ModuleResponse.builder()
                .code(ModuleResponseCodeEnum.FAILURE.getCode())
                .moduleKey(moduleKey)
                .msg(invalidReason)
                .build();
    }

    public static ModuleResponse fail(ModuleResponseCodeEnum code, String moduleKey, String invalidReason) {
        return ModuleResponse.builder()
                .code(code.getCode())
                .moduleKey(moduleKey)
                .msg(invalidReason)
                .build();
    }

}
