package com.sankuai.dz.product.detail.gateway.domain.param.transformer;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.DeviceInfo;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserInfo;
import com.sankuai.dz.product.detail.gateway.domain.biz.DeviceInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.biz.ProductCategoryHandler;
import com.sankuai.dz.product.detail.gateway.domain.biz.UserInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.param.transformer.exception.CustomParseException;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RequestTransformerTest {

    @Mock
    private ProductCategoryHandler productCategoryHandler;

    @Mock
    private UserInfoResolver userInfoResolver;

    @Mock
    private DeviceInfoResolver deviceInfoResolver;

    @InjectMocks
    private RequestTransformer requestTransformer;

    private GatewayContext createContext() {
        UnifiedPageRequest unifiedRequest = new UnifiedPageRequest();
        unifiedRequest.setProductId(123L);
        unifiedRequest.setProductType(1);
        return new GatewayContext(unifiedRequest);
    }

    @Test(expected = RuntimeException.class)
    public void testProcessWhenV1PathThrowsException() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<MdpContextUtils> mdpMock = mockStatic(MdpContextUtils.class)) {
            mdpMock.when(MdpContextUtils::getAppKey).thenReturn("test-app");
            lionMock.when(() -> Lion.getInt("test-app", "request.transformer.v2.ratio", 0)).thenReturn(0);
            when(productCategoryHandler.getCategory(any())).thenThrow(new RuntimeException("Test error"));
            GatewayContext context = createContext();
            requestTransformer.process(context);
        }
    }

    @Test
    public void testProcessWhenUsingV2PathWithDebugTest() throws Throwable {
        UnifiedPageRequest unifiedRequest = new UnifiedPageRequest();
        unifiedRequest.setProductId(123L);
        unifiedRequest.setProductType(1);
        GatewayContext context = new GatewayContext(unifiedRequest);
        ProductCategory mockCategory = new ProductCategory();
        UserInfo mockUserInfo = new UserInfo();
        DeviceInfo mockDeviceInfo = new DeviceInfo();
        when(productCategoryHandler.getCategoryAsync(any())).thenReturn(CompletableFuture.completedFuture(mockCategory));
        when(userInfoResolver.getUserInfo(anyBoolean(), any(), any())).thenReturn(CompletableFuture.completedFuture(mockUserInfo));
        when(deviceInfoResolver.getDeviceInfo(anyBoolean(), any())).thenReturn(CompletableFuture.completedFuture(mockDeviceInfo));
        requestTransformer.process(context);
        assertNotNull(context.getSpiRequest());
        assertNotNull(context.getProductCategory());
        assertNotNull(context.getUserInfo());
        assertNotNull(context.getDeviceInfo());
    }

    @Test
    public void testGetCustomParamWhenCustomParamStrIsNull() throws Throwable {
        // arrange
        String customParamStr = null;
        // act
        CustomParam result = RequestTransformer.getCustomParam(customParamStr);
        // assert
        assertNotNull(result);
    }

    @Test(expected = CustomParseException.class)
    public void testGetCustomParamWhenCustomParamStrIsNotNullButExceptionOccurs() throws Throwable {
        // arrange
        String customParamStr = "%";
        // act
        RequestTransformer.getCustomParam(customParamStr);
    }

    @Test
    public void testGetCustomParamWhenCustomParamStrIsNotNullAndResultIsEmpty() throws Throwable {
        // arrange
        String customParamStr = "{}";
        // act
        CustomParam result = RequestTransformer.getCustomParam(customParamStr);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetCustomParamWhenCustomParamStrIsNotNullAndResultIsNotEmpty() throws Throwable {
        // arrange
        String customParamStr = "{\"key\":\"value\"}";
        // act
        CustomParam result = RequestTransformer.getCustomParam(customParamStr);
        // assert
        assertNotNull(result);
        // Verify the custom parameters map contains the expected key-value pair
        assertNotNull(result.getCustomParams());
        assertEquals("value", result.getCustomParams().get("key"));
    }
}
