package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 20:38
 */
@Data
@TypeDoc(description = "页面可选配置")
public abstract class AbstractMergeRule implements Serializable {

    @FieldDoc(description = "可选路由key", requiredness = Requiredness.REQUIRED)
    private Map<OptionalRoutingKeyEnum, String> optionalRoutingKeyMap = new HashMap<>();

    @FieldDoc(description = "合并策略", requiredness = Requiredness.REQUIRED)
    @JsonIgnore
    public abstract ConfigMergeTypeEnum getMergeType();

    @JsonIgnore
    public Set<OptionalRoutingKeyEnum> getRoutingKeySet() {
        return optionalRoutingKeyMap.keySet();
    }

    public String buildOptionalKey() {
        return Arrays.stream(OptionalRoutingKeyEnum.values())
                .filter(key -> optionalRoutingKeyMap.containsKey(key))
                .map(key -> {
                    String value = optionalRoutingKeyMap.get(key);
                    return String.format("%s:%s", key.name(), value);
                }).collect(Collectors.joining("-"));
    }

}
