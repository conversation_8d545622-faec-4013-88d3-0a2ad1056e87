package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.AbstractMergeRule;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 17:09
 */
@Data
@TypeDoc(description = "页面配置模板，根据不同场景计算出不同的页面配置")
public class PageConfigTemplateDTO implements Serializable {

    @FieldDoc(description = "兜底页面配置", requiredness = Requiredness.REQUIRED)
    private PageConfigDataDTO mainConfigData;

    @FieldDoc(description = "可选后处理规则", requiredness = Requiredness.OPTIONAL)
    private List<AbstractMergeRule> optionalRules = new ArrayList<>();

}
