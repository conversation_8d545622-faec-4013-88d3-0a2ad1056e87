package com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.tab;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.page.config.error.LayoutConfigFatalException;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.ModuleConfigDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;

class TabModuleConfigDTOTest {

    /**
     * 测试深度小于10且有子模块的情况
     */
    @Test
    public void testGetAllModuleConfigsNormalWithChildren() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        TabModuleItemDTO tabItem = mock(TabModuleItemDTO.class);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        ModuleConfigDTO childModule = mock(ModuleConfigDTO.class);
        when(tabItem.getGroupConfig()).thenReturn(Collections.singletonList(groupConfig));
        when(groupConfig.getModuleList()).thenReturn(Collections.singletonList(childModule));
        when(childModule.getAllModuleConfigs(anyInt())).thenReturn(Collections.singletonList(childModule));
        tabItems.add(tabItem);
        tabModule.setTabItemList(tabItems);
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains(tabModule));
        assertTrue(result.contains(childModule));
    }

    /**
     * 测试深度小于10且无子模块的情况
     */
    @Test
    public void testGetAllModuleConfigsNormalWithoutChildren() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        tabModule.setTabItemList(new ArrayList<>());
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(tabModule));
    }

    /**
     * 测试深度等于边界值9的情况
     */
    @Test
    public void testGetAllModuleConfigsBoundaryDepth9() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        tabModule.setTabItemList(new ArrayList<>());
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(9);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(tabModule));
    }

    /**
     * 测试深度等于10抛出异常的情况
     */
    @Test
    public void testGetAllModuleConfigsDepth10ThrowsException() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        tabModule.setTabItemList(new ArrayList<>());
        // act & assert
        LayoutConfigFatalException exception = assertThrows(LayoutConfigFatalException.class, () -> tabModule.getAllModuleConfigs(10));
        assertEquals("超过模块最大嵌套层数，请检查配置", exception.getMessage());
    }

    /**
     * 测试深度大于10抛出异常的情况
     */
    @Test
    public void testGetAllModuleConfigsDepthGreaterThan10ThrowsException() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        tabModule.setTabItemList(new ArrayList<>());
        // act & assert
        LayoutConfigFatalException exception = assertThrows(LayoutConfigFatalException.class, () -> tabModule.getAllModuleConfigs(11));
        assertEquals("超过模块最大嵌套层数，请检查配置", exception.getMessage());
    }

    /**
     * 测试tabItemList为空的情况
     */
    @Test
    public void testGetAllModuleConfigsEmptyTabItemList() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        // 使用空列表而不是null
        tabModule.setTabItemList(new ArrayList<>());
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(tabModule));
    }

    /**
     * 测试groupConfig为空的情况
     */
    @Test
    public void testGetAllModuleConfigsEmptyGroupConfig() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        TabModuleItemDTO tabItem = mock(TabModuleItemDTO.class);
        when(tabItem.getGroupConfig()).thenReturn(new ArrayList<>());
        tabItems.add(tabItem);
        tabModule.setTabItemList(tabItems);
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(tabModule));
    }

    /**
     * 测试moduleList为空的情况
     */
    @Test
    public void testGetAllModuleConfigsEmptyModuleList() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        TabModuleItemDTO tabItem = mock(TabModuleItemDTO.class);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        when(tabItem.getGroupConfig()).thenReturn(Collections.singletonList(groupConfig));
        when(groupConfig.getModuleList()).thenReturn(new ArrayList<>());
        tabItems.add(tabItem);
        tabModule.setTabItemList(tabItems);
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(tabModule));
    }

    /**
     * 测试多层嵌套的情况
     */
    @Test
    public void testGetAllModuleConfigsMultiLevelNesting() throws Throwable {
        // arrange
        TabModuleConfigDTO tabModule = new TabModuleConfigDTO();
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        TabModuleItemDTO tabItem = mock(TabModuleItemDTO.class);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        // 使用真实对象而不是mock
        TabModuleConfigDTO childTabModule = new TabModuleConfigDTO();
        ModuleConfigDTO leafModule = mock(ModuleConfigDTO.class);
        // 设置子模块的tabItemList，避免NPE
        childTabModule.setTabItemList(new ArrayList<>());
        when(tabItem.getGroupConfig()).thenReturn(Collections.singletonList(groupConfig));
        when(groupConfig.getModuleList()).thenReturn(Collections.singletonList(childTabModule));
        when(leafModule.getAllModuleConfigs(anyInt())).thenReturn(Collections.singletonList(leafModule));
        tabItems.add(tabItem);
        tabModule.setTabItemList(tabItems);
        // act
        List<ModuleConfigDTO> result = tabModule.getAllModuleConfigs(0);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains(tabModule));
        assertTrue(result.contains(childTabModule));
    }
}
