package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.PageReplaceRule;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:11
 */
public class PageReplaceHandler implements ConfigMergeHandler<PageReplaceRule> {

    @Override
    public void merge(final PageConfigDataDTO mainConfigData, final PageReplaceRule rule) {
        if (rule.getMainConfigData() == null) {
            return;
        }
        mainConfigData.setModuleGroupConfigs(rule.getMainConfigData().getModuleGroupConfigs());
    }

    @Override
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.PAGE_REPLACE;
    }

}
