package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;


import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSCycleChecker;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/1/31 13:17
 */
public class DAGBFSCycleCheckerTest {

    @Test(expected = FetcherDAGException.class)
    public void test() {
        Map<String, Set<String>> cyclicChildrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("B"));
            put("A", Sets.newHashSet("B"));
            put("B", Sets.newHashSet("C"));
            put("C", Sets.newHashSet("D", "M"));
            put("D", Sets.newHashSet("E"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("C"));// 形成环 C->D->E->F->C
            put("M", Sets.newHashSet("N"));
            put("N", Sets.newHashSet("B"));//形成环 B->C->M->N->B
        }};
        Map<String, Set<String>> cyclicParentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(cyclicChildrenGraph);
        DAGBFSCycleChecker.KahnResult kahnResult = DAGBFSCycleChecker.performKahnCheck(cyclicChildrenGraph, cyclicParentGraph);
        kahnResult.checkCycle(cyclicChildrenGraph);
    }

}