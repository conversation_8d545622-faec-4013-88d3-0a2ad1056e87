package com.sankuai.dz.product.detail.gateway.domain.page.config;

import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.LayoutConfigFatalException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/4/29 09:45
 */
@Component
public class PageConfigMainFunction implements InitializingBean {

    @Resource
    private List<PageConfigBaseHandler> pageConfigHandlerList;
    private static final Map<PageRegionEnum, PageConfigBaseHandler> pageConfigHandlerMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        for (PageConfigBaseHandler pageConfigBaseHandler : pageConfigHandlerList) {
            for (PageRegionEnum pageRegionEnum : pageConfigBaseHandler.getSupportedPageRegion()) {
                if (pageConfigHandlerMap.containsKey(pageRegionEnum)) {
                    throw new IllegalArgumentException("多个pageConfigHandler支持同一个pageRegionEnum:" + pageRegionEnum.name());
                }
                pageConfigHandlerMap.put(pageRegionEnum, pageConfigBaseHandler);
            }
        }
    }

    public void process(final GatewayContext context) {
        PageRegionEnum pageRegionEnum = PageRegionEnum.fromCode(context.getUnifiedRequest().getPageRegion());
        PageConfigBaseHandler pageConfigHandler = pageConfigHandlerMap.get(pageRegionEnum);
        if (pageConfigHandler == null) {
            throw new LayoutConfigFatalException("获取不到pageConfigHandler，pageRegionEnum:" + pageRegionEnum.name());
        }
        pageConfigHandler.process(context);
    }

}
