package com.sankuai.dz.api.module.arrange.framework.context;

import com.meituan.mtrace.context.TransmissibleThreadLocal;
import com.sankuai.dz.api.module.arrange.framework.context.exception.RunnerContextFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.lowcode.LowCodeContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.Getter;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/4/16 16:09
 */
@SuppressWarnings("rawtypes")
public class RunnerContext {

    /**
     * MTrace的TransmissibleThreadLocal支持跨线程（线程池）传递上下文
     */
    private static final TransmissibleThreadLocal<RunnerContext> userContext = new TransmissibleThreadLocal<>();

    public static RunnerContext getRunnerContext() {
        return userContext.get();
    }

    public static void setRunnerContext(RunnerContext runnerContext) {
        userContext.set(runnerContext);
    }

    public static void clearRunnerContext() {
        userContext.remove();
    }

    /**
     * 公共参数，理论上只读
     * tips：代码上为了性能考虑设置为非只读，非全局变量，随意修改只会影响单次请求结果
     */
    @Getter
    private final ProductDetailPageRequest request;

    /**
     * Fetcher返回值
     * tips：代码上为了性能考虑设置为非只读，非全局变量，随意修改只会影响单次请求结果
     */
    @Getter
    private final ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap;

    @Getter
    private final LowCodeContext lowCodeContext;

    public RunnerContext(final ProductDetailPageRequest request,
                         final ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap,
                         final LowCodeContext lowCodeContext) {
        this.request = request;
        this.fetcherContextMap = fetcherContextMap;
        this.lowCodeContext = lowCodeContext;
    }

    /**
     * 检查上下文有效性，如果有问题直接抛出异常，理论上不会，除非框架代码有bug
     */
    public void checkParam() {
        if (this.request == null
                || this.fetcherContextMap == null
                || this.lowCodeContext == null) {
            throw new RunnerContextFatalException("FATAL ERROR!!!上下文中关键参数为null!!!请务必立刻马上检查代码");
        }
    }

}
