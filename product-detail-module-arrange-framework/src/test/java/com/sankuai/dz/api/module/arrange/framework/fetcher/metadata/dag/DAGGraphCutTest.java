package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DAGGraphCutTest {

    @Mock
    private Map<String, Set<String>> mockChildrenGraph;

    @Mock
    private Map<String, Set<String>> mockParentGraph;

    /**
     * Test normal case with simple valid DAG
     */
    @Test
    public void testGraphCut_SimpleValidDAG_Success() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>(Arrays.asList("B", "C")));
        childrenGraph.put("B", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("C", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("D", new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        parentGraph.put("B", new HashSet<>(Collections.singletonList("A")));
        parentGraph.put("C", new HashSet<>(Collections.singletonList("A")));
        parentGraph.put("D", new HashSet<>(Arrays.asList("B", "C")));
        // act
        DAGGraphCut.DAGGraphCutResult result = DAGGraphCut.graphCut("A", childrenGraph, parentGraph);
        // assert
        assertNotNull(result);
        assertEquals(4, result.getChildrenGraph().size());
        assertEquals(Arrays.asList("A", "B", "C", "D"), result.getTopologicalSort());
        assertTrue(result.getParentGraph().get("D").containsAll(Arrays.asList("B", "C")));
    }

    /**
     * Test error case when start node is not in graph
     */
    @Test
    public void testGraphCut_StartNodeNotInGraph_ThrowsException() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        // act & assert
        FetcherDAGException exception = assertThrows(FetcherDAGException.class, () -> DAGGraphCut.graphCut("X", childrenGraph, parentGraph));
        assertEquals("DAG中没有起始点", exception.getMessage());
    }

    /**
     * Test error case when start node has non-zero in-degree
     */
    @Test
    public void testGraphCut_StartNodeWithNonZeroInDegree_ThrowsException() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>(Collections.singletonList("B")));
        childrenGraph.put("B", new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>(Collections.singletonList("Z")));
        parentGraph.put("B", new HashSet<>(Collections.singletonList("A")));
        // act & assert
        FetcherDAGException exception = assertThrows(FetcherDAGException.class, () -> DAGGraphCut.graphCut("B", childrenGraph, parentGraph));
        assertEquals("B不是开始节点，入度不为0", exception.getMessage());
    }

    /**
     * Test case for DAG with multiple paths and cycles verification
     */
    @Test
    public void testGraphCut_ComplexDAGWithMultiplePaths_Success() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>(Arrays.asList("B", "C")));
        childrenGraph.put("B", new HashSet<>(Collections.singletonList("D")));
        childrenGraph.put("C", new HashSet<>(Arrays.asList("D", "E")));
        childrenGraph.put("D", new HashSet<>(Collections.singletonList("F")));
        childrenGraph.put("E", new HashSet<>(Collections.singletonList("F")));
        childrenGraph.put("F", new HashSet<>());
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        // act
        DAGGraphCut.DAGGraphCutResult result = DAGGraphCut.graphCut("A", childrenGraph, parentGraph);
        // assert
        assertNotNull(result);
        assertEquals(6, result.getChildrenGraph().size());
        assertTrue(result.getTopologicalSort().indexOf("A") < result.getTopologicalSort().indexOf("F"));
        assertEquals(2, result.getParentGraph().get("F").size());
    }

    /**
     * Test case for single node DAG
     */
    @Test
    public void testGraphCut_SingleNodeDAG_Success() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        // act
        DAGGraphCut.DAGGraphCutResult result = DAGGraphCut.graphCut("A", childrenGraph, parentGraph);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getChildrenGraph().size());
        assertEquals(Collections.singletonList("A"), result.getTopologicalSort());
        assertTrue(result.getParentGraph().get("A").isEmpty());
    }

    /**
     * Test case for DAG with empty children set
     */
    @Test
    public void testGraphCut_EmptyChildrenSet_Success() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        childrenGraph.put("A", new HashSet<>());
        Map<String, Set<String>> parentGraph = new HashMap<>();
        parentGraph.put("A", new HashSet<>());
        // act
        DAGGraphCut.DAGGraphCutResult result = DAGGraphCut.graphCut("A", childrenGraph, parentGraph);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getChildrenGraph().size());
        assertEquals(Collections.singletonList("A"), result.getTopologicalSort());
        assertTrue(result.getChildrenGraph().get("A").isEmpty());
    }
}
