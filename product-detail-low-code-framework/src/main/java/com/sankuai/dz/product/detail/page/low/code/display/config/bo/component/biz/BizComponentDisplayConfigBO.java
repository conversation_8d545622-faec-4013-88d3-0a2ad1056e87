package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 10:28
 */
@Getter
public abstract class BizComponentDisplayConfigBO extends ComponentDisplayConfigBO {

    /**
     * 所需数据源
     */
    private final Map<String, DataSourceConfigBO> dataSource;

    public BizComponentDisplayConfigBO(final String componentKey,
                                       final Map<String, DataSourceConfigBO> dataSource) {
        super(componentKey);
        this.dataSource = Collections.unmodifiableMap(dataSource);
    }

    @Override
    public List<DataSourceConfigBO> getDataSourceConfigs() {
        return new ArrayList<>(dataSource.values());
    }

    @Override
    public ComponentGroupEnum getComponentGroup() {
        return ComponentGroupEnum.Business;
    }

    @Override
    public Set<ComponentTypeEnum> getSupportedComponentType() {
        //暂时可以支持所有业务组件
        return Arrays.stream(ComponentTypeEnum.values())
                .filter(type -> type.getComponentGroup() == ComponentGroupEnum.Business)
                .collect(Collectors.toSet());
    }

    /**
     * 获取业务组件所有的规则配置
     */
    public abstract List<RuleConfigDTO> getAllRuleConfigs();

    /**
     * 构建ComponentBO
     */
    public ComponentBO buildComponentBO(final Map<String, Object> data,
                                        final int depth) {
        ComponentDefinition componentDefinition = ComponentDefinitionStorage.getComponentMetadata(this.getComponentKey());
        return processBizComponent(componentDefinition, data);
    }

    /**
     * 根据规则引擎生成BO接口
     * 目前有
     * ①遍历组件BO类结构，按照字段维度进行赋值方式，用于M端表单配置，for产运
     * ②根据代码生成组件BO，用于实现AI-Coding
     */
    public abstract BizComponentBO processBizComponent(final ComponentDefinition componentDefinition,
                                                       final Map<String, Object> data);

}
