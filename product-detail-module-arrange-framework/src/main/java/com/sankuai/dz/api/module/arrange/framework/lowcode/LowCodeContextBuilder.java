package com.sankuai.dz.api.module.arrange.framework.lowcode;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigStorage;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/4 16:07
 */
@Slf4j
public class LowCodeContextBuilder {

    public static LowCodeContext build(final ModuleArrangeRequest moduleArrangeRequest) {
        try {
            ConfigQueryResponse response = DisplayConfigStorage.getConfig(
                    moduleArrangeRequest.getRequest().getPageConfigRoutingKey()
            );
            if (response.getConfig() == null) {
                return new LowCodeContext();
            }
            //根据请求中的moduleKey过滤对应的模块配置
            final Map<String, ModuleDisplayConfigBO> filteredConfigs = getFilteredModuleDisplayConfigs(
                    moduleArrangeRequest, response.getConfig()
            );
            //根据请求中的moduleKey过滤出关联数据源配置
            final Map<String, DataSourceConfigBO> filteredDataSource = getFilteredDataSource(
                    filteredConfigs, response
            );
            return new LowCodeContext(filteredConfigs, filteredDataSource);
        } catch (Throwable throwable) {
            log.error("LowCodeContextBuilder.build,moduleArrangeRequest:{}", JSON.toJSONString(moduleArrangeRequest), throwable);
            return new LowCodeContext();
        }
    }

    /**
     * 过滤出本次请求相关的模块配置
     */
    private static Map<String, ModuleDisplayConfigBO> getFilteredModuleDisplayConfigs(ModuleArrangeRequest moduleArrangeRequest, PageDisplayConfigBO pageDisplayConfig) {
        return pageDisplayConfig.getModuleConfigs().entrySet()
                .stream()
                .filter(entry -> moduleArrangeRequest.getModuleKeys().contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 根据请求中的moduleKey过滤出关联数据源配置
     */
    private static Map<String, DataSourceConfigBO> getFilteredDataSource(Map<String, ModuleDisplayConfigBO> filteredConfigs, ConfigQueryResponse response) {
        final Set<String> filteredDataSourceFetcherNames = filteredConfigs.values()
                .stream()
                .map(config -> config.getAllDataSource().keySet())
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        return response.getConfig().getAllDataSource().entrySet()
                .stream()
                .filter(entry -> filteredDataSourceFetcherNames.contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (v1, v2) -> v1
                ));
    }

}
