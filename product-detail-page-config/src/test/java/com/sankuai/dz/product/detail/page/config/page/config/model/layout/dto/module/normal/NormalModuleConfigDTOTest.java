package com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.normal;

import static org.junit.jupiter.api.Assertions.*;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.overlay.OverlayModuleConfigDTO;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;

import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.normal.NormalModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.overlay.OverlayModuleVO;
import com.sankuai.dz.product.detail.page.config.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.page.config.page.config.model.request.BuildModuleVORequest;
import java.util.ArrayList;
import java.util.Arrays;

@ExtendWith(MockitoExtension.class)
class NormalModuleConfigDTOTest {

    @InjectMocks
    private NormalModuleConfigDTO normalModuleConfigDTO;

    @Mock
    private OverlayModuleConfigDTO mockOverlayModule1;

    @Mock
    private OverlayModuleConfigDTO mockOverlayModule2;

    @Mock
    private BuildModuleVORequest request;

    /**
     * Test scenario: getAllModuleConfigs when overlayModules is null
     * Expected: Should return a list containing only the current instance
     */
    @Test
    public void testGetAllModuleConfigsWhenOverlayModulesIsNull() {
        // arrange
        normalModuleConfigDTO.setOverlayModules(null);
        // act
        List<ModuleConfigDTO> result = normalModuleConfigDTO.getAllModuleConfigs(0);
        // assert
        assertNotNull(result, "Result list should not be null");
        assertEquals(1, result.size(), "Result should contain only the current instance");
        assertSame(normalModuleConfigDTO, result.get(0), "First element should be the current instance");
    }

    /**
     * Test scenario: getAllModuleConfigs when overlayModules is empty
     * Expected: Should return a list containing only the current instance
     */
    @Test
    public void testGetAllModuleConfigsWhenOverlayModulesIsEmpty() {
        // arrange
        normalModuleConfigDTO.setOverlayModules(Lists.newArrayList());
        // act
        List<ModuleConfigDTO> result = normalModuleConfigDTO.getAllModuleConfigs(0);
        // assert
        assertNotNull(result, "Result list should not be null");
        assertEquals(1, result.size(), "Result should contain only the current instance");
        assertSame(normalModuleConfigDTO, result.get(0), "First element should be the current instance");
    }

    /**
     * Test scenario: getAllModuleConfigs when overlayModules contains elements
     * Expected: Should return a list containing the current instance and all overlay modules
     */
    @Test
    public void testGetAllModuleConfigsWhenOverlayModulesHasElements() {
        // arrange
        List<OverlayModuleConfigDTO> overlayModules = Lists.newArrayList(mockOverlayModule1, mockOverlayModule2);
        normalModuleConfigDTO.setOverlayModules(overlayModules);
        // act
        List<ModuleConfigDTO> result = normalModuleConfigDTO.getAllModuleConfigs(0);
        // assert
        assertNotNull(result, "Result list should not be null");
        assertEquals(3, result.size(), "Result should contain current instance and overlay modules");
        assertSame(normalModuleConfigDTO, result.get(0), "First element should be the current instance");
        assertTrue(result.contains(mockOverlayModule1), "Result should contain first overlay module");
        assertTrue(result.contains(mockOverlayModule2), "Result should contain second overlay module");
    }

    /**
     * Test scenario: getAllModuleConfigs with different depth values
     * Expected: Depth parameter should not affect the result
     */
    @Test
    public void testGetAllModuleConfigsWithDifferentDepthValues() {
        // arrange
        List<OverlayModuleConfigDTO> overlayModules = Lists.newArrayList(mockOverlayModule1);
        normalModuleConfigDTO.setOverlayModules(overlayModules);
        // act
        List<ModuleConfigDTO> resultDepthZero = normalModuleConfigDTO.getAllModuleConfigs(0);
        List<ModuleConfigDTO> resultDepthPositive = normalModuleConfigDTO.getAllModuleConfigs(1);
        List<ModuleConfigDTO> resultDepthNegative = normalModuleConfigDTO.getAllModuleConfigs(-1);
        // assert
        assertEquals(resultDepthZero.size(), resultDepthPositive.size(), "Results should be same regardless of depth");
        assertEquals(resultDepthZero.size(), resultDepthNegative.size(), "Results should be same regardless of depth");
        assertEquals(2, resultDepthZero.size(), "Result should contain current instance and overlay module");
    }

    /**
     * Test scenario: getAllModuleConfigs verifying the order of elements
     * Expected: Current instance should always be first, followed by overlay modules
     */
    @Test
    public void testGetAllModuleConfigsElementOrder() {
        // arrange
        List<OverlayModuleConfigDTO> overlayModules = Lists.newArrayList(mockOverlayModule1, mockOverlayModule2);
        normalModuleConfigDTO.setOverlayModules(overlayModules);
        // act
        List<ModuleConfigDTO> result = normalModuleConfigDTO.getAllModuleConfigs(0);
        // assert
        assertNotNull(result, "Result list should not be null");
        assertEquals(3, result.size(), "Result should contain three elements");
        assertSame(normalModuleConfigDTO, result.get(0), "First element must be current instance");
        assertTrue(result.subList(1, result.size()).containsAll(overlayModules), "Remaining elements should be overlay modules");
    }

    @Test
    public void testBuildVOWithNullOverlayModules() throws Throwable {
        // arrange
        NormalModuleConfigDTO dto = new NormalModuleConfigDTO();
        dto.setFirstScreenFlag(1);
        dto.setModuleKey("testKey");
        dto.setOverlayModules(null);
        // act
        ModuleConfigVO result = dto.buildVO(request, 0);
        // assert
        assertTrue(result instanceof NormalModuleConfigVO);
        NormalModuleConfigVO normalVO = (NormalModuleConfigVO) result;
        assertEquals(1, normalVO.getFirstScreenFlag());
        assertEquals("testKey", normalVO.getModuleKey());
        assertEquals(ModuleTypeEnum.normal.name(), normalVO.getModuleType());
        assertNull(normalVO.getOverlayModules());
    }

    @Test
    public void testBuildVOWithEmptyOverlayModules() throws Throwable {
        // arrange
        NormalModuleConfigDTO dto = new NormalModuleConfigDTO();
        dto.setFirstScreenFlag(1);
        dto.setModuleKey("testKey");
        dto.setOverlayModules(new ArrayList<>());
        // act
        ModuleConfigVO result = dto.buildVO(request, 0);
        // assert
        assertTrue(result instanceof NormalModuleConfigVO);
        NormalModuleConfigVO normalVO = (NormalModuleConfigVO) result;
        assertEquals(1, normalVO.getFirstScreenFlag());
        assertEquals("testKey", normalVO.getModuleKey());
        assertEquals(ModuleTypeEnum.normal.name(), normalVO.getModuleType());
        assertNull(normalVO.getOverlayModules(), "Overlay modules should be null for empty list");
    }

    @Test
    public void testBuildVOWithOverlayModules() throws Throwable {
        // arrange
        NormalModuleConfigDTO dto = new NormalModuleConfigDTO();
        dto.setFirstScreenFlag(1);
        dto.setModuleKey("testKey");
        OverlayModuleConfigDTO overlayModule1 = new OverlayModuleConfigDTO();
        overlayModule1.setModuleKey("overlay1");
        overlayModule1.setFirstScreenFlag(2);
        OverlayModuleConfigDTO overlayModule2 = new OverlayModuleConfigDTO();
        overlayModule2.setModuleKey("overlay2");
        overlayModule2.setFirstScreenFlag(3);
        dto.setOverlayModules(Arrays.asList(overlayModule1, overlayModule2));
        // act
        ModuleConfigVO result = dto.buildVO(request, 0);
        // assert
        assertTrue(result instanceof NormalModuleConfigVO);
        NormalModuleConfigVO normalVO = (NormalModuleConfigVO) result;
        assertEquals(1, normalVO.getFirstScreenFlag());
        assertEquals("testKey", normalVO.getModuleKey());
        assertEquals(ModuleTypeEnum.normal.name(), normalVO.getModuleType());
        List<OverlayModuleVO> overlayModules = normalVO.getOverlayModules();
        assertNotNull(overlayModules);
        assertEquals(2, overlayModules.size());
        assertEquals("overlay1", overlayModules.get(0).getModuleKey());
        assertEquals(2, overlayModules.get(0).getFirstScreenFlag());
        assertEquals(ModuleTypeEnum.overlay.name(), overlayModules.get(0).getModuleType());
        assertEquals("overlay2", overlayModules.get(1).getModuleKey());
        assertEquals(3, overlayModules.get(1).getFirstScreenFlag());
        assertEquals(ModuleTypeEnum.overlay.name(), overlayModules.get(1).getModuleType());
    }

    @Test
    public void testBuildVOWithDepth() throws Throwable {
        // arrange
        NormalModuleConfigDTO dto = new NormalModuleConfigDTO();
        dto.setFirstScreenFlag(1);
        dto.setModuleKey("testKey");
        OverlayModuleConfigDTO overlayModule = new OverlayModuleConfigDTO();
        overlayModule.setModuleKey("overlay1");
        overlayModule.setFirstScreenFlag(2);
        dto.setOverlayModules(Arrays.asList(overlayModule));
        // act
        ModuleConfigVO result = dto.buildVO(request, 5);
        // assert
        assertTrue(result instanceof NormalModuleConfigVO);
        NormalModuleConfigVO normalVO = (NormalModuleConfigVO) result;
        assertNotNull(normalVO.getOverlayModules());
        assertEquals(1, normalVO.getOverlayModules().size());
    }
}
