package com.sankuai.dz.api.module.arrange.framework.utils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/2/10 00:56
 */
public class GenericTypeResolver {

    /**
     * 获取指定父类的第 index 个泛型实际类型
     *
     * @param clazz        子类的 Class 对象
     * @param targetParent 目标父类的 Class 对象
     * @param index        泛型参数的位置（从 0 开始）
     * @return 实际类型，若未找到则返回 null
     */
    public static Class<?> getGenericActualType(Class<?> clazz, Class<?> targetParent, int index) {
        return resolveGenericType(clazz, targetParent, index, new HashMap<>());
    }

    private static Class<?> resolveGenericType(Class<?> clazz, Class<?> targetParent, int index, Map<TypeVariable<?>, Type> typeMap) {
        // 到达目标父类时，解析其泛型参数
        if (clazz == targetParent) {
            TypeVariable<?>[] typeParams = clazz.getTypeParameters();
            if (index < typeParams.length) {
                Type resolvedType = typeMap.get(typeParams[index]);
                return getClassFromType(resolvedType);
            }
            return null;
        }

        // 处理父类
        Type superClassType = clazz.getGenericSuperclass();
        Class<?> superClass = null;
        Map<TypeVariable<?>, Type> superTypeMap = new HashMap<>(typeMap);

        if (superClassType instanceof ParameterizedType) {
            ParameterizedType pSuperType = (ParameterizedType) superClassType;
            superClass = (Class<?>) pSuperType.getRawType();
            buildTypeMap(pSuperType, superTypeMap);
        } else if (superClassType instanceof Class) {
            superClass = (Class<?>) superClassType;
        }

        if (superClass != null && superClass != Object.class) {
            Class<?> result = resolveGenericType(superClass, targetParent, index, superTypeMap);
            if (result != null) return result;
        }

        // 处理接口
        for (Type interfaceType : clazz.getGenericInterfaces()) {
            Class<?> interfaceClass = null;
            Map<TypeVariable<?>, Type> interfaceTypeMap = new HashMap<>(typeMap);

            if (interfaceType instanceof ParameterizedType) {
                ParameterizedType pInterface = (ParameterizedType) interfaceType;
                interfaceClass = (Class<?>) pInterface.getRawType();
                buildTypeMap(pInterface, interfaceTypeMap);
            } else if (interfaceType instanceof Class) {
                interfaceClass = (Class<?>) interfaceType;
            }

            if (interfaceClass != null) {
                Class<?> result = resolveGenericType(interfaceClass, targetParent, index, interfaceTypeMap);
                if (result != null) return result;
            }
        }

        return null;
    }

    /**
     * 构建类型变量到实际类型的映射
     */
    private static void buildTypeMap(ParameterizedType type, Map<TypeVariable<?>, Type> typeMap) {
        Class<?> rawClass = (Class<?>) type.getRawType();
        TypeVariable<?>[] typeParams = rawClass.getTypeParameters();
        Type[] actualArgs = type.getActualTypeArguments();

        for (int i = 0; i < typeParams.length; i++) {
            Type resolvedArg = resolveType(actualArgs[i], typeMap);
            typeMap.put(typeParams[i], resolvedArg);
        }
    }

    /**
     * 递归解析类型（处理嵌套泛型）
     */
    private static Type resolveType(Type type, Map<TypeVariable<?>, Type> typeMap) {
        if (type instanceof TypeVariable) {
            return typeMap.getOrDefault(type, type);
        } else if (type instanceof ParameterizedType) {
            ParameterizedType pType = (ParameterizedType) type;
            Type[] actualArgs = pType.getActualTypeArguments();
            Type[] resolvedArgs = new Type[actualArgs.length];
            for (int i = 0; i < actualArgs.length; i++) {
                resolvedArgs[i] = resolveType(actualArgs[i], typeMap);
            }
            return new ParameterizedTypeImpl((Class<?>) pType.getRawType(), resolvedArgs, null);
        }
        return type;
    }

    /**
     * 从 Type 中提取 Class 类型
     */
    private static Class<?> getClassFromType(Type type) {
        if (type instanceof Class) {
            return (Class<?>) type;
        } else if (type instanceof ParameterizedType) {
            Type rawType = ((ParameterizedType) type).getRawType();
            if (rawType instanceof Class) {
                return (Class<?>) rawType;
            }
        }
        return null;
    }

    // 自定义 ParameterizedType 实现（仅用于演示）
    private static class ParameterizedTypeImpl implements ParameterizedType {
        private final Class<?> rawType;
        private final Type[] actualTypeArgs;
        private final Type ownerType;

        ParameterizedTypeImpl(Class<?> rawType, Type[] actualTypeArgs, Type ownerType) {
            this.rawType = rawType;
            this.actualTypeArgs = actualTypeArgs;
            this.ownerType = ownerType;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return actualTypeArgs;
        }

        @Override
        public Type getRawType() {
            return rawType;
        }

        @Override
        public Type getOwnerType() {
            return ownerType;
        }
    }

}