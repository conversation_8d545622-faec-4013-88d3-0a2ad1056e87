package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.lang.reflect.Field;
import java.util.concurrent.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BaseFetcherContextGetFetchResponseTest {

    @Mock
    private FetcherDefinition mockFetcherDefinition;

    /**
     * Test case: Should return cached response if it exists
     */
    @Test
    public void testGetFetchResponse_WithExistingCache() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        FetcherResponse<TestDTO> cachedResponse = FetcherResponse.succeed(new TestDTO());
        Field cacheField = BaseFetcherContext.class.getDeclaredField("fetchResponseCache");
        cacheField.setAccessible(true);
        cacheField.set(context, cachedResponse);
        // act
        FetcherResponse<TestDTO> result = context.getFetchResponse();
        // assert
        assertNotNull(result);
        assertSame(cachedResponse, result, "Should return the cached response");
        // cleanup
        cacheField.setAccessible(false);
    }

    /**
     * Test case: Should get response from future successfully
     */
    @Test
    public void testGetFetchResponse_SuccessfulFutureGet() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        FetcherResponse<TestDTO> expectedResponse = FetcherResponse.succeed(new TestDTO());
        CompletableFuture<FetcherResponse<TestDTO>> future = CompletableFuture.completedFuture(expectedResponse);
        context.future = future;
        when(mockFetcherDefinition.getTimeout()).thenReturn(1000L);
        context.fetcherDefinition = mockFetcherDefinition;
        // act
        FetcherResponse<TestDTO> result = context.getFetchResponse();
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertSame(expectedResponse, result);
    }

    /**
     * Test case: Should return fail response when future.get() returns null
     */
    @Test
    public void testGetFetchResponse_NullFutureResult() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        CompletableFuture<FetcherResponse<TestDTO>> future = CompletableFuture.completedFuture(null);
        context.future = future;
        when(mockFetcherDefinition.getTimeout()).thenReturn(1000L);
        when(mockFetcherDefinition.getFetcherName()).thenReturn("TestFetcher");
        context.fetcherDefinition = mockFetcherDefinition;
        // act
        FetcherResponse<TestDTO> result = context.getFetchResponse();
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getError() instanceof FetcherFatalException);
        assertTrue(result.getError().getMessage().contains("TestFetcher"));
    }

    /**
     * Test case: Should return fail response when future.get() throws TimeoutException
     */
    @Test
    public void testGetFetchResponse_TimeoutException() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        CompletableFuture<FetcherResponse<TestDTO>> future = new CompletableFuture<>();
        context.future = future;
        // Set very short timeout
        when(mockFetcherDefinition.getTimeout()).thenReturn(1L);
        context.fetcherDefinition = mockFetcherDefinition;
        // Create a delayed completion to ensure timeout
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(100);
                future.complete(FetcherResponse.succeed(new TestDTO()));
            } catch (InterruptedException e) {
                // ignore
            }
        });
        // act
        FetcherResponse<TestDTO> result = context.getFetchResponse();
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getError() instanceof TimeoutException, "Expected TimeoutException but got: " + (result.getError() != null ? result.getError().getClass() : "null"));
    }

    /**
     * Test case: Should return fail response when future.get() throws InterruptedException
     */
    @Test
    public void testGetFetchResponse_InterruptedException() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        CompletableFuture<FetcherResponse<TestDTO>> future = new CompletableFuture<>();
        context.future = future;
        when(mockFetcherDefinition.getTimeout()).thenReturn(1000L);
        context.fetcherDefinition = mockFetcherDefinition;
        // Interrupt the current thread after a short delay
        Thread currentThread = Thread.currentThread();
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(10);
                currentThread.interrupt();
            } catch (InterruptedException e) {
                // ignore
            }
        });
        // act
        FetcherResponse<TestDTO> result = context.getFetchResponse();
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getError() instanceof InterruptedException);
        // Clear interrupted status
        Thread.interrupted();
    }

    /**
     * Test case: Should cache the response after first call
     */
    @Test
    public void testGetFetchResponse_CachesResponse() throws Throwable {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        FetcherResponse<TestDTO> expectedResponse = FetcherResponse.succeed(new TestDTO());
        CompletableFuture<FetcherResponse<TestDTO>> future = CompletableFuture.completedFuture(expectedResponse);
        context.future = future;
        when(mockFetcherDefinition.getTimeout()).thenReturn(1000L);
        context.fetcherDefinition = mockFetcherDefinition;
        // act
        FetcherResponse<TestDTO> result1 = context.getFetchResponse();
        FetcherResponse<TestDTO> result2 = context.getFetchResponse();
        // Use reflection to verify cache
        Field cacheField = BaseFetcherContext.class.getDeclaredField("fetchResponseCache");
        cacheField.setAccessible(true);
        FetcherResponse<TestDTO> cachedResponse = (FetcherResponse<TestDTO>) cacheField.get(context);
        // assert
        assertNotNull(cachedResponse);
        assertSame(result1, result2, "Multiple calls should return same instance");
        assertSame(result1, cachedResponse, "Response should be cached");
        // cleanup
        cacheField.setAccessible(false);
    }

    // Helper classes for testing
    private static class TestDTO extends FetcherReturnValueDTO {
    }

    private static class TestFetcherContext extends BaseFetcherContext<TestDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }
}
