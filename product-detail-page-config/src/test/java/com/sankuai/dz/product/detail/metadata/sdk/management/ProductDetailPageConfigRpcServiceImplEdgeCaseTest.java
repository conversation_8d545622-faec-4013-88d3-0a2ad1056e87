package com.sankuai.dz.product.detail.metadata.sdk.management;

import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.ProductDetailPageConfigService;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductDetailPageConfigRpcServiceImpl 边界情况和异常场景测试
 * 专门测试各种边界条件和异常情况，提高代码覆盖率
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 20:30
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageConfigRpcServiceImplEdgeCaseTest {

    @InjectMocks
    private ProductDetailPageConfigRpcServiceImpl rpcService;

    @Mock
    private ProductDetailPageConfigService productDetailPageConfigService;

    @Before
    public void setUp() {
        // 重置所有 mock 对象
        reset(productDetailPageConfigService);
    }

    /**
     * 测试特殊字符路由key
     */
    @Test
    public void testWithSpecialCharacterRoutingKey() {
        PageConfigRoutingKey specialKey = ProductDetailPageConfigRpcServiceTestHelper.createSpecialCharacterRoutingKey();
        PageConfigTemplateDTO specialTemplate = ProductDetailPageConfigRpcServiceTestHelper.createMockConfigTemplate();
        PageConfigDataDTO specialConfig = ProductDetailPageConfigRpcServiceTestHelper.createMockPageConfigData();

        when(productDetailPageConfigService.getPageConfigTemplate(specialKey)).thenReturn(specialTemplate);
        when(productDetailPageConfigService.getPageConfig(specialKey)).thenReturn(specialConfig);

        PageConfigTemplateDTO templateResult = rpcService.queryConfigTemplate(specialKey);
        PageConfigDataDTO configResult = rpcService.getPageConfig(specialKey);

        assertNotNull("特殊字符路由key应该返回配置模板", templateResult);
        assertNotNull("特殊字符路由key应该返回页面配置", configResult);
        assertTrue("特殊字符路由key应该是有效的",
                ProductDetailPageConfigRpcServiceTestHelper.isValidRoutingKey(specialKey));

        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(specialKey);
        verify(productDetailPageConfigService, times(1)).getPageConfig(specialKey);
    }

    /**
     * 测试大型路由key集合
     */
    @Test
    public void testWithLargeRoutingKeySet() {
        Set<String> largeKeySet = ProductDetailPageConfigRpcServiceTestHelper.createLargeRoutingKeySet();

        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys()).thenReturn(largeKeySet);

        Set<String> result = rpcService.getAllPageRoutingKey();

        assertNotNull("大型路由key集合结果不应该为空", result);
        assertEquals("大型路由key集合大小应该正确", largeKeySet.size(), result.size());
        assertTrue("大型路由key集合应该包含所有元素", result.containsAll(largeKeySet));
        assertTrue("大型路由key集合应该是有效的",
                ProductDetailPageConfigRpcServiceTestHelper.isValidRoutingKeySet(result));

        verify(productDetailPageConfigService, times(1)).getAllPageConfigTemplateCacheKeys();
    }

    /**
     * 测试服务返回null的各种情况
     */
    @Test
    public void testWithServiceReturningNull() {
        PageConfigRoutingKey testKey = ProductDetailPageConfigRpcServiceTestHelper.createValidMTRoutingKey();

        // 测试 getPageConfigTemplate 返回 null
        when(productDetailPageConfigService.getPageConfigTemplate(testKey)).thenReturn(null);
        PageConfigTemplateDTO templateResult = rpcService.queryConfigTemplate(testKey);
        assertNull("服务返回null时，应该返回null", templateResult);

        // 测试 getPageConfig 返回 null
        when(productDetailPageConfigService.getPageConfig(testKey)).thenReturn(null);
        PageConfigDataDTO configResult = rpcService.getPageConfig(testKey);
        assertNull("服务返回null时，应该返回null", configResult);

        // 测试 getAllPageConfigTemplateCacheKeys 返回 null
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys()).thenReturn(null);
        Set<String> keysResult = rpcService.getAllPageRoutingKey();
        assertNull("服务返回null时，应该返回null", keysResult);

        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(testKey);
        verify(productDetailPageConfigService, times(1)).getPageConfig(testKey);
        verify(productDetailPageConfigService, times(1)).getAllPageConfigTemplateCacheKeys();
    }

    /**
     * 测试不同类型的异常
     */
    @Test
    public void testWithDifferentExceptionTypes() {
        PageConfigRoutingKey testKey = ProductDetailPageConfigRpcServiceTestHelper.createValidMTRoutingKey();

        // 测试 IllegalArgumentException
        when(productDetailPageConfigService.getPageConfigTemplate(testKey))
                .thenThrow(new IllegalArgumentException("参数非法"));

        try {
            rpcService.queryConfigTemplate(testKey);
            fail("应该抛出 IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            assertEquals("异常消息应该正确", "参数非法", e.getMessage());
        }

        // 重置mock
        reset(productDetailPageConfigService);

        // 测试 NullPointerException
        when(productDetailPageConfigService.getPageConfig(testKey))
                .thenThrow(new NullPointerException("空指针异常"));

        try {
            rpcService.getPageConfig(testKey);
            fail("应该抛出 NullPointerException");
        } catch (NullPointerException e) {
            assertEquals("异常消息应该正确", "空指针异常", e.getMessage());
        }

        // 重置mock
        reset(productDetailPageConfigService);

        // 测试 UnsupportedOperationException
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys())
                .thenThrow(new UnsupportedOperationException("不支持的操作"));

        try {
            rpcService.getAllPageRoutingKey();
            fail("应该抛出 UnsupportedOperationException");
        } catch (UnsupportedOperationException e) {
            assertEquals("异常消息应该正确", "不支持的操作", e.getMessage());
        }
    }

    /**
     * 测试路由key的相等性比较
     */
    @Test
    public void testRoutingKeyEquality() {
        PageConfigRoutingKey key1 = ProductDetailPageConfigRpcServiceTestHelper.createValidMTRoutingKey();
        PageConfigRoutingKey key2 = ProductDetailPageConfigRpcServiceTestHelper.copyRoutingKey(key1);
        PageConfigRoutingKey key3 = ProductDetailPageConfigRpcServiceTestHelper.createValidDPRoutingKey();

        // 测试相等的路由key
        assertTrue("相同的路由key应该相等",
                ProductDetailPageConfigRpcServiceTestHelper.areRoutingKeysEqual(key1, key2));

        // 测试不相等的路由key
        assertFalse("不同的路由key应该不相等",
                ProductDetailPageConfigRpcServiceTestHelper.areRoutingKeysEqual(key1, key3));

        // 测试null路由key
        assertFalse("null和非null路由key应该不相等",
                ProductDetailPageConfigRpcServiceTestHelper.areRoutingKeysEqual(key1, null));
        assertTrue("两个null路由key应该相等",
                ProductDetailPageConfigRpcServiceTestHelper.areRoutingKeysEqual(null, null));

        // 使用相等的路由key调用服务
        PageConfigTemplateDTO template = ProductDetailPageConfigRpcServiceTestHelper.createMockConfigTemplate();
        when(productDetailPageConfigService.getPageConfigTemplate(any(PageConfigRoutingKey.class)))
                .thenReturn(template);

        PageConfigTemplateDTO result1 = rpcService.queryConfigTemplate(key1);
        PageConfigTemplateDTO result2 = rpcService.queryConfigTemplate(key2);

        assertNotNull("相等路由key应该返回配置模板", result1);
        assertNotNull("相等路由key应该返回配置模板", result2);
        assertEquals("相等路由key应该返回相同的配置模板", result1, result2);

        verify(productDetailPageConfigService, times(2)).getPageConfigTemplate(any(PageConfigRoutingKey.class));
    }

    /**
     * 测试描述信息方法
     */
    @Test
    public void testDescriptionMethods() {
        PageConfigRoutingKey routingKey = ProductDetailPageConfigRpcServiceTestHelper.createValidMTRoutingKey();
        PageConfigTemplateDTO template = ProductDetailPageConfigRpcServiceTestHelper.createMockConfigTemplate();
        PageConfigDataDTO config = ProductDetailPageConfigRpcServiceTestHelper.createMockPageConfigData();
        Set<String> keys = ProductDetailPageConfigRpcServiceTestHelper.createMockRoutingKeySet();

        // 测试描述信息方法
        String routingKeyDesc = ProductDetailPageConfigRpcServiceTestHelper.getRoutingKeyDescription(routingKey);
        String templateDesc = ProductDetailPageConfigRpcServiceTestHelper.getConfigTemplateDescription(template);
        String configDesc = ProductDetailPageConfigRpcServiceTestHelper.getPageConfigDescription(config);
        String keysDesc = ProductDetailPageConfigRpcServiceTestHelper.getRoutingKeySetDescription(keys);

        assertNotNull("路由key描述不应该为空", routingKeyDesc);
        assertNotNull("配置模板描述不应该为空", templateDesc);
        assertNotNull("页面配置描述不应该为空", configDesc);
        assertNotNull("路由key集合描述不应该为空", keysDesc);

        assertTrue("路由key描述应该包含关键信息", routingKeyDesc.contains("RoutingKey"));
        assertTrue("配置模板描述应该包含关键信息", templateDesc.contains("ConfigTemplate"));
        assertTrue("页面配置描述应该包含关键信息", configDesc.contains("PageConfig"));
        assertTrue("路由key集合描述应该包含关键信息", keysDesc.contains("RoutingKeySet"));

        // 测试null参数的描述
        String nullRoutingKeyDesc = ProductDetailPageConfigRpcServiceTestHelper.getRoutingKeyDescription(null);
        String nullTemplateDesc = ProductDetailPageConfigRpcServiceTestHelper.getConfigTemplateDescription(null);
        String nullConfigDesc = ProductDetailPageConfigRpcServiceTestHelper.getPageConfigDescription(null);
        String nullKeysDesc = ProductDetailPageConfigRpcServiceTestHelper.getRoutingKeySetDescription(null);

        assertEquals("null路由key描述应该正确", "null routing key", nullRoutingKeyDesc);
        assertEquals("null配置模板描述应该正确", "null config template", nullTemplateDesc);
        assertEquals("null页面配置描述应该正确", "null page config", nullConfigDesc);
        assertEquals("null路由key集合描述应该正确", "null routing key set", nullKeysDesc);
    }
}
