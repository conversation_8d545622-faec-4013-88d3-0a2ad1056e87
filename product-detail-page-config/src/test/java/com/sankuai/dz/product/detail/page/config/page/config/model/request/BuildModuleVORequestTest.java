package com.sankuai.dz.product.detail.page.config.page.config.model.request;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BuildModuleVORequestTest {

    /**
     * 测试当moduleResponseMap为null时返回false
     */
    @Test
    public void testIsModuleKeyResponseValidWhenMapIsNull() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        // act
        boolean result = request.isModuleKeyResponseValid("anyKey");
        // assert
        assertFalse(result);
    }

    /**
     * 测试当moduleResponseMap不为null但key不存在时返回false
     */
    @Test
    public void testIsModuleKeyResponseValidWhenKeyNotExist() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        Map<String, Boolean> map = new HashMap<>();
        request.setModuleResponseMap(map);
        // act
        boolean result = request.isModuleKeyResponseValid("notExistKey");
        // assert
        assertFalse(result);
    }

    /**
     * 测试当moduleResponseMap不为null且key存在但值为null时返回false
     */
    @Test
    public void testIsModuleKeyResponseValidWhenValueIsNull() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        Map<String, Boolean> map = new HashMap<>();
        map.put("testKey", null);
        request.setModuleResponseMap(map);
        // act
        boolean result = request.isModuleKeyResponseValid("testKey");
        // assert
        assertFalse(result);
    }

    /**
     * 测试当moduleResponseMap不为null且key存在值为false时返回false
     */
    @Test
    public void testIsModuleKeyResponseValidWhenValueIsFalse() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        Map<String, Boolean> map = new HashMap<>();
        map.put("testKey", false);
        request.setModuleResponseMap(map);
        // act
        boolean result = request.isModuleKeyResponseValid("testKey");
        // assert
        assertFalse(result);
    }

    /**
     * 测试当moduleResponseMap不为null且key存在值为true时返回true
     */
    @Test
    public void testIsModuleKeyResponseValidWhenValueIsTrue() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        Map<String, Boolean> map = new HashMap<>();
        map.put("testKey", true);
        request.setModuleResponseMap(map);
        // act
        boolean result = request.isModuleKeyResponseValid("testKey");
        // assert
        assertTrue(result);
    }

    /**
     * 测试当moduleKey为null时的边界情况
     */
    @Test
    public void testIsModuleKeyResponseValidWhenKeyIsNull() throws Throwable {
        // arrange
        BuildModuleVORequest request = new BuildModuleVORequest();
        Map<String, Boolean> map = new HashMap<>();
        request.setModuleResponseMap(map);
        // act
        boolean result = request.isModuleKeyResponseValid(null);
        // assert
        assertFalse(result);
    }
}
