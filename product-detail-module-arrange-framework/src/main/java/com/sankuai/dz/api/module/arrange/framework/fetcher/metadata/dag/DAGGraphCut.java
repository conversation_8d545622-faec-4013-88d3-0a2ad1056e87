package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/1/29 23:53
 */
public class DAGGraphCut {

    @Data
    @AllArgsConstructor
    public static class DAGGraphCutResult {
        /**
         * 子节点邻接矩阵，key:当前节点，value:当前节点的所有子节点
         */
        private final Map<String, Set<String>> childrenGraph;
        /**
         * 父节点邻接矩阵，key:当前节点，value:当前节点的所有父节点
         */
        private final Map<String, Set<String>> parentGraph;
        /**
         * 子图所有节点的拓扑排序，用于后续遍历DAG与服务编排
         */
        private final List<String> topologicalSort;
    }

    /**
     * BFS遍历，根据起始点裁剪DAG
     * 返回子图的不同类型的数据结构（父节点邻接矩阵、子节点邻接矩阵、拓扑排序等）
     */
    public static DAGGraphCutResult graphCut(final String start,
                                             final Map<String, Set<String>> entireDAGChildrenGraph,
                                             final Map<String, Set<String>> entireDAGParentGraph) {
        // 验证起始节点是否存在
        if (!entireDAGParentGraph.containsKey(start) || !entireDAGChildrenGraph.containsKey(start)) {
            throw new FetcherDAGException("DAG中没有起始点");
        }

        //初始化入度表
        Map<String, Integer> inDegreeMap = DAGBFSUtils.initInDegreeMap(entireDAGParentGraph);

        //初始化队列（入度为0的节点的队列），用于BFS循环
        //这里筛选了Start，所以生成的graph中只会包含从该Start节点出发路过的节点，如果一个节点包含了两个开始节点则会忽略
        Queue<String> queue = DAGBFSUtils.initQueue(inDegreeMap, start);
        if (queue.isEmpty()) {
            throw new FetcherDAGException(String.format("%s不是开始节点，入度不为0", start));
        }

        //开始BFS循环构建子图
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        Map<String, Set<String>> parentGraph = new HashMap<>();
        //子图拓扑排序
        List<String> topologicalSort = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            //初始化当前节点的父节点集合
            parentGraph.putIfAbsent(node, new HashSet<>());
            //记录拓扑排序
            topologicalSort.add(node);
            //获取所有子节点
            Set<String> children = entireDAGChildrenGraph.getOrDefault(node, new HashSet<>());
            for (String child : children) {
                //初始化当前子节点的父节点合集
                parentGraph.putIfAbsent(child, new HashSet<>());
                //对所有子节点的入度-1，如果为0则加入队列
                inDegreeMap.put(child, inDegreeMap.get(child) - 1);
                if (inDegreeMap.get(child) == 0) {
                    queue.add(child);
                }
                //把当前节点加入子节点的父节点合集
                parentGraph.get(child).add(node);
            }
            //记录当前节点的所有子节点
            childrenGraph.put(node, children);
        }
        //校验子图的完整性
        DAGBFSUtils.checkDAGGraphIntegrity(childrenGraph);
        DAGBFSUtils.checkDAGGraphIntegrity(parentGraph);
        //校验拓扑排序结果与DAG的一致性
        checkNodeCorrectness(childrenGraph, parentGraph, topologicalSort);
        //校验childrenGraph和parentGraph是否可以互相转换，两者必须是等价的
        checkDAGGraphCorrectness(childrenGraph, parentGraph);
        return new DAGGraphCutResult(childrenGraph, parentGraph, topologicalSort);
    }

    private static void checkNodeCorrectness(final Map<String, Set<String>> childrenGraph,
                                             final Map<String, Set<String>> parentGraph,
                                             final List<String> topologicalSort) {
        Set<String> childrenGraphNodes = childrenGraph.keySet();
        Set<String> parentGraphNodes = parentGraph.keySet();
        if (!childrenGraphNodes.equals(parentGraphNodes)) {
            throw new FetcherDAGException("构建出来的子图childrenGraph与parentGraph节点集合不一致");
        }
        if (childrenGraphNodes.size() != topologicalSort.size() || !childrenGraphNodes.containsAll(topologicalSort)) {
            throw new FetcherDAGException("构建出来的子图topologicalSort与DAGGraph节点集合不一致");
        }
    }

    private static void checkDAGGraphCorrectness(final Map<String, Set<String>> childrenGraph,
                                                 final Map<String, Set<String>> parentGraph) {
        Map<String, Set<String>> convertChildrenGraph = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);
        if (!convertChildrenGraph.equals(childrenGraph)) {
            throw new FetcherDAGException("parentGraph无法转换出等价的childrenGraph");
        }
        Map<String, Set<String>> convertParentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        if (!convertParentGraph.equals(parentGraph)) {
            throw new FetcherDAGException("childrenGraph无法转换出等价的parentGraph");
        }
    }

}
