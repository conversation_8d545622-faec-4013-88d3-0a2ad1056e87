package com.sankuai.dz.product.detail.page.config.model.layout.lion.config;

import com.dianping.pigeon.util.CollectionUtils;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.page.config.utils.json.JacksonUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 20:45
 * 足疗团购
 */
public class ProductDetail_SecondCategory_1_303 extends BaseConfigTool{

    @Test
    public void testConfig() {
        //pageConfigTemplateDTO
        PageConfigTemplateDTO pageConfigTemplateDTO = new PageConfigTemplateDTO();
        PageConfigDataDTO mainConfigData = new PageConfigDataDTO();
        pageConfigTemplateDTO.setMainConfigData(mainConfigData);

        //mainConfigData
        //足疗预订基础模块
//        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
//                "头图模块组",
//                buildNormalModuleConfigDTO(
//                        "module_detail_navigation_bar_normal", "DealTrade", 1
//                ),
//                buildNormalModuleConfigDTO(
//                        "module_detail_head_pic_banner", "GuideCommon", 1
//                )
//        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "商品基础信息模块组",
                buildNormalModuleConfigDTO(
                        "module_detail_navigation_bar_normal", "DealTrade", 1
                ),
                buildNormalModuleConfigDTO(
                        "module_detail_head_pic_banner", "GuideCommon", 1
                ),
                buildExclusiveModuleConfigDTO(
                        "exclusiveModule1",
                        buildNormalModuleConfigDTO(
                                "module_detail_deal_atmosphere_price_sale_bar", "DealTrade", 1
                        ),
                        buildNormalModuleConfigDTO(
                                "module_detail_deal_price_sale_bar", "DealTrade", 1
                        )
                ),//价格条 or 氛围条
                buildNormalModuleConfigDTO(
                        "module_price_discount_detail", "DealTrade", 1
                ),//优惠栏和优惠浮层
                buildNormalModuleConfigDTO(
                        "module_detail_deal_multi_sku_select", "DealTrade", 1
                ),//多sku
                buildNormalModuleConfigDTO(
                        "module_detail_discountcard", "DealTrade", 1
                ),//折扣卡
                buildNormalModuleConfigDTO(
                        "module_detail_title", "GuideCommon", 1
                ),//标题
                buildNormalModuleConfigDTO(
                        "module_detail_deal_tags", "GuideCommon", 1
                ),//标签
                buildNormalModuleConfigDTO(
                        "module_detail_highlight", "GuideCommon", 1
                ),//亮点
                buildNormalModuleConfigDTO(
                        "module_detail_limit_module", "GuideCommon", 1
                ),//限制条
                buildNormalModuleConfigDTO(
                        "module_detail_reminder_info_tag", "GuideCommon", 1,
                        buildOverlayModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 1)
                ),//须知条和须知浮层
                buildNormalModuleConfigDTO(
                        "module_detail_guarantee_info_tag", "GuideCommon", 1
                ),//保障条和保障浮层
                buildNormalModuleConfigDTO(
                        "module_detail_deal_shop_tag", "GuideCommon", 1
                ),//门店标签模块
                buildNormalModuleConfigDTO(
                        "module_detail_deal_markup_select_bar", "DealTrade", 1
                )//加价选择条和加价浮层
//                buildNormalModuleConfigDTO(
//                        "module_detail_deal_free_member_card", "DealTrade", 1
//                )//免费商家会员卡
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "免费商家会员卡",
                buildNormalModuleConfigDTO(
                        "module_detail_deal_free_member_card", "DealTrade", 1
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "拼团规则",
                buildNormalModuleConfigDTO(
                        "module_detail_deal_pintuan_rule", "DealTrade", 0
                )// 拼团规则
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "Tab栏",
                buildTabModuleConfigDTO(
                        "module_detail_tab", "GuideCommon", 1,
                        buildTabModuleItemDTO(
                                "deal_detail",
                                "团购详情",
                                buildModuleGroupConfigDTO(
                                        "结构化详情",
                                        buildNormalModuleConfigDTO("module_detail_structured_detail", "GuideCommon", 1)
                                ),
                                buildModuleGroupConfigDTO(
                                        "付费升级",
                                        buildNormalModuleConfigDTO("module_detail_deal_markup_detail", "DealTrade", 0)
                                ),
                                buildModuleGroupConfigDTO(
                                        "服务设施",
                                        buildNormalModuleConfigDTO("module_detail_service_facilities", "GuideCommon", 1)
                                ),
                                buildModuleGroupConfigDTO(
                                        "补充说明",
                                        buildNormalModuleConfigDTO("module_detail_deal_timescard_additional", "GuideCommon", 1)
                                ),// 次卡补充信息
                                buildModuleGroupConfigDTO(
                                        "图文详情",
                                        buildNormalModuleConfigDTO("module_detail_image_text", "GuideCommon", 0)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "purchase_note",
                                "购买须知",
                                buildModuleGroupConfigDTO(
                                        "结构化购买须知",
                                        buildNormalModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 0)
                                ),
                                buildModuleGroupConfigDTO(
                                        "适用门店",
                                        buildNormalModuleConfigDTO("module_detail_deal_available_shop", "GuideCommon", 1)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "product_review",
                                "评价",
                                buildModuleGroupConfigDTO(
                                        "评价",
                                        buildNormalModuleConfigDTO("module_detail_deal_review", "GuideCommon", 0)
                                )
                        )
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "公益商家",
                buildNormalModuleConfigDTO(
                        "module_detail_deal_welfare_info", "DealTrade", 0
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "推荐",
                buildNormalModuleConfigDTO(
                        "module_detail_recommend", "GuideCommon", 0
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "底Bar区域",
                buildNormalModuleConfigDTO(
                "module_detail_deal_bottom_bar", "DealTrade", 1
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "加价浮层",
                buildOverlayModuleConfigDTO("module_detail_deal_markup_layer", "DealTrade", 1)
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "直播浮层",
                buildOverlayModuleConfigDTO("module_detail_deal_float_video_layer", "GuideCommon", 1)
        ));
        System.out.println(JacksonUtils.serialize(pageConfigTemplateDTO));
        Assert.assertTrue( pageConfigTemplateDTO.getMainConfigData() != null && !CollectionUtils.isEmpty(pageConfigTemplateDTO.getMainConfigData().getModuleGroupConfigs()));
    }

}