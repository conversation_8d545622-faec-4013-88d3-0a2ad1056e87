package com.sankuai.dz.product.detail.gateway.domain.spi.call;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.context.InvokerInfo;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPICallFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.SPICallFactory;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.SPIConfigService;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler.SPICallHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 18:21
 */
@Slf4j
@Component
public class SPICallMainFunction {

    @Resource
    private SPIConfigService spiConfigService;

    @Resource
    private SPICallFactory spiCallFactory;

    /**
     * 请求分发
     *
     * @return 每个SPI的返回值
     */
    public void process(final GatewayContext gatewayContext) {
        long startTime = System.currentTimeMillis();
        ProductDetailPageRequest request = gatewayContext.getSpiRequest();
        Map<String, Set<String>> aliasModulesMap = gatewayContext.getInvokerInfoList().stream().collect(Collectors.toMap(InvokerInfo::getInterfaceAlias, InvokerInfo::getModuleKeys));
        final Set<String> originalModuleKeys = request.getModuleKeys();
        //获取每个SPI接口的配置信息
        final Map<String, SPIConfigDTO> aliasSPIConfigDTOMap = new HashMap<>();
        for (String alias : aliasModulesMap.keySet()) {
            if (!aliasSPIConfigDTOMap.containsKey(alias)) {
                SPIConfigDTO spiConfigDTO = spiConfigService.getSPIConfigDTO(alias);
                if (spiConfigDTO == null) {
                    log.error("查不到该alias={}对应的spi配置，为了不影响其他模块调用，所以仅告警", alias, new SPIConfigFatalException(String.format("查不到该alias=%s对应的spi配置", alias)));
                    continue;
                }
                aliasSPIConfigDTOMap.put(alias, spiConfigDTO);
            }
        }
        //并行发起调用
        final Map<String, CompletableFuture<GenericProductDetailPageResponse>> responseFutures = new HashMap();
        long maxTimeout = 0;
        for (SPIConfigDTO spiConfigDTO : aliasSPIConfigDTOMap.values()) {
            Set<String> moduleKeys = aliasModulesMap.get(spiConfigDTO.getInterfaceAlias());
            if (CollectionUtils.isEmpty(moduleKeys)) {
                continue;//理论上不可能到这
            }
            maxTimeout = Math.max(maxTimeout, spiConfigDTO.getTimeout());
            SPICallHandler spiCallHandler = spiCallFactory.getHandler(spiConfigDTO.getInterfaceType());
            request.setModuleKeys(moduleKeys);//复用http入参，替换moduleKeys，后续会还原请求入参
            CompletableFuture<GenericProductDetailPageResponse> future = spiCallHandler.call(spiConfigDTO, request);
            responseFutures.put(spiConfigDTO.getInterfaceAlias(), future);
        }
        //还原http入参
        request.setModuleKeys(originalModuleKeys);
        //get所有结果
        try {
            CompletableFuture.allOf(responseFutures.values().toArray(new CompletableFuture[0])).get(maxTimeout, TimeUnit.MILLISECONDS);
        } catch (Throwable ignored) {
        }
        //获取所有SPI的返回值
        final List<GenericProductDetailPageResponse> responseList = new ArrayList<>();
        for (Map.Entry<String, CompletableFuture<GenericProductDetailPageResponse>> entry : responseFutures.entrySet()) {
            GenericProductDetailPageResponse response;
            try {
                response = entry.getValue().get(maxTimeout, TimeUnit.MILLISECONDS);
                if (response == null) {
                    throw new SPICallFatalException("SPI(alias=" + entry.getKey() + ")调用返回值为null");
                }
            } catch (Throwable throwable) {
                //调用异常不抛出，构建fail返回结果，避免影响其他接口
                log.error("SPICallMainFunction.singleSPI.call,alias={}", entry.getKey(), throwable);
                response = GenericProductDetailPageResponse.fail("SPI调用发生异常,msg=" + throwable.getMessage());
            }
            //详细打点
            doSPICatTransaction(entry.getKey(), response);
            responseList.add(response);
        }
        gatewayContext.setSpiResponseList(responseList);
        //统计TP50、90、99耗时
        long duration = System.currentTimeMillis() - startTime;
        Cat.newCompletedTransactionWithDuration(
                "ProductUnifiedPageService", "SPICallMainFunction", duration
        );
        //记录多维度（分行业、分首屏/非首屏等）的接口分发耗时
        Cat.logMetricForDuration(
                "SPICallMainFunction", duration, gatewayContext.getLogMetricDimension()
        );
    }

    private void doSPICatTransaction(final String interfaceAlias,
                                     final GenericProductDetailPageResponse response) {
        Transaction transaction = Cat.newTransactionWithDuration(
                "SPICallMainFunction", interfaceAlias, response.getDuration()
        );
        if (response.getCode() == PageResponseCodeEnum.SUCCESS.getCode()) {
            transaction.setStatus(Transaction.SUCCESS);
        } else {
            transaction.setStatus(String.format("code:%s;msg:%s", response.getCode(), response.getMsg()));
        }
        transaction.complete();
    }

}
