package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.field;

import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import lombok.Getter;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/5/30 16:00
 */
@Getter
public class BaseTypeFieldDisplayConfigBO extends FieldDisplayConfigBO {

    /**
     * 规则配置
     */
    private final RuleConfigDTO ruleConfig;

    public BaseTypeFieldDisplayConfigBO(final String fieldName,
                                        final boolean collection,
                                        final RuleConfigDTO ruleConfig) {
        super(fieldName, collection);
        this.ruleConfig = ruleConfig;
    }

    @Override
    public boolean isPojo() {
        return false;
    }

}
