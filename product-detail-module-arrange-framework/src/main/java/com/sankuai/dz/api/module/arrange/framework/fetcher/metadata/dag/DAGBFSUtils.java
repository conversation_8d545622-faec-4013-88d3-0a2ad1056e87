package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/1/31 14:22
 */
public class DAGBFSUtils {

    /**
     * 检查DAGGraph是否完整，Map.Keys是否包含了所有Map.Values
     */
    public static void checkDAGGraphIntegrity(final Map<String, Set<String>> dagGraph) {
        for (Map.Entry<String, Set<String>> entry : dagGraph.entrySet()) {
            for (String node : entry.getValue()) {
                if (!dagGraph.containsKey(node)) {
                    throw new FetcherDAGException(String.format(
                            "DAG完整性校验失败!!!节点(%s)异常，也有可能是因为该节点有多个起始节点导致的", node
                    ));
                }
            }
        }
    }

    /**
     * 将子节点邻接矩阵转换为父节点邻接矩阵
     * 输入示例：{A:[B,C], B:[D], C:[D], D:[]}
     * 输出结果：{B:[A], C:[A], D:[B,C], A:[]}
     */
    public static Map<String, Set<String>> convertChildrenGraphToParentGraph(final Map<String, Set<String>> childGraph) {

        Map<String, Set<String>> parentGraph = new HashMap<>();

        // 初始化所有节点的父节点列表
        childGraph.keySet().forEach(node -> parentGraph.put(node, new HashSet<>()));

        // 遍历每个父节点到子节点的关系
        childGraph.forEach((parentNode, children) -> {
            for (String child : children) {
                // 为每个子节点添加父节点引用
                parentGraph.computeIfAbsent(child, k -> new HashSet<>()).add(parentNode);
            }
        });

        return parentGraph;
    }

    /**
     * 将父节点邻接矩阵转换为子节点邻接矩阵
     * 输入示例：{B:[A], C:[A], D:[B,C], A:[]}
     * 输出结果：{A:[B,C], B:[D], C:[D], D:[]}
     */
    public static Map<String, Set<String>> convertParentGraphToChildrenGraph(final Map<String, Set<String>> parentGraph) {

        Map<String, Set<String>> childrenGraph = new HashMap<>();

        // 初始化所有节点的子节点列表
        parentGraph.keySet().forEach(node -> childrenGraph.put(node, new HashSet<>()));

        // 遍历每个子节点的父节点关系
        parentGraph.forEach((childNode, parents) -> {
            for (String parent : parents) {
                // 为每个父节点添加子节点引用
                childrenGraph.computeIfAbsent(parent, k -> new HashSet<>()).add(childNode);
            }
        });

        return childrenGraph;
    }

    /**
     * 用parentGraph初始化入度表
     */
    public static Map<String, Integer> initInDegreeMap(final Map<String, Set<String>> parentGraph) {
        Map<String, Integer> inDegree = new HashMap<>();
        parentGraph.forEach((node, parent) -> inDegree.put(node, parent.size()));
        return inDegree;
    }

    /**
     * 初始化队列，用于BFS循环
     *
     * @param inDegreeMap 初始化的入度表
     * @param start 指定开始节点，如果为null则不指定
     * @return 入度为0的节点的队列
     */
    public static Queue<String> initQueue(final Map<String, Integer> inDegreeMap,
                                          final String start) {
        Queue<String> queue = new LinkedList<>();
        inDegreeMap.forEach((node, degree) -> {
            if (degree == 0 && (start == null || start.equals(node))) {
                queue.add(node);
            }
        });
        return queue;
    }

}
