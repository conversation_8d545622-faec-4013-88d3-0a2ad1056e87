package com.sankuai.dz.product.detail.page.low.code.starter;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/5/28 16:17
 */
@Slf4j
public class LowCodeStarter implements InitializingBean {

    @Setter
    private List<String> basePackages;

    @Override
    public void afterPropertiesSet() throws Exception {
        Transaction transaction = Cat.newTransaction("LowCodeStarter", "init");
        try {
            //初始化组件元数据
            ComponentDefinitionStorage.init(basePackages);

            //拉取所有配置
            DisplayConfigStorage.init();

            //校验配置与元数据的正确性


        } catch (Throwable throwable) {
            transaction.setStatus(throwable);
            Cat.logEvent("LowCodeStarter", "outer-error");
            log.error("LowCodeStarter.outerCatch", throwable);
            throw throwable;
        } finally {
            transaction.complete();
        }
    }

}
