package com.sankuai.dz.product.detail.page.low.code.test.config.bo;

import com.google.common.collect.Lists;
import com.sankuai.athena.digital.arch.annotations.DigitalArchClass;
import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;


import lombok.*;

import java.util.List;
import java.util.Optional;

/**
 * @Author: caisiyuan03
 * @Date: 2025/6/9 20:25
 * @Description: 相同M选N类型的服务项目列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@DigitalArchClass
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Service_Project_MN_List,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "服务项目（M选N）列表",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class ServiceProjectMNListComponentBO extends BizComponentBO {

    @DigitalArchField(desc = "相同M选N类型的服务项目列表")
    private List<MNWithPrefixDotTitleComponentBO> sameServiceProjectMNList;

    @Override
    public BizComponentVO boBuildBizComponentVO() {
        List<BaseViewComponent> result = Lists.newArrayList();
        for (MNWithPrefixDotTitleComponentBO mnWithPrefixDotTitleComponentBO : sameServiceProjectMNList) {
            List<BaseViewComponent> baseViewComponents = Optional.ofNullable(mnWithPrefixDotTitleComponentBO)
                    .map(MNWithPrefixDotTitleComponentBO::boBuildBizComponentVO)
                    .map(BizComponentVO::getDealDetails)
                    .orElse(Lists.newArrayList());
            result.addAll(baseViewComponents);
        }
        return new BizComponentVO(result);
    }
}
