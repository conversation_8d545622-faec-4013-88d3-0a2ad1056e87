package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 16:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "全页面配置替换策略")
public class PageReplaceRule extends AbstractMergeRule {

    @FieldDoc(description = "整个页面的配置", requiredness = Requiredness.REQUIRED)
    private PageConfigDataDTO mainConfigData;

    @Override
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.PAGE_REPLACE;
    }

}
