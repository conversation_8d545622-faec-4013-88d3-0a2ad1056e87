package com.sankuai.dz.api.module.arrange.framework.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Tests for FieldsWithAnnotationResolver.getFields method
 */
@ExtendWith(MockitoExtension.class)
public class FieldsWithAnnotationResolverGetFieldsTest {

    @Retention(RetentionPolicy.RUNTIME)
    public @interface TestAnnotation {
    }

    public interface BaseInterface {
    }

    @TestAnnotation
    public static class AnnotatedClass implements BaseInterface {
    }

    public static class NonAnnotatedClass implements BaseInterface {
    }

    public static class TestContainer {

        private AnnotatedClass annotatedField;

        private NonAnnotatedClass nonAnnotatedField;
    }

    /**
     * Test when the class has fields with the specified annotation and correct parent class
     */
    @Test
    public void testGetFieldsWithCorrectAnnotationAndParent() throws Throwable {
        // arrange & act
        List<Class<? extends BaseInterface>> fields = FieldsWithAnnotationResolver.getFields(TestContainer.class, TestAnnotation.class, BaseInterface.class);
        // assert
        assertEquals(1, fields.size(), "Should find one matching field");
        assertEquals(AnnotatedClass.class, fields.get(0), "Should contain AnnotatedClass");
    }

    /**
     * Test when the class has no fields with the specified annotation
     */
    @Test
    public void testGetFieldsWithNoAnnotationPresent() throws Throwable {
        // arrange
        class EmptyClass {

            private NonAnnotatedClass field;
        }
        // act
        List<Class<? extends BaseInterface>> fields = FieldsWithAnnotationResolver.getFields(EmptyClass.class, TestAnnotation.class, BaseInterface.class);
        // assert
        assertTrue(fields.isEmpty(), "Should find no fields as no fields have annotated types");
    }

    /**
     * Test when the class has fields with the specified annotation but incorrect parent class
     */
    @Test
    public void testGetFieldsWithCorrectAnnotationWrongParent() throws Throwable {
        // arrange
        @TestAnnotation
        class WrongParentClass {
        }
        class TestClass {

            private WrongParentClass field;
        }
        // act
        List<Class<? extends BaseInterface>> fields = FieldsWithAnnotationResolver.getFields(TestClass.class, TestAnnotation.class, BaseInterface.class);
        // assert
        assertTrue(fields.isEmpty(), "Should find no fields as field types don't match parent class");
    }

    /**
     * Test when the input class is null
     */
    @Test
    public void testGetFieldsWithNullClass() throws Throwable {
        // act
        List<Class<? extends BaseInterface>> fields = FieldsWithAnnotationResolver.getFields(null, TestAnnotation.class, BaseInterface.class);
        // assert
        assertTrue(fields.isEmpty(), "Should return empty list for null class");
    }

    /**
     * Test when the annotation class is null
     */
    @Test
    public void testGetFieldsWithNullAnnotationClass() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> FieldsWithAnnotationResolver.getFields(TestContainer.class, null, BaseInterface.class), "Should throw NPE when annotation class is null");
    }

    /**
     * Test with inheritance hierarchy
     */
    @Test
    public void testGetFieldsWithInheritance() throws Throwable {
        // arrange
        class Parent {

            private AnnotatedClass parentField;
        }
        class Child extends Parent {

            private AnnotatedClass childField;
        }
        // act
        List<Class<? extends BaseInterface>> fields = FieldsWithAnnotationResolver.getFields(Child.class, TestAnnotation.class, BaseInterface.class);
        // assert
        assertEquals(2, fields.size(), "Should find fields from both parent and child classes");
        assertTrue(fields.contains(AnnotatedClass.class), "Should contain AnnotatedClass");
    }
}
