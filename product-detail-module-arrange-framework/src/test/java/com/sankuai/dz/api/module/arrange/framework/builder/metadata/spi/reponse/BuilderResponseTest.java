package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.reponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABDetailDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;

@ExtendWith(MockitoExtension.class)
class BuilderResponseTest {

    @Mock
    private AbstractModuleVO mockModuleVO;

    /**
     * 测试当传入非空Throwable时，fail方法应返回success=false且error被正确设置的BuilderResponse
     */
    @Test
    public void testFailWithNonNullError() {
        // arrange
        Throwable mockError = mock(Throwable.class);
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.fail(mockError);
        // assert
        assertFalse(response.isSuccess(), "success应为false");
        assertSame(mockError, response.getError(), "error应等于传入的Throwable");
        assertNull(response.getReturnValue(), "returnValue应为null");
        assertNull(response.getAbDetails(), "abDetails应为null");
    }

    /**
     * 测试当传入null Throwable时，fail方法应返回success=false且error为null的BuilderResponse
     */
    @Test
    public void testFailWithNullError() {
        // arrange
        Throwable nullError = null;
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.fail(nullError);
        // assert
        assertFalse(response.isSuccess(), "success应为false");
        assertNull(response.getError(), "error应为null");
        assertNull(response.getReturnValue(), "returnValue应为null");
        assertNull(response.getAbDetails(), "abDetails应为null");
    }

    /**
     * 测试fail方法每次调用都返回新的实例，且不会共享状态
     */
    @Test
    public void testFailReturnsNewInstanceEachTime() {
        // arrange
        Throwable mockError1 = mock(Throwable.class);
        Throwable mockError2 = mock(Throwable.class);
        // act
        BuilderResponse<AbstractModuleVO> response1 = BuilderResponse.fail(mockError1);
        BuilderResponse<AbstractModuleVO> response2 = BuilderResponse.fail(mockError2);
        // assert
        assertNotSame(response1, response2, "每次调用应返回新实例");
        assertSame(mockError1, response1.getError(), "response1的error应等于第一个mock");
        assertSame(mockError2, response2.getError(), "response2的error应等于第二个mock");
    }

    /**
     * 测试fail方法返回的对象类型正确
     */
    @Test
    public void testFailReturnsCorrectType() {
        // arrange
        Throwable mockError = mock(Throwable.class);
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.fail(mockError);
        // assert
        assertInstanceOf(BuilderResponse.class, response, "返回类型应为BuilderResponse");
    }

    @Test
    void testSucceed_NormalCase() throws Throwable {
        // arrange
        List<ABDetailDTO> abDetails = new ArrayList<>();
        ABDetailDTO abDetail = new ABDetailDTO();
        abDetails.add(abDetail);
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(mockModuleVO, abDetails);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertSame(mockModuleVO, response.getReturnValue(), "Return value should match input");
        assertSame(abDetails, response.getAbDetails(), "AB details should match input");
        assertNull(response.getError(), "Error should be null");
    }

    @Test
    void testSucceed_ReturnValueNull() throws Throwable {
        // arrange
        List<ABDetailDTO> abDetails = new ArrayList<>();
        ABDetailDTO abDetail = new ABDetailDTO();
        abDetails.add(abDetail);
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(null, abDetails);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertNull(response.getReturnValue(), "Return value should be null");
        assertSame(abDetails, response.getAbDetails(), "AB details should match input");
        assertNull(response.getError(), "Error should be null");
    }

    @Test
    void testSucceed_AbDetailsNull() throws Throwable {
        // arrange
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(mockModuleVO, null);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertSame(mockModuleVO, response.getReturnValue(), "Return value should match input");
        assertNull(response.getAbDetails(), "AB details should be null");
        assertNull(response.getError(), "Error should be null");
    }

    @Test
    void testSucceed_AbDetailsEmpty() throws Throwable {
        // arrange
        List<ABDetailDTO> emptyList = Collections.emptyList();
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(mockModuleVO, emptyList);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertSame(mockModuleVO, response.getReturnValue(), "Return value should match input");
        assertSame(emptyList, response.getAbDetails(), "AB details should be empty list");
        assertNull(response.getError(), "Error should be null");
    }

    @Test
    void testSucceed_BothNull() throws Throwable {
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(null, null);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertNull(response.getReturnValue(), "Return value should be null");
        assertNull(response.getAbDetails(), "AB details should be null");
        assertNull(response.getError(), "Error should be null");
    }

    @Test
    public void testSucceedWithMockedReturnValue() throws Throwable {
        // arrange
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(mockModuleVO);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertSame(mockModuleVO, response.getReturnValue(), "Return value should match input");
        assertNull(response.getError(), "Error should be null");
        assertNull(response.getAbDetails(), "AB details should be null");
        // Remove verification since getModuleKey() is not called in the tested method
    }

    @Test
    public void testSucceedWithNullReturnValue() throws Throwable {
        // arrange
        AbstractModuleVO nullVO = null;
        // act
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(nullVO);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful even with null input");
        assertNull(response.getReturnValue(), "Return value should be null");
        assertNull(response.getError(), "Error should be null");
        assertNull(response.getAbDetails(), "AB details should be null");
    }

    @Test
    public void testSucceedResponseImmutability() throws Throwable {
        // arrange
        BuilderResponse<AbstractModuleVO> response = BuilderResponse.succeed(mockModuleVO);
        // assert
        assertTrue(response.isSuccess(), "Response should be successful");
        assertSame(mockModuleVO, response.getReturnValue(), "Return value should match input");
        assertNull(response.getError(), "Error should be null");
        assertNull(response.getAbDetails(), "AB details should be null");
    }
}
