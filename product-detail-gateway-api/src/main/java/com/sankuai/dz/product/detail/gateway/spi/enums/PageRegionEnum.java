package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/1/10 上午10:21
 */
@Getter
public enum PageRegionEnum {

    ALL_SCREEN(0, "全屏"),
    FIRST_SCREEN(1, "首屏"),
    NOT_FIRST_SCREEN(2, "非首屏"),
    SELECTED_MODULE(100, "指定模块"),
    OLD_DETAIL_PAGE_COMPATIBLE(1000, "老详情页兼容");

    private final int code;

    private final String desc;

    PageRegionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PageRegionEnum fromCode(int code) {
        for (PageRegionEnum value : PageRegionEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (PageRegionEnum value : PageRegionEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(PageRegionEnum.values()).collect(Collectors.toMap(
                PageRegionEnum::getCode,
                PageRegionEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(PageRegionEnum.values()).collect(Collectors.toMap(
                PageRegionEnum::name,
                PageRegionEnum::getDesc
        ));
    }

}