package com.sankuai.dz.product.detail.page.config.spi.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import static org.junit.jupiter.api.Assertions.*;

import com.dianping.pigeon.remoting.common.service.GenericService;
import com.sankuai.dz.product.detail.page.config.error.SPIConfigFatalException;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 21:59
 */
@ExtendWith(MockitoExtension.class)
public class PigeonSPICallHandlerTest {

    private static final String json = "{\"code\":501,\"msg\":null,\"abResultList\":[],\"moduleResponse\":{\"module_detail_structured_detail\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_structured_detail\",\"moduleVO\":{\"dealDetails\":[{\"endBackgroundColor\":null,\"order\":null,\"subContent\":null,\"icon\":null,\"backgroundColor\":null,\"detail\":null,\"content\":\"60分钟\",\"title\":\"\",\"type\":1},{\"endBackgroundColor\":null,\"order\":null,\"subContent\":null,\"icon\":null,\"backgroundColor\":null,\"detail\":null,\"content\":\"头部\",\"title\":\"服务部位\",\"type\":2},{\"endBackgroundColor\":null,\"order\":1,\"subContent\":\"足疗\",\"icon\":null,\"backgroundColor\":null,\"detail\":\"60分钟\",\"content\":\"头部\",\"title\":\"服务流程\",\"type\":3}],\"moduleKey\":\"module_detail_structured_detail\"},\"success\":true},\"module_detail_highlight\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_highlight\",\"moduleVO\":{\"jumpUrl\":null,\"style\":\"struct\",\"attrs\":[{\"value\":\"60分钟\",\"name\":\"服务时长\"},{\"value\":\"头部\",\"name\":\"服务部位\"}],\"delimiter\":\"line\",\"moduleKey\":\"module_detail_highlight\"},\"success\":true},\"module_detail_booking_instructions\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_booking_instructions\",\"moduleVO\":null,\"success\":true},\"module_detail_guarantee_info_tag_popup\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_guarantee_info_tag_popup\",\"moduleVO\":{\"title\":null,\"layerConfigs\":[{\"miniJumpUrl\":null,\"textStyle\":null,\"textType\":0,\"type\":1,\"desc\":null,\"jumpUrl\":null,\"title\":\"随时退·过期退\",\"icon\":\"https://p0.meituan.net/ingee/2860c04f209c5ebe48ad7e05a726de711937.png\"}],\"priceProtectionTag\":null,\"moduleKey\":\"module_detail_guarantee_info_tag_popup\"},\"success\":true},\"module_tags\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_tags\",\"moduleVO\":null,\"success\":true},\"module_detail_guarantee_info_tag\":{\"code\":500,\"msg\":\"出错啦\",\"moduleKey\":\"module_detail_guarantee_info_tag\",\"moduleVO\":null,\"success\":false},\"module_detail_discountcard\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_discountcard\",\"moduleVO\":{\"contents\":null,\"jumpUrl\":null,\"backgroundColor\":null,\"suffixIcon\":null,\"prefixIcon\":null,\"moduleKey\":\"module_detail_discountcard\"},\"success\":true},\"module_detail_head_pic_banner\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_head_pic_banner\",\"moduleVO\":{\"headPicModules\":[{\"vrIconUrl\":null,\"vrUrl\":null,\"videoId\":null,\"scale\":\"16:9\",\"desc\":null,\"spritePic\":null,\"type\":1,\"videoUrl\":null,\"content\":\"https://p0.meituan.net/dpmerchantpic/ffd079e16f475723df441201264f4c974542.jpg%40960w_540h_1e_1c_1l%7Cwatermark%3D1%26%26r%3D2%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\"}],\"moduleKey\":\"module_detail_head_pic_banner\"},\"success\":true},\"module_detail_reminder_info_tag\":{\"code\":500,\"msg\":\"出错啦\",\"moduleKey\":\"module_detail_reminder_info_tag\",\"moduleVO\":null,\"success\":false},\"module_detail_navigation_bar_normal\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_navigation_bar_normal\",\"moduleVO\":{\"navbar\":[{\"type\":1,\"popover\":[],\"jumpUrl\":\"\",\"conf\":[{\"type\":1,\"icon\":\"https://p0.meituan.net/dztgdetailimages/fe24c1631bc3e8a6e7dcc8b3dc7ae3e410840.png\",\"text\":\"已收藏\"},{\"type\":2,\"icon\":\"https://p0.meituan.net/dztgdetailimages/ea5d3cc0d93bbcb5e3995f123d8db4b55457.png\",\"text\":\"已收藏\"},{\"type\":3,\"icon\":\"https://p0.meituan.net/dztgdetailimages/44dd4b08b3fbe8b5f1cf331256344bbf9291.png\",\"text\":\"收藏\"},{\"type\":4,\"icon\":\"https://p0.meituan.net/dztgdetailimages/bc91407b2207893eafaaae01aa0ec7843746.png\",\"text\":\"收藏\"}]},{\"type\":2,\"popover\":[],\"jumpUrl\":\"\",\"conf\":[{\"type\":1,\"icon\":\"https://p0.meituan.net/dztgdetailimages/bfc181f5bb78119e243100ba8d8a4ee59652.png\",\"text\":\"分享\"},{\"type\":2,\"icon\":\"https://p0.meituan.net/dztgdetailimages/e7b2043dcf74a78a710e2e23373d0bc33786.png\",\"text\":\"分享\"}]},{\"type\":3,\"popover\":[{\"type\":4,\"popover\":[],\"jumpUrl\":\"imeituan://www.meituan.com/home\",\"conf\":[{\"type\":1,\"icon\":\"https://p0.meituan.net/dztgdetailimages/f0ddf17c0ca209d04f467b45d2b89ef61136.png\",\"text\":\"首页\"}]},{\"type\":6,\"popover\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/poi/detail?id=604616051\",\"conf\":[{\"type\":1,\"icon\":\"https://p0.meituan.net/dztgdetailimages/0fe9c7d60d077802e38cc54fcff3620c3250.png\",\"text\":\"门店\"}]}],\"jumpUrl\":\"\",\"conf\":[{\"type\":1,\"icon\":\"https://p0.meituan.net/dztgdetailimages/3ab96603bbea2d911ae3d72eeff4e7174743.png\",\"text\":\"更多\"},{\"type\":2,\"icon\":\"https://p0.meituan.net/dztgdetailimages/45a417ac48b0c2ee6c921eca05a7583d1767.png\",\"text\":\"更多\"}]}],\"share\":{\"title\":\"足疗预订2\",\"desc\":null,\"url\":\"https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D604616051%26productType%3D2%26productId%3D423627757\",\"miniProgramConfig\":null,\"image\":\"http://p0.meituan.net/dpmerchantpic/ffd079e16f475723df441201264f4c974542.jpg\"},\"statusInfo\":{\"favorStatus\":0},\"moduleKey\":\"module_detail_navigation_bar_normal\"},\"success\":true},\"module_detail_service_facilities\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_service_facilities\",\"moduleVO\":{\"detailServiceFacilities\":[],\"moduleKey\":\"module_detail_service_facilities\"},\"success\":true},\"module_detail_title\":{\"code\":200,\"msg\":null,\"moduleKey\":\"module_detail_title\",\"moduleVO\":{\"title\":\"足疗预订2\",\"moduleKey\":\"module_detail_title\"},\"success\":true}}}";

    @Mock
    private GenericService genericService1;

    @Mock
    private GenericService genericService2;

    public static void main(String[] args) {
        GenericProductDetailPageResponse response = JSONObject.parseObject(json, GenericProductDetailPageResponse.class);
        System.out.println(response);
    }


    private Map<String, GenericService> getSpiMapValue(PigeonSPICallHandler handler) throws Exception {
        Field field = PigeonSPICallHandler.class.getDeclaredField("spiMap");
        field.setAccessible(true);
        return (Map<String, GenericService>) field.get(handler);
    }

    @Test
    public void testRefreshSPIMapWithNonEmptyMap() throws Throwable {
        // arrange
        PigeonSPICallHandler handler = new PigeonSPICallHandler();
        Map<String, GenericService> inputMap = new HashMap<>();
        inputMap.put("service1", genericService1);
        // act
        handler.refreshSPIMap(inputMap);
        // assert
        Map<String, GenericService> actualMap = getSpiMapValue(handler);
        assertSame(inputMap, actualMap);
    }

    @Test
    public void testRefreshSPIMapWithNull() throws Throwable {
        // arrange
        PigeonSPICallHandler handler = new PigeonSPICallHandler();
        // act & assert
        SPIConfigFatalException exception = assertThrows(SPIConfigFatalException.class, () -> handler.refreshSPIMap(null));
        assertEquals("PIGEON类型的SPI服务集合为空!", exception.getMessage());
    }

    @Test
    public void testRefreshSPIMapWithEmptyMap() throws Throwable {
        // arrange
        PigeonSPICallHandler handler = new PigeonSPICallHandler();
        // act & assert
        SPIConfigFatalException exception = assertThrows(SPIConfigFatalException.class, () -> handler.refreshSPIMap(Collections.emptyMap()));
        assertEquals("PIGEON类型的SPI服务集合为空!", exception.getMessage());
    }

    @Test
    public void testRefreshSPIMapWithSingleElementMap() throws Throwable {
        // arrange
        PigeonSPICallHandler handler = new PigeonSPICallHandler();
        Map<String, GenericService> inputMap = Collections.singletonMap("service1", genericService1);
        // act
        handler.refreshSPIMap(inputMap);
        // assert
        Map<String, GenericService> actualMap = getSpiMapValue(handler);
        assertEquals(1, actualMap.size());
        assertSame(genericService1, actualMap.get("service1"));
    }

    @Test
    public void testRefreshSPIMapWithMultipleElementsMap() throws Throwable {
        // arrange
        PigeonSPICallHandler handler = new PigeonSPICallHandler();
        Map<String, GenericService> inputMap = new HashMap<>();
        inputMap.put("service1", genericService1);
        inputMap.put("service2", genericService2);
        // act
        handler.refreshSPIMap(inputMap);
        // assert
        Map<String, GenericService> actualMap = getSpiMapValue(handler);
        assertEquals(2, actualMap.size());
        assertSame(genericService1, actualMap.get("service1"));
        assertSame(genericService2, actualMap.get("service2"));
    }
}
