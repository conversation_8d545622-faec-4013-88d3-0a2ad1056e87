package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/2/10 10:44
 */
@Getter
public enum ClientTypeEnum {

    UNKNOWN(0, "unknown", PlatformEnum.UNKNOWN),
    DP_APP(100, "dpapp", PlatformEnum.DP),
    DP_M(101, "m", PlatformEnum.DP),
    DP_XCX(102, "dpxcx", PlatformEnum.DP),
    DP_WX(103, "dpwx", PlatformEnum.DP),
    DP_PC(104, "dppc", PlatformEnum.DP),
    DP_BAIDU_MAP_XCX(105, "dpbaidumapxcx", PlatformEnum.DP),
    MT_APP(200, "mtapp", PlatformEnum.MT),
    MT_I(201, "i", PlatformEnum.MT),
    MT_XCX(202, "mtxcx", PlatformEnum.MT),
    MT_WX(203, "mtwx", PlatformEnum.MT),
    MT_PC(204, "mtpc", PlatformEnum.MT),
    MT_ZJ_XCX(205, "mtzjxcx", PlatformEnum.MT),
    MT_WWJKZ_XCX(206, "mtwwjkzxcx", PlatformEnum.MT),
    MT_KUAI_SHOU_XCX(207, "mtkuaishouxcx", PlatformEnum.MT),
    MT_MAO_YAN_APP(208, "mtMaoYanApp", PlatformEnum.MT),
    MT_MAO_YAN_XCX(209, "mtMaoYanXCX", PlatformEnum.MT),
    MT_MAP(210, "mtmap", PlatformEnum.MT),
    MT_WAI_MAI_APP(220, "mtwmapp", PlatformEnum.MT),
    KAI_DIAN_BAO(300, "kdb", PlatformEnum.KAI_DIAN_BAO),

    /**
     * 阿波罗
     */
    APOLLO(500, "apollo", PlatformEnum.KAI_DIAN_BAO),
    /**
     * 三方平台
     */
    THIRD_PLATFORM(501, "thirdPlatform", PlatformEnum.UNKNOWN),
    /**
     * 美团美播小程序
     */
    MT_LIVE_XCX(502, "", PlatformEnum.MT),
    /**
     *  美团美播提单小程序
     */
    MT_LIVE_ORDER_XCX(503, "三方", PlatformEnum.MT),
    /**
     * 美团万物小程序
     */
    MT_WAN_WU_XCX(504, "三方", PlatformEnum.MT),

    ;



    private final int code;
    private final String msg;
    private final PlatformEnum platform;

    ClientTypeEnum(final int code,
                   final String msg,
                   final PlatformEnum platform) {
        this.code = code;
        this.msg = msg;
        this.platform = platform;
    }

    public static boolean containsCode(int code) {
        return Arrays.stream(ClientTypeEnum.values()).anyMatch(e -> e.code == code);
    }

    public static ClientTypeEnum fromCode(int code) {
        return Arrays.stream(ClientTypeEnum.values()).filter(e -> e.code == code).findFirst().orElse(ClientTypeEnum.UNKNOWN);
    }

    public boolean isMtClientType() {
        return this.getPlatform() == PlatformEnum.MT;
    }

    public boolean isDpClientType() {
        return this.getPlatform() == PlatformEnum.DP;
    }
    public boolean isInApp() {
        return this == ClientTypeEnum.DP_APP || this == ClientTypeEnum.MT_APP;
    }

    public boolean isInWxXCX() {
        return this == ClientTypeEnum.DP_XCX || this == ClientTypeEnum.MT_XCX;
    }

}