package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.ComponentFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ComponentFetcherContextBuildFutureTest {

    @Mock
    private ComponentFetcherDefinition fetcherDefinition;

    private TestComponentFetcherContext context;

    @BeforeEach
    void setUp() {
        context = spy(new TestComponentFetcherContext(fetcherDefinition));
    }

    @Test
    void testBuildFutureSuccess() throws Throwable {
        // arrange
        FetcherResponse<TestAggregateResult> aggregateResponse = new FetcherResponse<>();
        aggregateResponse.setSuccess(true);
        TestAggregateResult aggregateResult = new TestAggregateResult();
        aggregateResponse.setReturnValue(aggregateResult);
        FetcherResponse<TestResult> mappedResponse = new FetcherResponse<>();
        mappedResponse.setSuccess(true);
        TestResult mappedResult = new TestResult();
        mappedResponse.setReturnValue(mappedResult);
        CompletableFuture<FetcherResponse<TestAggregateResult>> inputFuture = CompletableFuture.completedFuture(aggregateResponse);
        doReturn(mappedResponse).when(context).mapResult(aggregateResponse);
        // act
        context.buildFuture(inputFuture);
        // assert
        assertNotNull(context.getFuture());
        FetcherResponse<TestResult> result = context.getFuture().get();
        assertTrue(result.isSuccess());
        assertEquals(mappedResult, result.getReturnValue());
        verify(context).mapResult(aggregateResponse);
    }

    @Test
    void testBuildFutureWithNullInput() throws Throwable {
        // act
        context.buildFuture(null);
        // assert
        assertNotNull(context.getFuture());
        FetcherResponse<TestResult> result = context.getFuture().get();
        assertFalse(result.isSuccess());
        assertTrue(result.getError() instanceof NullPointerException);
    }

    private static class TestComponentFetcherContext extends ComponentFetcherContext<Object, TestAggregateResult, TestResult> {

        TestComponentFetcherContext(ComponentFetcherDefinition definition) {
            this.fetcherDefinition = definition;
        }

        @Override
        public void fulfillRequest(Object request) {
        }

        @Override
        public FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }

        @Override
        protected FetcherResponse<TestResult> mapResult(FetcherResponse<TestAggregateResult> aggregateResult) {
            return new FetcherResponse<>();
        }
    }

    private static class TestAggregateResult extends FetcherReturnValueDTO {
    }

    private static class TestResult extends FetcherReturnValueDTO {
    }
}
