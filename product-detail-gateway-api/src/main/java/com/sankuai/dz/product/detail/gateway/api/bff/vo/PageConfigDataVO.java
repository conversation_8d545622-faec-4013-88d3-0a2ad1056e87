package com.sankuai.dz.product.detail.gateway.api.bff.vo;

import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/22 21:21
 */
@Data
public class PageConfigDataVO implements Serializable {

    private PageConfigRoutingKey routeKey;

    private List<List<ModuleConfigVO>> data;

}
