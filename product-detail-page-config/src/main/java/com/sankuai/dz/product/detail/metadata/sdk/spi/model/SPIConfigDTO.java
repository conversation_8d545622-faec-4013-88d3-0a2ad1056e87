package com.sankuai.dz.product.detail.metadata.sdk.spi.model;

import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import lombok.Data;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 20:42
 */
@Data
public class SPIConfigDTO {

    /**
     * 服务别名，全局唯一
     */
    private final String interfaceAlias;

    /**
     * 接口类型
     */
    private final InterfaceTypeEnum interfaceType;

    /**
     * 接口url
     */
    private final String interfaceUrl;

    /**
     * 超时时间，单位TimeUnit.MILLISECONDS
     */
    private final int timeout = 5000;

}
