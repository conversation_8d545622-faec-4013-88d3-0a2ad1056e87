package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.AggregateFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/9 10:58
 */
@Getter
@SuppressWarnings("rawtypes")
public class AggregateFetcherDefinition extends FetcherDefinition {

    @Override
    public FetcherTypeEnum getFetcherType() {
        return FetcherTypeEnum.AGGREGATE_FETCHER;
    }

    /**
     * 所有component，静态信息
     */
    private final Set<String> componentFetcherNames;
    /**
     * 聚合了本身的依赖项 与 所有componentFetcher的fillRequestDependencies
     */
    private final Set<Class<? extends BaseFetcherContext>> classOfPreviousLayerDependenciesInDAG;//已转换为只读
    private final Set<String> nameOfPreviousLayerDependenciesInDAG;//已转换为只读

    public AggregateFetcherDefinition(final Class<? extends AggregateFetcherContext> fetcherClass,
                                      final List<ComponentFetcherDefinition> allComponentFetchers) throws Exception {
        super(fetcherClass, new FetcherDefinitionInitParam(fetcherClass, fetcherClass.getAnnotation(AggregateFetcher.class)));
        Set<Class<? extends BaseFetcherContext>> classOfPreviousLayerDependencies = parseClassOfPreviousLayerDependenciesInDAG();
        Set<String> componentFetcherNames = new HashSet<>();
        for (ComponentFetcherDefinition componentFetcherDefinition : allComponentFetchers) {
            if (componentFetcherDefinition.getAggregateFetcherClass() != fetcherClass) {
                continue;
            }
            componentFetcherNames.add(componentFetcherDefinition.getFetcherName());
            classOfPreviousLayerDependencies.addAll(componentFetcherDefinition.getClassOfFillRequestDependencies());
        }
        this.componentFetcherNames = Collections.unmodifiableSet(componentFetcherNames);
        this.classOfPreviousLayerDependenciesInDAG = Collections.unmodifiableSet(classOfPreviousLayerDependencies);
        this.nameOfPreviousLayerDependenciesInDAG = Collections.unmodifiableSet(
                this.classOfPreviousLayerDependenciesInDAG.stream().map(FetcherDefinition::getFetcherName).collect(Collectors.toSet())
        );
        checkPreviousLayerDependenciesInDAG();
    }

    private Set<Class<? extends BaseFetcherContext>> parseClassOfPreviousLayerDependenciesInDAG() {
        final Set<Class<? extends BaseFetcherContext>> dependencies = new HashSet<>();
        //循环解析
        Class<?> currentClass = this.fetcherClass;
        while (currentClass != null) {
            if (!AggregateFetcherContext.class.isAssignableFrom(currentClass)) {
                break;
            }
            AggregateFetcher annotation = currentClass.getAnnotation(AggregateFetcher.class);
            if (annotation != null) {
                dependencies.addAll(Arrays.asList(annotation.previousLayerDependencies()));
            }
            currentClass = currentClass.getSuperclass();
        }
        //转化为不可编辑的集合返回
        return dependencies;
    }

}
