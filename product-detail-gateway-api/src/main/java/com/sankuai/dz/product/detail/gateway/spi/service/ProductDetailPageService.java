package com.sankuai.dz.product.detail.gateway.spi.service;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;

/**
 * @Author: guangyujie
 * @Date: 2025/1/8 19:20
 * 业务方需实现的SPI接口协议
 */
public interface ProductDetailPageService {

    /**
     * 商品详情页模块化SPI
     */
    ProductDetailPageResponse query(ProductDetailPageRequest request);

}
