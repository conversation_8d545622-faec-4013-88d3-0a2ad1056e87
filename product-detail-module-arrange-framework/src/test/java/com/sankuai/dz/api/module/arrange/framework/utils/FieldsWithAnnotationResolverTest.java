package com.sankuai.dz.api.module.arrange.framework.utils;

import org.junit.Test;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/4/29 10:47
 */
public class FieldsWithAnnotationResolverTest {

    @Test
    public void getFields() {
        List<Class<? extends ExampleComponent>> fields = FieldsWithAnnotationResolver.getFields(Child.class, BizComponent.class, ExampleComponent.class);
        for (Class<? extends ExampleComponent> field : fields) {
            System.out.println(field.getName() + " : " + field.getSimpleName());
        }
        // 输出:
        // childComponent : ExampleComponent
        // parentField : ExampleComponent
    }

    // 示例使用的@BizComponent注解定义
    @Retention(RetentionPolicy.RUNTIME)
    @interface BizComponent {
    }

    // 测试示例
    @BizComponent
    static class ExampleComponent {
    }

    static class Parent {
        private ExampleComponent parentField; // 应被收集
    }

    static class Child extends Parent {
        private String childField;
        private ExampleComponent childComponent; // 应被收集
    }

}