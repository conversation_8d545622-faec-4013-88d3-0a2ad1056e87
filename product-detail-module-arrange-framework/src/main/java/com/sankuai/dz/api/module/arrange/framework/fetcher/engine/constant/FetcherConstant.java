package com.sankuai.dz.api.module.arrange.framework.fetcher.engine.constant;

import com.dianping.lion.Environment;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/3 09:40
 */
public class FetcherConstant {

    public static final long PROD_MAX_TIME_OUT = 2000L;

    public static final long MAX_TIME_OUT = Environment.isTestEnv() ? 10000L : PROD_MAX_TIME_OUT;

    public static final String FETCHER_CAT_NAME = "ProductDetailFetcher";

}
