<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dz</groupId>
        <artifactId>product-detail-gateway</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-gateway-starter</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>product-detail-gateway-starter</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-gateway-application</artifactId>
        </dependency>

        <!-- region MDP 脚手架生成代码统计埋点 内容为空，不引入其他依赖，请勿删除 -->
        <!-- MDP LA Initializer -->
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>mdp-boot-initializr-mdp</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <!-- endregion MDP 脚手架生成代码统计埋点 -->

        <!-- Mdp Boot Starter -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter</artifactId>
        </dependency>

        <!-- Mdp Boot Components -->
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>swagger-analysis-core</artifactId>
            <scope>compile</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-doc</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 第三方依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <!-- 4080接口是否露出pigeon服务 -->
                <!--                <configuration>-->
                <!--                    <requiresUnpack>-->
                <!--                        <dependency>-->
                <!--                            <groupId>com.dianping.dpsf</groupId>-->
                <!--                            <artifactId>dpsf-net</artifactId>-->
                <!--                        </dependency>-->
                <!--                    </requiresUnpack>-->
                <!--                </configuration>-->
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>swagger-analysis-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-doc-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-graphql-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
