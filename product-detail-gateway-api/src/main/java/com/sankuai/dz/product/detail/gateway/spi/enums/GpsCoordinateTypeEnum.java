package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/8 17:39
 */
@Getter
public enum GpsCoordinateTypeEnum {

    GCJ02(1, "火星坐标系");

    private final int code;

    private final String desc;

    GpsCoordinateTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GpsCoordinateTypeEnum fromCode(int code) {
        for (GpsCoordinateTypeEnum value : GpsCoordinateTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (GpsCoordinateTypeEnum value : GpsCoordinateTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(GpsCoordinateTypeEnum.values()).collect(Collectors.toMap(
                GpsCoordinateTypeEnum::getCode,
                GpsCoordinateTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(GpsCoordinateTypeEnum.values()).collect(Collectors.toMap(
                GpsCoordinateTypeEnum::name,
                GpsCoordinateTypeEnum::getDesc
        ));
    }

}