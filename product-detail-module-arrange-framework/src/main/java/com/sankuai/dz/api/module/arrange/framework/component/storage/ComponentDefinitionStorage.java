package com.sankuai.dz.api.module.arrange.framework.component.storage;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.component.annotation.BizComponent;
import com.sankuai.dz.api.module.arrange.framework.component.definition.ComponentDefinition;
import com.sankuai.dz.api.module.arrange.framework.component.exception.ComponentFatalException;
import com.sankuai.dz.api.module.arrange.framework.component.spi.BaseComponent;
import com.sankuai.dz.api.module.arrange.framework.utils.Storage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/4/16 15:34
 */
@SuppressWarnings("unchecked")
@Slf4j
public class ComponentDefinitionStorage {

    private static ApplicationContext applicationContext;

    @SuppressWarnings("unchecked")
    public static <T extends BaseComponent> T getComponentBean(final ComponentDefinition componentDefinition) {
        try {
            return (T) applicationContext.getBean(componentDefinition.getComponetClass());
        } catch (Throwable throwable) {
            //BuilderComponent可以抛出异常打断Builder的创建
            log.error("ComponentDefinitionStorage.getComponentBean", throwable);
            throw new ComponentFatalException("运行时Fatal Error!!! getComponentBean 失败!!!", throwable);
        }
    }

    public static final Storage<Class<? extends BaseComponent>, ComponentDefinition> componentStorageOfModuleClass = new Storage<>(
            new HashMap<>(), "BuilderComponentClass", "BuilderComponentDefinition"
    );

    public static synchronized void initComponentDefinitionStorage(final ApplicationContext applicationContext) throws Exception {
        ComponentDefinitionStorage.applicationContext = applicationContext;
        String[] beanNamesForAnnotation = applicationContext.getBeanNamesForAnnotation(BizComponent.class);
        for (String componentBeanName : beanNamesForAnnotation) {
            final Class<?> componentClass = applicationContext.getType(componentBeanName);
            if (componentClass == null) {
                throw new BuilderFatalError("无法找到该Component的beanName对应的Class:" + componentBeanName);
            }
            if (!BaseComponent.class.isAssignableFrom(componentClass)) {
                throw new BuilderFatalError(String.format(
                        "Component(%s)没有继承AbstractComponent", componentClass.getName()
                ));
            }
            if (!applicationContext.isPrototype(componentBeanName)) {
                throw new BuilderFatalError(String.format(
                        "Component(%s)不是多例的(prototype)", componentClass.getName()
                ));
            }
            ComponentDefinition componentDefinition = new ComponentDefinition((Class<? extends BaseComponent>) componentClass);
            saveComponentDefinition(componentDefinition);
        }
    }

    private static void saveComponentDefinition(ComponentDefinition componentDefinition) {
        if (componentStorageOfModuleClass.containsKey(componentDefinition.getComponetClass())) {
            throw new IllegalArgumentException(String.format(
                    "出现重复定义的Component(class=%s)", componentDefinition.getComponetClass().getName()
            ));
        }
        componentStorageOfModuleClass.put(componentDefinition.getComponetClass(), componentDefinition);
    }

}
