package com.sankuai.dz.product.detail.page.low.code.entity.component.metadata;

import com.sankuai.athena.digital.arch.enums.DAFieldClassTypeEnum;
import com.sankuai.athena.digital.arch.enums.DAFieldTypeEnum;
import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.DAGenericTypeBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAFieldInfoBO;
import com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeMetadataFatalException;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/6/6 15:53
 */
public class ComponentDefinitionValidator {

    /**
     * 组件定义的字段只允许是
     * ①基本类型，基本类型定义参考DAFieldClassTypeEnum
     * ②POJO，且POJO嵌套层数不能大于5层，否则会影响性能
     * ③List，List的泛型只能是①和②，不允许List嵌套
     *
     * @see DAFieldClassTypeEnum
     */
    public static void checkClassField(final DAClassInfoBO daClassInfoBO,
                                        final int depth) {
        if (depth >= ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new IllegalArgumentException(String.format("类型定义嵌套层数过深，最多%s层", ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH));
        }
        for (DAFieldInfoBO field : daClassInfoBO.getFieldInfoMap().values()) {
            checkType(field.getGenericType(), depth);
        }
    }

    private static void checkType(final DAGenericTypeBO genericType,
                                  final int depth) {
        DAFieldTypeEnum rawTypeEnum = genericType.getRawTypeEnum();
        if (rawTypeEnum == DAFieldTypeEnum.BASE_TYPE) {
            return;
        } else if (rawTypeEnum == DAFieldTypeEnum.COLLECTION) {
            if (genericType.getParameterizedTypeList().size() > 1) {
                throw new LowCodeMetadataFatalException("集合的泛型数量>1，不合法");
            }
            checkCollectionType(genericType.getParameterizedTypeList().get(0), depth);
        } else if (rawTypeEnum == DAFieldTypeEnum.POJO) {
            DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(genericType.getRawType().getTClass());
            if (subClassInfo == null) {
                throw new LowCodeMetadataFatalException("找不到POJO字段的元数据:" + genericType.getRawType().getTClass().getName());
            }
            checkClassField(subClassInfo, depth + 1);
        } else {
            throw new LowCodeMetadataFatalException("组件定义只允许出现①基本类型、②集合、③POJO、④枚举!!!");
        }
    }

    /**
     * List的泛型只能是①和②，不允许List嵌套
     */
    private static void checkCollectionType(final DAGenericTypeBO genericType,
                                            final int depth) {
        DAFieldTypeEnum rawTypeEnum = genericType.getRawTypeEnum();
        if (rawTypeEnum == DAFieldTypeEnum.BASE_TYPE) {
            return;
        } else if (rawTypeEnum == DAFieldTypeEnum.POJO) {
            DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(genericType.getRawType().getTClass());
            if (subClassInfo == null) {
                throw new LowCodeMetadataFatalException("找不到POJO字段的元数据:" + genericType.getRawType().getTClass().getName());
            }
            checkClassField(subClassInfo, depth + 1);
        } else {
            throw new LowCodeMetadataFatalException("集合的泛型只允许是①基本类型、②集合、④枚举!!!");
        }
    }

}
