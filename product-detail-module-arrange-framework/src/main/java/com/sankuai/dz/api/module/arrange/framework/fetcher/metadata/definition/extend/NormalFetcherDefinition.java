package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/14 11:39
 */
@Getter
@SuppressWarnings("rawtypes")
public class NormalFetcherDefinition extends FetcherDefinition {

    @Override
    public FetcherTypeEnum getFetcherType() {
        return FetcherTypeEnum.NORMAL_FETCHER;
    }

    /**
     * 依赖的fetcher类型集合
     * 注解中的数据，仅有上一层的依赖fetcher，没有全路径的依赖fetcher
     * 不能用作唯一标识，唯一标识只有fetcherName！！！
     * 只有isStartFetcher=true，dependencies才能为空
     */
    private final Set<Class<? extends BaseFetcherContext>> classOfPreviousLayerDependenciesInDAG;//已转换为只读

    /**
     * 依赖的fetcher名称集合
     * 注解中的数据，仅有上一层的依赖fetcher，没有全路径的依赖fetcher
     */
    private final Set<String> nameOfPreviousLayerDependenciesInDAG;//已转换为只读

    public NormalFetcherDefinition(final Class<? extends NormalFetcherContext> fetcherClass) throws Exception {
        super(fetcherClass, new FetcherDefinitionInitParam(fetcherClass, fetcherClass.getAnnotation(Fetcher.class)));
        this.classOfPreviousLayerDependenciesInDAG = parseClassOfPreviousLayerDependenciesInDAG();
        this.nameOfPreviousLayerDependenciesInDAG = Collections.unmodifiableSet(
                this.classOfPreviousLayerDependenciesInDAG.stream().map(FetcherDefinition::getFetcherName).collect(Collectors.toSet())
        );
        checkPreviousLayerDependenciesInDAG();
    }

    private Set<Class<? extends BaseFetcherContext>> parseClassOfPreviousLayerDependenciesInDAG() {
        final Set<Class<? extends BaseFetcherContext>> dependencies = new HashSet<>();
        //循环解析
        Class<?> currentClass = this.fetcherClass;
        while (currentClass != null) {
            if (!NormalFetcherContext.class.isAssignableFrom(currentClass)) {
                break;
            }
            Fetcher annotation = currentClass.getAnnotation(Fetcher.class);
            if (annotation != null) {
                dependencies.addAll(Arrays.asList(annotation.previousLayerDependencies()));
            }
            currentClass = currentClass.getSuperclass();
        }
        //转化为不可编辑的集合返回
        return Collections.unmodifiableSet(dependencies);
    }

}
