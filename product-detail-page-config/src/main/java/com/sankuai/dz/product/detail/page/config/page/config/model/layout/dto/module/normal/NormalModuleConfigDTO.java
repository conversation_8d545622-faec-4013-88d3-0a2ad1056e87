package com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.normal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.page.config.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.normal.NormalModuleConfigVO;
import com.sankuai.dz.product.detail.page.config.page.config.model.request.BuildModuleVORequest;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.module.overlay.OverlayModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.overlay.OverlayModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 11:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "普通模块")
public class NormalModuleConfigDTO extends BizModuleConfigDTO {

    /**
     * 模块涉及的浮层模块，默认为空
     */
    private List<OverlayModuleConfigDTO> overlayModules;

    @Override
    @JsonIgnore
    public ModuleTypeEnum getModuleType() {
        return ModuleTypeEnum.normal;
    }

    @Override
    @JsonIgnore
    public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
        List<ModuleConfigDTO> allModules = Lists.newArrayList(this);
        if (CollectionUtils.isNotEmpty(overlayModules)) {
            allModules.addAll(overlayModules);
        }
        return allModules;
    }

    @Override
    @JsonIgnore
    public ModuleConfigVO buildVO(final BuildModuleVORequest request, final int depth) {
        NormalModuleConfigVO vo = new NormalModuleConfigVO();
        vo.setFirstScreenFlag(this.getFirstScreenFlag());
        vo.setModuleKey(this.getModuleKey());
        vo.setModuleType(this.getModuleType().name());
        if (CollectionUtils.isNotEmpty(this.getOverlayModules())) {
            vo.setOverlayModules(
                    this.getOverlayModules().stream()
                            .map(overlay -> (OverlayModuleVO) overlay.buildVO(request, depth + 1))
                            .collect(Collectors.toList())
            );
        }
        return vo;
    }

}
