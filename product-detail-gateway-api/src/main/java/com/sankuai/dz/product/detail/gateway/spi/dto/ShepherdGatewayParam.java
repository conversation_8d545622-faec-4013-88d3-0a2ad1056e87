package com.sankuai.dz.product.detail.gateway.spi.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.enums.MpAppIdEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/1/8 20:13
 */
@Data
@TypeDoc(description = "网关层面解析出来的参数")
public class ShepherdGatewayParam implements Serializable {

    @FieldDoc(description = "点评用户实ID", requiredness = Requiredness.REQUIRED)
    private long dpUserId;

    @FieldDoc(description = "美团用户实ID", requiredness = Requiredness.REQUIRED)
    private long mtUserId;

    @FieldDoc(description = "点评用户虚ID", requiredness = Requiredness.REQUIRED)
    private long dpVirtualUserId;

    @FieldDoc(description = "美团用户虚ID", requiredness = Requiredness.REQUIRED)
    private long mtVirtualUserId;

    @FieldDoc(description = "设备ID, 点评侧为dpid 美团侧为uuid", requiredness = Requiredness.REQUIRED)
    private String deviceId;

    @FieldDoc(description = "对应请求头中的pragma-unionid字段", requiredness = Requiredness.REQUIRED)
    private String unionid;

    @FieldDoc(description = "客户端类型(ios | android | 空字符串)", requiredness = Requiredness.REQUIRED)
    private String mobileOSType;

    @FieldDoc(description = "App 版本号，小程序版本请使用csecversionname", requiredness = Requiredness.REQUIRED)
    private String appVersion;

    @FieldDoc(description = "对应请求头中的MTSI-flag字段，只有过了反爬才会有该字段，该字段是反爬技术手动添加", requiredness = Requiredness.REQUIRED)
    private String mtsiflag;

    @FieldDoc(description = "小程序openId", requiredness = Requiredness.OPTIONAL)
    private String openId;

    @FieldDoc(description = "小程序appId", requiredness = Requiredness.OPTIONAL, type = MpAppIdEnum.class)
    private String mpAppId;

    @FieldDoc(description = "小程序版本号", requiredness = Requiredness.OPTIONAL)
    private String csecversionname;

    @FieldDoc(description = "userAgent", requiredness = Requiredness.OPTIONAL)
    private String userAgent;

    @FieldDoc(description = "用户IP地址", requiredness = Requiredness.OPTIONAL)
    private String userIp;

    @FieldDoc(description = "接口URI", requiredness = Requiredness.OPTIONAL)
    private String requestURI;

    @FieldDoc(description = "前端MRN版本号", requiredness = Requiredness.OPTIONAL)
    private String mrnVersion;

}
