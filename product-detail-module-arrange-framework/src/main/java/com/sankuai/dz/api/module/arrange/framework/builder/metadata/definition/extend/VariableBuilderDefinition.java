package com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;

/**
 * @Author: guangyujie
 * @Date: 2025/3/9 17:22
 */
public class VariableBuilderDefinition extends BuilderDefinition {

    @Override
    public BuilderTypeEnum getBuilderType() {
        return BuilderTypeEnum.VARIABLE_BUILDER;
    }

    @SuppressWarnings("rawtypes")
    public VariableBuilderDefinition(Class<? extends BaseVariableBuilder> builderClass) {
        super(builderClass);
    }

}
