package com.sankuai.dz.api.module.arrange.framework.lowcode;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * LowCodeContextBuilder 完整测试套件
 * 包含所有相关的测试类，确保覆盖率大于60%
 * 
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/1/16 11:30
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    LowCodeContextBuilderTest.class,
    LowCodeContextBuilderEdgeCaseTest.class
})
public class LowCodeContextBuilderTestSuite {
    // 测试套件类，无需实现内容


}
