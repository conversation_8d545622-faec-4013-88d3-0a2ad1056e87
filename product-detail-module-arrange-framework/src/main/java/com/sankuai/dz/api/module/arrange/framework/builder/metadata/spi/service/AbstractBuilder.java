package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.api.module.arrange.framework.builder.engine.constant.BuilderConstant;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.reponse.BuilderResponse;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.component.exception.ComponentFatalException;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContext;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContextAware;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.LowCodeModuleVO;
import com.sankuai.dz.product.detail.page.low.code.runtime.LowCodeRuntimeProcessService;
import com.sankuai.dz.product.detail.page.low.code.runtime.request.LowCodeProcessRequest;
import com.sankuai.dz.product.detail.page.low.code.runtime.response.LowCodeProcessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:53
 */
@Slf4j
public abstract class AbstractBuilder<T extends AbstractModuleVO, Definition extends BuilderDefinition>
        extends RunnerContextAware {

    /**
     * 当前Builder定义
     */
    protected Definition builderDefinition;

    /**
     * 模块配置 for LowCode
     * 如果为null，则改模块不支持配置化LowCode搭建
     */
    private ModuleDisplayConfigBO moduleDisplayConfig;

    /**
     * 是否支持LowCode配置化构建模块
     * 实现类是否支持低代码框架 && moduleDisplayConfig不为null
     */
    private boolean isSupportedForLowCode;

    /**
     * 实现类是否支持低代码框架，静态信息
     */
    protected boolean isBuilderSupportedForLowCode() {
        return false;
    }

    @Override
    @SuppressWarnings("unchecked")
    protected void doInit(final RunnerContext runnerContext) {
        this.builderDefinition = (Definition) BuilderDefinitionStorage.builderStorageOfModuleClass.get(this.getClass());
        if (builderDefinition == null) {
            throw new ComponentFatalException(String.format("获取不到builderDefinition,class=%s", this.getClass().getName()));
        }
        this.moduleDisplayConfig = runnerContext.getLowCodeContext().getModuleDisplayConfig(this.builderDefinition.getModuleKey());
        this.isSupportedForLowCode = isBuilderSupportedForLowCode() && this.moduleDisplayConfig != null;
    }

    @Override
    protected boolean containsDependencyFetcher(String dependencyFetcherName) {
        if (isSupportedForLowCode) {
            return this.moduleDisplayConfig.getAllDataSource().containsKey(dependencyFetcherName);
        } else {
            return this.builderDefinition.getDependentFetcherNames().contains(dependencyFetcherName);
        }
    }

    /**
     * 执行Builder具体业务逻辑，Builder是多例的，所以参数已经初始化好
     * 定义为final，不允许重写
     */
    public final BuilderResponse<? extends AbstractModuleVO> build() {
        Transaction transaction = Cat.newTransaction(BuilderConstant.BUILDER_CAT_NAME, this.builderDefinition.getModuleKey());
        try {
            AbstractModuleVO result;
            if (this.isSupportedForLowCode) {
                result = doLowCodeBuild();
            } else {
                result = doBuild();
            }
            transaction.setStatus(Transaction.SUCCESS);
            return BuilderResponse.succeed(result);
        } catch (Throwable throwable) {
            transaction.setStatus(throwable);
            log.error("Builder(moduleKey = {})发生异常,request:{}", this.builderDefinition.getModuleKey(), JSON.toJSONString(this.request), throwable);
            return BuilderResponse.fail(throwable);
        } finally {
            transaction.complete();
        }
    }

    private LowCodeModuleVO doLowCodeBuild() {
        LowCodeProcessRequest lowCodeProcessRequestest = new LowCodeProcessRequest();
        lowCodeProcessRequestest.setComponentConfigs(this.moduleDisplayConfig.getComponentConfigList());
        lowCodeProcessRequestest.setData(getDependencyResultForLowCode());
        LowCodeProcessResponse response = LowCodeRuntimeProcessService.process(lowCodeProcessRequestest);
        return new LowCodeModuleVO(
                this.builderDefinition.getModuleKey(),
                response.getValidComponents().stream()
                        .map(componentBO -> componentBO.buildVO(0))
                        .collect(Collectors.toList())
        );
    }

    /**
     * 获取依赖Fetcher结果，根据LowCode数据源配置自动装配
     */
    protected Map<String, Object> getDependencyResultForLowCode() {
        Set<String> allDependentFetcherNames = this.moduleDisplayConfig.getAllDataSource().keySet();
        if (CollectionUtils.isEmpty(allDependentFetcherNames)) {
            return new HashMap<>();
        }
        Map<String, Object> data = new HashMap<>();
        for (String fetcherName : allDependentFetcherNames) {
            data.put(fetcherName, getDependencyResult(fetcherName));
        }
        return data;
    }

    /**
     * 执行Builder逻辑
     */
    public abstract T doBuild();

}
