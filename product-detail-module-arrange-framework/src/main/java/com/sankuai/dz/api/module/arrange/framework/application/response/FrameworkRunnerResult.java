package com.sankuai.dz.api.module.arrange.framework.application.response;

import com.sankuai.dz.api.module.arrange.framework.builder.engine.BuilderEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.reponse.BuilderResponse;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.response.ModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/10 13:21
 */
@Data
public class FrameworkRunnerResult {

    /**
     * 最终返回值
     */
    private final ProductDetailPageResponse productDetailPageResponse;

    /**
     * moduleKey匹配到的Builder
     */
    private final List<BuilderDefinition> builderDefinitions;

    /**
     * 根据builder匹配到的fetcher
     */
    private final Set<String> targetFetcherNames;

    /**
     * FetcherEngine执行结果
     */
    private final FetcherEngine fetcherEngine;

    /**
     * BuilderEngine执行结果
     */
    private final BuilderEngine builderEngine;

    public FrameworkRunnerResult(final List<BuilderDefinition> builderDefinitions,
                                 final Set<String> targetFetcherNames,
                                 final FetcherEngine fetcherEngine,
                                 final BuilderEngine builderEngine) {
        this.builderDefinitions = builderDefinitions;
        this.targetFetcherNames = targetFetcherNames;
        this.fetcherEngine = fetcherEngine;
        this.builderEngine = builderEngine;
        this.productDetailPageResponse = buildPageResponse();
    }

    private ProductDetailPageResponse buildPageResponse() {
        Map<String, BuilderResponse<? extends AbstractModuleVO>> builderResponseMap = this.builderEngine.getBuilderResponseMap();
        List<ABResultDTO> abResultList = new ArrayList<>();
        List<ModuleResponse> moduleResponse = new ArrayList<>();
        for (Map.Entry<String, BuilderResponse<? extends AbstractModuleVO>> entry : builderResponseMap.entrySet()) {
            String moduleKey = entry.getKey();
            BuilderResponse<? extends AbstractModuleVO> builderResponse = entry.getValue();
            if (CollectionUtils.isNotEmpty(builderResponse.getAbDetails())) {
                abResultList.add(new ABResultDTO(moduleKey, builderResponse.getAbDetails()));
            }
            ModuleResponse response;
            if (builderResponse.isSuccess()) {
                response = ModuleResponse.succeed(moduleKey, builderResponse.getReturnValue());
            } else {
                response = ModuleResponse.fail(moduleKey, "出错啦");
            }
            moduleResponse.add(response);
        }
        return ProductDetailPageResponse.buildResponse(moduleResponse, abResultList);
    }

}
