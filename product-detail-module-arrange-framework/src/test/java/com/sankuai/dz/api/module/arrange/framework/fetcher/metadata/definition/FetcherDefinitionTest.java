package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/2/3 16:32
 */
public class FetcherDefinitionTest {

    @Test
    public void test() {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A", "E"));
            put("A", Sets.newHashSet("B", "C", "F"));
            put("B", Sets.newHashSet("D"));
            put("C", Sets.newHashSet("D"));
            put("D", Sets.newHashSet("F", "Z"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("Z"));
            put("Z", Sets.newHashSet());
        }};
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        String start = parentGraph.entrySet().stream().filter(entry -> entry.getValue().isEmpty()).map(Map.Entry::getKey).findAny().orElse(null);
        if (start == null) {
            throw new IllegalArgumentException("没有生成出开始节点");
        }
        DAG dag = new DAG(
                DAG.DAGBuilder.builder()
                        .start(start)
                        .entireDAGChildrenGraph(childrenGraph)
                        .entireDAGParentGraph(parentGraph)
                        .needCheckCycle(true)
                        .needCheckGraphIntegrity(true)
                        .build()
        );

        System.out.println(JSON.toJSONString(dag.getChildrenGraph()));
        System.out.println(JSON.toJSONString(dag.getParentGraph()));
        System.out.println(JSON.toJSONString(dag.getTopologicalSort()));
        Assert.assertNotNull(childrenGraph);
    }

    @Test
    public void test2() {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A", "B", "C", "D", "E", "F", "G", "H", "I"));
            put("A", Sets.newHashSet("1", "2", "3"));
            put("B", Sets.newHashSet("4"));
            put("C", Sets.newHashSet("5"));
            put("D", Sets.newHashSet("10", "11"));
            put("E", Sets.newHashSet("13"));
            put("F", Sets.newHashSet("20"));
            put("G", Sets.newHashSet());
            put("H", Sets.newHashSet());
            put("I", Sets.newHashSet());
            put("1", Sets.newHashSet());
            put("2", Sets.newHashSet());
            put("3", Sets.newHashSet());
            put("4", Sets.newHashSet());
            put("5", Sets.newHashSet());
            put("10", Sets.newHashSet());
            put("11", Sets.newHashSet());
            put("13", Sets.newHashSet());
            put("20", Sets.newHashSet());
        }};
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        String start = parentGraph.entrySet().stream().filter(entry -> entry.getValue().isEmpty()).map(Map.Entry::getKey).findAny().orElse(null);
        if (start == null) {
            throw new IllegalArgumentException("没有生成出开始节点");
        }
        DAG dag = new DAG(
                DAG.DAGBuilder.builder()
                        .start(start)
                        .entireDAGChildrenGraph(childrenGraph)
                        .entireDAGParentGraph(parentGraph)
                        .needCheckCycle(true)
                        .needCheckGraphIntegrity(true)
                        .build()
        );
        System.out.println(JSON.toJSONString(dag.getChildrenGraph()));
        System.out.println(JSON.toJSONString(dag.getParentGraph()));
        System.out.println(JSON.toJSONString(dag.getTopologicalSort()));
        Assert.assertNotNull(dag.getChildrenGraph());
    }

//    @Test
//    public void randomTest() {
//        Map<String, Set<String>> childrenGraph = generateRandomDAG(50, 0.6);
//        System.out.println(JSON.toJSONString(childrenGraph));
//        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
//        String start = parentGraph.entrySet().stream().filter(entry -> entry.getValue().isEmpty()).map(Map.Entry::getKey).findAny().orElse(null);
//        if (start == null) {
//            throw new IllegalArgumentException("没有生成出开始节点");
//        }
//        DAG dag = new DAG(
//                DAG.DAGBuilder.builder()
//                        .start(start)
//                        .entireDAGChildrenGraph(childrenGraph)
//                        .entireDAGParentGraph(parentGraph)
//                        .needCheckCycle(true)
//                        .needCheckGraphIntegrity(true)
//                        .build()
//        );
//        System.out.println(JSON.toJSONString(dag.getChildrenGraph()));
//        System.out.println(JSON.toJSONString(dag.getParentGraph()));
//        System.out.println(JSON.toJSONString(dag.getTopologicalSort()));
//    }

    public static Map<String, Set<String>> generateRandomDAG(int nodeCount, double edgeProbability) {
        if (nodeCount <= 0 || edgeProbability < 0 || edgeProbability > 1) {
            throw new IllegalArgumentException("Invalid input parameters");
        }

        Map<String, Set<String>> dag = new HashMap<>();
        List<String> nodes = new ArrayList<>();

        // 创建节点
        for (int i = 0; i < nodeCount; i++) {
            String nodeName = String.valueOf(i);
            nodes.add(nodeName);
            dag.put(nodeName, new HashSet<>());
        }

        // 随机添加边
        Random random = new Random();
        for (int i = 0; i < nodeCount; i++) {
            for (int j = i + 1; j < nodeCount; j++) {
                if (random.nextDouble() < edgeProbability) {
                    dag.get(nodes.get(i)).add(nodes.get(j));
                }
            }
        }

        return dag;
    }

}