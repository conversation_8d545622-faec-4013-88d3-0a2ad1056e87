package com.sankuai.dz.api.module.arrange.framework.builder.engine;

import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.reponse.BuilderResponse;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.dz.api.module.arrange.framework.application.constant.FrameworkRunnerConstant.FRAMEWORK_RUNNER_CAT_NAME;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/2/3 21:30
 */
@Getter
@SuppressWarnings("rawtypes")
public class BuilderEngine {

    /**
     * 需要返回的模块的moduleKeys
     */
    private final List<BuilderDefinition> builderDefinitions;

    /**
     * module返回值
     */
    private final Map<String, BuilderResponse<? extends AbstractModuleVO>> builderResponseMap = new HashMap<>();

    public BuilderEngine(final List<BuilderDefinition> builderDefinitions) {
        this.builderDefinitions = builderDefinitions;
    }

    @SuppressWarnings("unchecked")
    public void run() {
        long startTime = System.currentTimeMillis();
        for (BuilderDefinition builderDefinition : this.builderDefinitions) {
            AbstractBuilder builderBean = BuilderDefinitionStorage.getBuilderBean(builderDefinition);
            if (builderBean == null) {
                continue;
            }
            builderResponseMap.put(builderDefinition.getModuleKey(), builderBean.build());
        }
        Cat.newCompletedTransactionWithDuration(FRAMEWORK_RUNNER_CAT_NAME, "builder", System.currentTimeMillis() - startTime);
    }

}
