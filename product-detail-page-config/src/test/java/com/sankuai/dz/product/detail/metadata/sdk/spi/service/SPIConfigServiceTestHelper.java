package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * SPIConfigService 测试辅助工具类
 * 提供测试数据创建和验证方法
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 22:30
 */
public class SPIConfigServiceTestHelper {

    /**
     * 创建有效的SPI配置Map
     */
    public static Map<String, SPIConfigDTO> createValidSPIConfigMap() {
        Map<String, SPIConfigDTO> configMap = new HashMap<>();
        
        SPIConfigDTO config1 = new SPIConfigDTO("product-detail-service", InterfaceTypeEnum.PIGEON, "com.sankuai.product.detail.ProductDetailService");
        SPIConfigDTO config2 = new SPIConfigDTO("recommend-service", InterfaceTypeEnum.PIGEON, "com.sankuai.recommend.RecommendService");
        SPIConfigDTO config3 = new SPIConfigDTO("review-service", InterfaceTypeEnum.PIGEON, "com.sankuai.review.ReviewService");
        
        configMap.put("product-detail-service", config1);
        configMap.put("recommend-service", config2);
        configMap.put("review-service", config3);
        
        return configMap;
    }

    /**
     * 创建单个SPI配置的Map
     */
    public static Map<String, SPIConfigDTO> createSingleSPIConfigMap() {
        Map<String, SPIConfigDTO> configMap = new HashMap<>();
        SPIConfigDTO config = new SPIConfigDTO("single-service", InterfaceTypeEnum.PIGEON, "com.sankuai.single.SingleService");
        configMap.put("single-service", config);
        return configMap;
    }

    /**
     * 创建空的SPI配置Map
     */
    public static Map<String, SPIConfigDTO> createEmptySPIConfigMap() {
        return new HashMap<>();
    }

    /**
     * 创建包含特殊字符的SPI配置Map
     */
    public static Map<String, SPIConfigDTO> createSpecialCharacterSPIConfigMap() {
        Map<String, SPIConfigDTO> configMap = new HashMap<>();
        
        SPIConfigDTO config1 = new SPIConfigDTO("service-特殊字符_123", InterfaceTypeEnum.PIGEON, "com.sankuai.特殊.SpecialService");
        SPIConfigDTO config2 = new SPIConfigDTO("service-emoji😀", InterfaceTypeEnum.PIGEON, "com.sankuai.emoji.EmojiService");
        SPIConfigDTO config3 = new SPIConfigDTO("service-with-hyphen-and_underscore", InterfaceTypeEnum.PIGEON, "com.sankuai.hyphen.HyphenService");
        
        configMap.put("service-特殊字符_123", config1);
        configMap.put("service-emoji😀", config2);
        configMap.put("service-with-hyphen-and_underscore", config3);
        
        return configMap;
    }

    /**
     * 创建大量SPI配置的Map
     */
    public static Map<String, SPIConfigDTO> createLargeSPIConfigMap(int count) {
        Map<String, SPIConfigDTO> configMap = new HashMap<>();
        
        for (int i = 1; i <= count; i++) {
            String alias = "service-" + i;
            String url = "com.sankuai.service" + i + ".Service" + i;
            SPIConfigDTO config = new SPIConfigDTO(alias, InterfaceTypeEnum.PIGEON, url);
            configMap.put(alias, config);
        }
        
        return configMap;
    }

    /**
     * 创建包含不同接口类型的SPI配置Map（虽然目前只有PIGEON）
     */
    public static Map<String, SPIConfigDTO> createMultipleInterfaceTypeSPIConfigMap() {
        Map<String, SPIConfigDTO> configMap = new HashMap<>();
        
        // 目前只有PIGEON类型，但可以为将来扩展做准备
        SPIConfigDTO pigeonConfig1 = new SPIConfigDTO("pigeon-service-1", InterfaceTypeEnum.PIGEON, "com.sankuai.pigeon1.PigeonService1");
        SPIConfigDTO pigeonConfig2 = new SPIConfigDTO("pigeon-service-2", InterfaceTypeEnum.PIGEON, "com.sankuai.pigeon2.PigeonService2");
        
        configMap.put("pigeon-service-1", pigeonConfig1);
        configMap.put("pigeon-service-2", pigeonConfig2);
        
        return configMap;
    }

    /**
     * 将SPI配置Map转换为JSON字符串
     */
    public static String toJsonString(Map<String, SPIConfigDTO> configMap) {
        return JSONObject.toJSONString(configMap);
    }

    /**
     * 创建有效的SPI配置JSON字符串
     */
    public static String createValidSPIConfigJson() {
        return toJsonString(createValidSPIConfigMap());
    }

    /**
     * 创建空的SPI配置JSON字符串
     */
    public static String createEmptySPIConfigJson() {
        return toJsonString(createEmptySPIConfigMap());
    }

    /**
     * 创建单个SPI配置的JSON字符串
     */
    public static String createSingleSPIConfigJson() {
        return toJsonString(createSingleSPIConfigMap());
    }

    /**
     * 创建特殊字符SPI配置的JSON字符串
     */
    public static String createSpecialCharacterSPIConfigJson() {
        return toJsonString(createSpecialCharacterSPIConfigMap());
    }

    /**
     * 创建大量SPI配置的JSON字符串
     */
    public static String createLargeSPIConfigJson(int count) {
        return toJsonString(createLargeSPIConfigMap(count));
    }

    /**
     * 创建无效的JSON字符串
     */
    public static String createInvalidJson() {
        return "{ invalid json format without closing brace";
    }

    /**
     * 创建null JSON字符串
     */
    public static String createNullJson() {
        return null;
    }

    /**
     * 创建空字符串JSON
     */
    public static String createEmptyStringJson() {
        return "";
    }

    /**
     * 验证SPI配置DTO的有效性
     */
    public static boolean isValidSPIConfigDTO(SPIConfigDTO config) {
        return config != null 
                && config.getInterfaceAlias() != null 
                && !config.getInterfaceAlias().trim().isEmpty()
                && config.getInterfaceType() != null
                && config.getInterfaceUrl() != null
                && !config.getInterfaceUrl().trim().isEmpty()
                && config.getTimeout() > 0;
    }

    /**
     * 验证SPI配置Map的有效性
     */
    public static boolean isValidSPIConfigMap(Map<String, SPIConfigDTO> configMap) {
        if (configMap == null || configMap.isEmpty()) {
            return false;
        }
        
        for (Map.Entry<String, SPIConfigDTO> entry : configMap.entrySet()) {
            String key = entry.getKey();
            SPIConfigDTO config = entry.getValue();
            
            // 验证key与config的alias一致
            if (!key.equals(config.getInterfaceAlias())) {
                return false;
            }
            
            // 验证config本身的有效性
            if (!isValidSPIConfigDTO(config)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取SPI配置DTO的描述信息
     */
    public static String getSPIConfigDescription(SPIConfigDTO config) {
        if (config == null) {
            return "null SPI config";
        }
        return String.format("SPIConfig[alias=%s, type=%s, url=%s, timeout=%d]",
                config.getInterfaceAlias(),
                config.getInterfaceType(),
                config.getInterfaceUrl(),
                config.getTimeout());
    }

    /**
     * 获取SPI配置Map的描述信息
     */
    public static String getSPIConfigMapDescription(Map<String, SPIConfigDTO> configMap) {
        if (configMap == null) {
            return "null SPI config map";
        }
        return String.format("SPIConfigMap[size=%d, aliases=%s]", 
                configMap.size(), 
                configMap.keySet().toString());
    }

    /**
     * 比较两个SPI配置DTO是否相等
     */
    public static boolean areSPIConfigsEqual(SPIConfigDTO config1, SPIConfigDTO config2) {
        if (config1 == null && config2 == null) {
            return true;
        }
        if (config1 == null || config2 == null) {
            return false;
        }
        return config1.getInterfaceAlias().equals(config2.getInterfaceAlias())
                && config1.getInterfaceType() == config2.getInterfaceType()
                && config1.getInterfaceUrl().equals(config2.getInterfaceUrl())
                && config1.getTimeout() == config2.getTimeout();
    }

    /**
     * 创建SPI配置DTO的深拷贝
     */
    public static SPIConfigDTO copySPIConfig(SPIConfigDTO original) {
        if (original == null) {
            return null;
        }
        return new SPIConfigDTO(
                original.getInterfaceAlias(),
                original.getInterfaceType(),
                original.getInterfaceUrl()
        );
    }

    /**
     * 创建SPI配置Map的深拷贝
     */
    public static Map<String, SPIConfigDTO> copySPIConfigMap(Map<String, SPIConfigDTO> original) {
        if (original == null) {
            return null;
        }
        Map<String, SPIConfigDTO> copy = new HashMap<>();
        for (Map.Entry<String, SPIConfigDTO> entry : original.entrySet()) {
            copy.put(entry.getKey(), copySPIConfig(entry.getValue()));
        }
        return copy;
    }

    /**
     * 验证JSON字符串是否为有效的SPI配置格式
     */
    public static boolean isValidSPIConfigJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        
        try {
            Map<String, SPIConfigDTO> configMap = JSONObject.parseObject(json, 
                    new com.alibaba.fastjson.TypeReference<Map<String, SPIConfigDTO>>() {});
            return isValidSPIConfigMap(configMap);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从JSON字符串解析SPI配置Map
     */
    public static Map<String, SPIConfigDTO> parseSPIConfigFromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            return JSONObject.parseObject(json, 
                    new com.alibaba.fastjson.TypeReference<Map<String, SPIConfigDTO>>() {});
        } catch (Exception e) {
            throw new RuntimeException("解析SPI配置JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建测试用的Lion配置key
     */
    public static String getSPIConfigLionKey() {
        return "com.sankuai.dzshoppingguide.detail.gateway.spi.config";
    }

    /**
     * 创建测试用的应用名
     */
    public static String getTestAppName() {
        return "test-app";
    }

    /**
     * 验证接口别名的格式
     */
    public static boolean isValidInterfaceAlias(String alias) {
        return alias != null 
                && !alias.trim().isEmpty() 
                && alias.length() <= 100  // 假设最大长度限制
                && alias.matches("^[a-zA-Z0-9_\\-\u4e00-\u9fa5😀-😿]+$"); // 支持字母、数字、下划线、连字符、中文、emoji
    }

    /**
     * 验证接口URL的格式
     */
    public static boolean isValidInterfaceUrl(String url) {
        return url != null 
                && !url.trim().isEmpty()
                && url.contains(".") // 简单的包名检查
                && url.length() <= 200; // 假设最大长度限制
    }
}
