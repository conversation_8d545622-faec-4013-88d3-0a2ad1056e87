package com.sankuai.dz.api.module.arrange.framework.management.utils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: g<PERSON><PERSON>jie
 * @Date: 2025/2/25 14:02
 */
public class DagToDotConverter {

    public static String generateDot(Map<String, Set<String>> adjacencyMatrix) {
        Set<String> edges = new LinkedHashSet<>();
        Map<String, Set<String>> groupedEdges = new HashMap<>();

        // 第一遍：处理普通边和聚合边
        for (Map.Entry<String, Set<String>> entry : adjacencyMatrix.entrySet()) {
            String source = entry.getKey();
            Set<String> targets = entry.getValue();

            // 处理source节点是否属于聚合组
            for (String target : targets) {
                groupedEdges.computeIfAbsent(source, k -> new HashSet<>()).add(target);
            }
        }

        // 第二遍：生成聚合节点的所有边
        groupedEdges.forEach((group, children) ->
                children.forEach(child ->
                        edges.add(formatEdge(group, child))
                )
        );

        // 构建DOT格式
        return "digraph G {\n" +
                edges.stream()
                        .sorted()
                        .map(edge -> "    " + edge)
                        .collect(Collectors.joining("\n")) +
                "\n}";
    }

    private static String formatEdge(String from, String to) {
        return "\"" + from + "\" -> \"" + to + "\"";
    }

}