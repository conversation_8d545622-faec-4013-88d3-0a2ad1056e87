package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/1/30 00:47
 */
public class DAGAncestorFinder {

    /**
     * 找到每个节点的所有祖先节点集合
     * @return 每个节点的所有祖先节点集合, key:当前节点,value:所有祖先节点
     */
    public static Map<String, Set<String>> findAllAncestors(final List<String> topologicalOrder,
                                                            final Map<String, Set<String>> parentGraph) {
        Map<String, Set<String>> ancestors = new LinkedHashMap<>();

        for (String node : topologicalOrder) {
            Set<String> nodeAncestors = new HashSet<>();

            // 合并所有父节点的祖先集合
            for (String parent : parentGraph.get(node)) {
                nodeAncestors.add(parent);
                nodeAncestors.addAll(ancestors.get(parent));
            }

            ancestors.put(node, nodeAncestors);
        }
        return ancestors;
    }

}
