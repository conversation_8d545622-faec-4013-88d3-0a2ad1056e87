package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 14:47
 */
@Slf4j
@SuppressWarnings("rawtypes")
public abstract class BaseFetcherContext<RESULT extends FetcherReturnValueDTO, DEFINITION extends FetcherDefinition> {

    /**
     * 公共参数，理论上只读
     * tips：代码上为了性能考虑设置为非只读，非全局变量，随意修改只会影响单次请求结果
     */
    protected ProductDetailPageRequest request;

    /**
     * 记录所有Fetcher的执行过程数据
     */
    private Map<String, BaseFetcherContext> fetcherContextMap;

    /**
     * 当前节点信息
     * FetcherDefinition内部所有属性均为只读，没有被篡改的风险
     */
    @Getter
    protected DEFINITION fetcherDefinition;

    /**
     * 整个有向无环图元数据
     * DAG内部所有属性均为只读，没有被篡改的风险
     */
    @Getter
    protected DAG dag;

    /**
     * Fetcher执行开始时间，用于统计耗时
     */
    @Getter
    private long startTime;

    /**
     * 数据源取数规则配置，每个Fetcher自由使用即可，这里只是框架透传
     */
    private DataSourceConfigBO dataSourceConfig;

    /**
     * dataSourceConfig可能为空，所以使用方必须获取Optional以此来提醒和规避NPE问题
     */
    protected Optional<DataSourceConfigBO> getDataSourceConfig() {
        return Optional.ofNullable(dataSourceConfig);
    }

    /**
     * 获取依赖fetcher的结果，注意需要显示定义在注解当中才能获取，不然会报错
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    protected <Result extends FetcherReturnValueDTO> FetcherResponse<Result> getDependencyResponse(
            final Class<? extends BaseFetcherContext> dependencyFetcherClass) {
        String currentFetcherName = this.fetcherDefinition.getFetcherName();
        String dependencyFetcherName = FetcherDefinition.getFetcherName(dependencyFetcherClass);
        //这里需要判断全路径上是否有依赖，需要dag的数据
        //区别于FetcherDefinition中的classOfDependencies和nameOfDependencies，它们仅代表上一层的依赖
        if (this.dag.isAncestor(currentFetcherName, dependencyFetcherName)) {
            BaseFetcherContext baseFetcherContext = this.fetcherContextMap.get(dependencyFetcherName);
            if (baseFetcherContext == null) {
                throw new FetcherFatalException("该fetcher的依赖fetcher未执行且不在FetcherContext中");
            }
            return baseFetcherContext.getFetchResponse();
        } else {
            throw new FetcherFatalException(String.format(
                    "该Fetcher(%s)没有依赖(%s)，但是需要获取依赖的返回值", currentFetcherName, dependencyFetcherName
            ));
        }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    protected <Result extends FetcherReturnValueDTO> Optional<Result> getDependencyResult(
            final Class<? extends BaseFetcherContext> dependencyFetcherClass,
            Class<Result> tClass) {
        FetcherResponse<FetcherReturnValueDTO> fetchResponse = getDependencyResponse(dependencyFetcherClass);
        if (!fetchResponse.isSuccess()) {
            return Optional.empty();
        } else {
            return Optional.ofNullable((Result) fetchResponse.getReturnValue());
        }
    }

    /**
     * 获取依赖fetcher的结果，注意需要显示定义在注解当中才能获取，不然会报错
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    protected <Result extends FetcherReturnValueDTO> Result getDependencyResult(
            final Class<? extends BaseFetcherContext> dependencyFetcherClass) {
        FetcherResponse<FetcherReturnValueDTO> fetchResponse = getDependencyResponse(dependencyFetcherClass);
        if (!fetchResponse.isSuccess()) {
            return null;
        } else {
            return (Result) fetchResponse.getReturnValue();
        }
    }

    /**
     * 初始化Context，一个fetcher可能初始化多个fetcher
     */
    public FetcherContextInitResult initContext(final DEFINITION fetcherDefinition,
                                                final FetcherEngine fetcherEngine) {
        this.startTime = System.currentTimeMillis();
        if (this.fetcherDefinition != null) {
            throw new IllegalStateException("DAG遍历中Fetcher不能重复初始化");
        }
        this.request = fetcherEngine.getRequest();
        this.fetcherContextMap = fetcherEngine.getFetcherContextMap();
        this.fetcherDefinition = fetcherDefinition;
        this.dag = fetcherEngine.getDag();
        this.dataSourceConfig = fetcherEngine.getLowCodeContext().getDataSourceConfig(this.fetcherDefinition.getFetcherName());
        try {
            //正常初始化
            return doInitContext(fetcherEngine);
        } catch (Throwable throwable) {
            try {
                //异常处理
                log.error("{}.doInitContext fail,request:{}", fetcherDefinition.getFetcherName(), JSON.toJSONString(request), throwable);
                return doInitContextExceptionally(fetcherEngine, throwable);
            } catch (Throwable throwableFromErrorHandler) {
                //异常处理失败兜底
                log.error("{}.doInitContextExceptionally fail,request:{}", fetcherDefinition.getFetcherName(), JSON.toJSONString(request), throwableFromErrorHandler);
                return new FetcherContextInitResult(this);
            }
        }
    }

    /**
     * 正常执行初始化
     */
    protected abstract FetcherContextInitResult doInitContext(final FetcherEngine fetcherEngine) throws Exception;

    /**
     * 初始化异常兜底，为了不打断整个流程，一定要对初始化异常的情况做Fetcher维度的降级，不能抛出异常
     */
    protected abstract FetcherContextInitResult doInitContextExceptionally(final FetcherEngine fetcherEngine,
                                                                           final Throwable throwable);

    /**
     * 获取编排执行结果
     * 有response缓存，避免重复调用doGetFetchResponse方法
     * 一个fetcher只有一个结果
     */
    private FetcherResponse<RESULT> fetchResponseCache;

    public FetcherResponse<RESULT> getFetchResponse() {
        if (this.fetchResponseCache == null) {
            this.fetchResponseCache = futureGet();
        }
        return this.fetchResponseCache;
    }

    @Getter
    protected CompletableFuture<FetcherResponse<RESULT>> future;

    private FetcherResponse<RESULT> futureGet() {
        FetcherResponse<RESULT> response;
        try {
            response = this.future.get(this.fetcherDefinition.getTimeout(), TimeUnit.MILLISECONDS);
            if (response == null) {
                response = FetcherResponse.fail(new FetcherFatalException(String.format(
                        "Fatal Error!!!fetcher(%s)response是null，理论上不可能到这里，除非重写了NormalBaseFetcher中的doFetch方法。",
                        this.fetcherDefinition.getFetcherName()
                )));
            }
        } catch (Throwable futureGetThrowable) {
            //框架内不做任何操作和日志记录，应用方可以自行处理
            response = FetcherResponse.fail(futureGetThrowable);
        }
        return response;
    }

    public void checkFutureAndSetDefaultFuture() {
        if (this.future == null) {
            this.future = CompletableFuture.completedFuture(FetcherResponse.fail(new FetcherFatalException("Fetcher's future is null")));
        }
    }

}
