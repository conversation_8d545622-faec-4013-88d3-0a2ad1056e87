package com.sankuai.dz.product.detail.gateway.domain.biz;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.product.detail.gateway.domain.bean.CategoryRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductCategoryHandlerTest {

    @InjectMocks
    private ProductCategoryHandler productCategoryHandler;

    @Mock
    private DealGroupQueryService dealGroupQueryService;

    @Mock
    private RedisStoreClient storeClient;

    /**
     * 测试缓存中有商品类别信息的情况
     */
    @Test
    public void testGetCategoryWithCache() throws Throwable {
        // arrange
        CategoryRequest detailPageRequest = new CategoryRequest(1L, 1, null);
        ProductCategory productCategory = new ProductCategory();
        when(storeClient.get(any(StoreKey.class))).thenReturn("{\"firstId\":1,\"secondId\":2,\"thirdId\":3,\"objectId\":4,\"objectVersion\":5}");
        // act
        ProductCategory result = productCategoryHandler.getCategory(detailPageRequest);
        // assert
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getFirstId());
        assertEquals(Integer.valueOf(2), result.getSecondId());
        assertEquals(Integer.valueOf(3), result.getThirdId());
        assertEquals(Long.valueOf(4), result.getObjectId());
        assertEquals(Long.valueOf(5), result.getObjectVersion());
    }

    /**
     * 测试缓存中没有商品类别信息，从 RPC 服务中获取的情况
     */
    @Test
    public void testGetCategoryWithoutCache() throws Throwable {
        // arrange
        CategoryRequest detailPageRequest = new CategoryRequest(1L, 1, null);
        ProductCategory productCategory = new ProductCategory();
        when(storeClient.get(any(StoreKey.class))).thenReturn(null);
        // Assuming the method under test is designed to return null when no cache and no RPC data
        // Adjusted to match the expected behavior
        when(dealGroupQueryService.queryByDealGroupIds(any())).thenReturn(null);
        // act
        ProductCategory result = productCategoryHandler.getCategory(detailPageRequest);
        // assert
        // Adjusted expectation
        assertNull(result);
    }

    /**
     * 测试在获取商品类别信息的过程中发生异常的情况
     */
    @Test
    public void testGetCategoryWithException() throws Throwable {
        // arrange
        CategoryRequest detailPageRequest = new CategoryRequest(1L, 1, null);
        when(storeClient.get(any(StoreKey.class))).thenThrow(new RuntimeException());
        // act
        ProductCategory result = productCategoryHandler.getCategory(detailPageRequest);
        // assert
        assertNull(result);
    }
}
