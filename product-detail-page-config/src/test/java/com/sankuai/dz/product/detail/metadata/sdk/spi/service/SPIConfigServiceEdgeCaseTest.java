package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler.SPICallHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SPIConfigService 边界情况和异常场景测试
 * 专门测试各种边界条件和异常情况，提高代码覆盖率
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 23:00
 */
@RunWith(MockitoJUnitRunner.class)
public class SPIConfigServiceEdgeCaseTest {

    @InjectMocks
    private SPIConfigService spiConfigService;

    @Mock
    private SPICallFactory spiCallFactory;

    @Mock
    private SPICallHandler mockHandler;

    @Before
    public void setUp() {
        // 重置所有 mock 对象
        reset(spiCallFactory, mockHandler);
    }

    /**
     * 测试使用测试辅助工具类创建的各种配置
     */
    @Test
    public void testWithHelperCreatedConfigs() {
        // 模拟handler正常行为
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        // 测试有效配置
        String validJson = SPIConfigServiceTestHelper.createValidSPIConfigJson();
        assertTrue("有效配置JSON应该通过验证", 
                SPIConfigServiceTestHelper.isValidSPIConfigJson(validJson));
        
        invokeInitSPIConfig(validJson);
        
        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("product-detail-service");
        SPIConfigDTO result2 = spiConfigService.getSPIConfigDTO("recommend-service");
        SPIConfigDTO result3 = spiConfigService.getSPIConfigDTO("review-service");
        
        assertNotNull("产品详情服务配置应该存在", result1);
        assertNotNull("推荐服务配置应该存在", result2);
        assertNotNull("评论服务配置应该存在", result3);
        
        assertTrue("产品详情服务配置应该有效", 
                SPIConfigServiceTestHelper.isValidSPIConfigDTO(result1));
        assertTrue("推荐服务配置应该有效", 
                SPIConfigServiceTestHelper.isValidSPIConfigDTO(result2));
        assertTrue("评论服务配置应该有效", 
                SPIConfigServiceTestHelper.isValidSPIConfigDTO(result3));

        // 重置mock
        reset(spiCallFactory, mockHandler);
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        // 测试单个配置
        String singleJson = SPIConfigServiceTestHelper.createSingleSPIConfigJson();
        invokeInitSPIConfig(singleJson);
        
        SPIConfigDTO singleResult = spiConfigService.getSPIConfigDTO("single-service");
        assertNotNull("单个服务配置应该存在", singleResult);
        assertTrue("单个服务配置应该有效", 
                SPIConfigServiceTestHelper.isValidSPIConfigDTO(singleResult));
    }

    /**
     * 测试特殊字符配置
     */
    @Test
    public void testWithSpecialCharacterConfigs() {
        // 模拟handler正常行为
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        String specialJson = SPIConfigServiceTestHelper.createSpecialCharacterSPIConfigJson();
        invokeInitSPIConfig(specialJson);

        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("service-特殊字符_123");
        SPIConfigDTO result2 = spiConfigService.getSPIConfigDTO("service-emoji😀");
        SPIConfigDTO result3 = spiConfigService.getSPIConfigDTO("service-with-hyphen-and_underscore");

        assertNotNull("特殊字符服务配置应该存在", result1);
        assertNotNull("emoji服务配置应该存在", result2);
        assertNotNull("连字符下划线服务配置应该存在", result3);

        assertEquals("特殊字符URL应该正确", "com.sankuai.特殊.SpecialService", result1.getInterfaceUrl());
        assertEquals("emoji URL应该正确", "com.sankuai.emoji.EmojiService", result2.getInterfaceUrl());
        assertEquals("连字符下划线URL应该正确", "com.sankuai.hyphen.HyphenService", result3.getInterfaceUrl());

        // 验证别名格式
        assertTrue("特殊字符别名应该有效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias("service-特殊字符_123"));
        assertTrue("emoji别名应该有效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias("service-emoji😀"));
        assertTrue("连字符下划线别名应该有效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias("service-with-hyphen-and_underscore"));
    }

    /**
     * 测试大量配置的处理
     */
    @Test
    public void testWithLargeConfigSet() {
        // 模拟handler正常行为
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        int configCount = 500;
        String largeJson = SPIConfigServiceTestHelper.createLargeSPIConfigJson(configCount);
        invokeInitSPIConfig(largeJson);

        // 验证部分配置
        SPIConfigDTO result1 = spiConfigService.getSPIConfigDTO("service-1");
        SPIConfigDTO result250 = spiConfigService.getSPIConfigDTO("service-250");
        SPIConfigDTO result500 = spiConfigService.getSPIConfigDTO("service-500");

        assertNotNull("配置1应该存在", result1);
        assertNotNull("配置250应该存在", result250);
        assertNotNull("配置500应该存在", result500);

        assertEquals("配置1的URL应该正确", "com.sankuai.service1.Service1", result1.getInterfaceUrl());
        assertEquals("配置250的URL应该正确", "com.sankuai.service250.Service250", result250.getInterfaceUrl());
        assertEquals("配置500的URL应该正确", "com.sankuai.service500.Service500", result500.getInterfaceUrl());

        // 验证handler被调用了正确的次数
        verify(mockHandler, times(configCount)).getSpiService(any(SPIConfigDTO.class));
        verify(mockHandler, times(InterfaceTypeEnum.values().length)).refreshSPIMap(any(Map.class));
    }

    /**
     * 测试无效JSON格式的各种情况
     */
    @Test
    public void testWithInvalidJsonFormats() {
        // 测试无效JSON
        try {
            invokeInitSPIConfig(SPIConfigServiceTestHelper.createInvalidJson());
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            // 期望的异常
        }

        // 测试空字符串JSON
        try {
            invokeInitSPIConfig(SPIConfigServiceTestHelper.createEmptyStringJson());
            fail("应该抛出异常");
        } catch (Exception e) {
            // 期望的异常
        }

        // 测试null JSON
        try {
            invokeInitSPIConfig(SPIConfigServiceTestHelper.createNullJson());
            fail("应该抛出异常");
        } catch (Exception e) {
            // 期望的异常
        }
    }

    /**
     * 测试空配置的处理
     */
    @Test(expected = SPIConfigFatalException.class)
    public void testWithEmptyConfig() {
        // 模拟handler正常行为
        when(spiCallFactory.getHandler(any(InterfaceTypeEnum.class))).thenReturn(mockHandler);
        when(mockHandler.getSpiService(any(SPIConfigDTO.class))).thenReturn(new Object());
        doNothing().when(mockHandler).refreshSPIMap(any(Map.class));

        String emptyJson = SPIConfigServiceTestHelper.createEmptySPIConfigJson();
        assertFalse("空配置JSON应该无效", 
                SPIConfigServiceTestHelper.isValidSPIConfigJson(emptyJson));
        
        // 执行测试，期望抛出异常
        invokeInitSPIConfig(emptyJson);
    }

    /**
     * 测试afterPropertiesSet方法的异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testAfterPropertiesSetWithLionException() throws Exception {
        try (MockedStatic<Environment> environmentMock = mockStatic(Environment.class);
             MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {

            // 模拟 Environment.getAppName()
            environmentMock.when(Environment::getAppName).thenReturn("test-app");

            // 模拟 Lion.getString() 抛出异常
            lionMock.when(() -> Lion.getString(anyString(), anyString()))
                    .thenThrow(new RuntimeException("Lion配置获取失败"));

            // 执行测试，期望抛出异常
            spiConfigService.afterPropertiesSet();
        }
    }

    /**
     * 测试afterPropertiesSet方法的Environment异常
     */
    @Test(expected = RuntimeException.class)
    public void testAfterPropertiesSetWithEnvironmentException() throws Exception {
        try (MockedStatic<Environment> environmentMock = mockStatic(Environment.class)) {

            // 模拟 Environment.getAppName() 抛出异常
            environmentMock.when(Environment::getAppName)
                    .thenThrow(new RuntimeException("获取应用名失败"));

            // 执行测试，期望抛出异常
            spiConfigService.afterPropertiesSet();
        }
    }

    /**
     * 测试配置比较和复制功能
     */
    @Test
    public void testConfigComparisonAndCopy() {
        Map<String, SPIConfigDTO> originalMap = SPIConfigServiceTestHelper.createValidSPIConfigMap();
        Map<String, SPIConfigDTO> copiedMap = SPIConfigServiceTestHelper.copySPIConfigMap(originalMap);

        assertNotNull("复制的配置Map不应该为空", copiedMap);
        assertEquals("复制的配置Map大小应该相同", originalMap.size(), copiedMap.size());

        // 验证每个配置的复制
        for (String alias : originalMap.keySet()) {
            SPIConfigDTO original = originalMap.get(alias);
            SPIConfigDTO copied = copiedMap.get(alias);

            assertNotNull("复制的配置应该存在", copied);
            assertTrue("复制的配置应该相等", 
                    SPIConfigServiceTestHelper.areSPIConfigsEqual(original, copied));
            assertNotSame("复制的配置应该是不同的对象", original, copied);
        }

        // 测试null配置的复制
        SPIConfigDTO nullCopy = SPIConfigServiceTestHelper.copySPIConfig(null);
        assertNull("null配置的复制应该为null", nullCopy);

        Map<String, SPIConfigDTO> nullMapCopy = SPIConfigServiceTestHelper.copySPIConfigMap(null);
        assertNull("null配置Map的复制应该为null", nullMapCopy);
    }

    /**
     * 测试描述信息方法
     */
    @Test
    public void testDescriptionMethods() {
        Map<String, SPIConfigDTO> configMap = SPIConfigServiceTestHelper.createValidSPIConfigMap();
        SPIConfigDTO config = configMap.get("product-detail-service");

        String configDesc = SPIConfigServiceTestHelper.getSPIConfigDescription(config);
        String mapDesc = SPIConfigServiceTestHelper.getSPIConfigMapDescription(configMap);

        assertNotNull("配置描述不应该为空", configDesc);
        assertNotNull("配置Map描述不应该为空", mapDesc);

        assertTrue("配置描述应该包含关键信息", configDesc.contains("SPIConfig"));
        assertTrue("配置描述应该包含别名", configDesc.contains("product-detail-service"));
        assertTrue("配置描述应该包含类型", configDesc.contains("PIGEON"));

        assertTrue("配置Map描述应该包含关键信息", mapDesc.contains("SPIConfigMap"));
        assertTrue("配置Map描述应该包含大小信息", mapDesc.contains("size=3"));

        // 测试null参数的描述
        String nullConfigDesc = SPIConfigServiceTestHelper.getSPIConfigDescription(null);
        String nullMapDesc = SPIConfigServiceTestHelper.getSPIConfigMapDescription(null);

        assertEquals("null配置描述应该正确", "null SPI config", nullConfigDesc);
        assertEquals("null配置Map描述应该正确", "null SPI config map", nullMapDesc);
    }

    /**
     * 测试验证方法
     */
    @Test
    public void testValidationMethods() {
        // 测试有效的别名和URL
        assertTrue("有效别名应该通过验证", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias("valid-service_123"));
        assertTrue("有效URL应该通过验证", 
                SPIConfigServiceTestHelper.isValidInterfaceUrl("com.sankuai.service.ValidService"));

        // 测试无效的别名
        assertFalse("null别名应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias(null));
        assertFalse("空别名应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias(""));
        assertFalse("空白别名应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceAlias("   "));

        // 测试无效的URL
        assertFalse("null URL应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceUrl(null));
        assertFalse("空URL应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceUrl(""));
        assertFalse("无点号URL应该无效", 
                SPIConfigServiceTestHelper.isValidInterfaceUrl("invalidurl"));

        // 测试配置Map验证
        Map<String, SPIConfigDTO> validMap = SPIConfigServiceTestHelper.createValidSPIConfigMap();
        assertTrue("有效配置Map应该通过验证", 
                SPIConfigServiceTestHelper.isValidSPIConfigMap(validMap));

        Map<String, SPIConfigDTO> emptyMap = SPIConfigServiceTestHelper.createEmptySPIConfigMap();
        assertFalse("空配置Map应该无效", 
                SPIConfigServiceTestHelper.isValidSPIConfigMap(emptyMap));

        assertFalse("null配置Map应该无效", 
                SPIConfigServiceTestHelper.isValidSPIConfigMap(null));
    }

    /**
     * 测试JSON解析功能
     */
    @Test
    public void testJsonParsing() {
        String validJson = SPIConfigServiceTestHelper.createValidSPIConfigJson();
        Map<String, SPIConfigDTO> parsedMap = SPIConfigServiceTestHelper.parseSPIConfigFromJson(validJson);

        assertNotNull("解析的配置Map不应该为空", parsedMap);
        assertEquals("解析的配置Map大小应该正确", 3, parsedMap.size());
        assertTrue("解析的配置Map应该有效", 
                SPIConfigServiceTestHelper.isValidSPIConfigMap(parsedMap));

        // 测试无效JSON解析
        try {
            SPIConfigServiceTestHelper.parseSPIConfigFromJson("invalid json");
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertTrue("异常消息应该包含解析失败信息", e.getMessage().contains("解析SPI配置JSON失败"));
        }

        // 测试空JSON解析
        Map<String, SPIConfigDTO> emptyResult = SPIConfigServiceTestHelper.parseSPIConfigFromJson("");
        assertNotNull("空JSON解析结果不应该为null", emptyResult);
        assertTrue("空JSON解析结果应该为空Map", emptyResult.isEmpty());
    }

    /**
     * 辅助方法：通过反射调用私有的 initSPIConfig 方法
     */
    private void invokeInitSPIConfig(String json) {
        try {
            java.lang.reflect.Method method = SPIConfigService.class.getDeclaredMethod("initSPIConfig", String.class);
            method.setAccessible(true);
            method.invoke(spiConfigService, json);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw new RuntimeException(e);
        }
    }
}
