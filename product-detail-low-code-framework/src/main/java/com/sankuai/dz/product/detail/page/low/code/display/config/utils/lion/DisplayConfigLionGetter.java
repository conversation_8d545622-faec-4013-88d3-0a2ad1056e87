package com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.fileconfig.FileConfigClient;
import com.sankuai.dz.product.detail.page.low.code.display.config.utils.json.JacksonUtils;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import static com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion.DisplayConfigLionConstant.APP_KEY;

/**
 * @Author: guangyujie
 * @Date: 2024/7/10 16:30
 */
@Slf4j
public class DisplayConfigLionGetter<T> {

    private final Class<T> tClass;

    public DisplayConfigLionGetter(Class<T> tClass) {
        this.tClass = tClass;
    }

    private static final FileConfigClient FILE_CONFIG_CLIENT = Lion.createFileConfigClient(APP_KEY);

    private final Map<String, T> dataCache = new HashMap<>();

    private final Map<String, ConfigListener> configListenerMap = new HashMap<>();

    public Set<String> getAllKeysFromDataCache() {
        return dataCache.keySet();
    }

    public Set<String> getAllKeysFromListener() {
        return configListenerMap.keySet();
    }

    public T get(String key) {
        return dataCache.get(key);
    }

    /**
     * 如果已经初始化了就不重新初始化
     */
    public synchronized void initSingleKey(final String key,
                                           final BiFunction<String, T, Void> preProcess,
                                           final Consumer<String> customListener) {
        if (configListenerMap.containsKey(key)) {
            return;
        }
        forceInitSingleKey(key, preProcess, customListener);
    }

    /**
     * 强制更新
     */
    public synchronized void forceInitSingleKey(final String key,
                                                final BiFunction<String, T, Void> preProcess,
                                                final Consumer<String> customListener) {
        try {
            if (StringUtils.isBlank(key)) {
                throw new IllegalArgumentException("key can't be blank");
            }
            //创建ListenerProcessor
            ConfigListener configListener = configEvent ->
                    configListenerProcessor(key, configEvent.getValue(), preProcess, customListener);
            //绑定ListenerProcessor到Lion监听器上，如果已绑定则重新绑定
            if (configListenerMap.containsKey(key)) {
                FILE_CONFIG_CLIENT.removeListener(key, configListenerMap.get(key));
                configListenerMap.remove(key);
            }
            FILE_CONFIG_CLIENT.addListener(key, configListener);
            configListenerMap.put(key, configListener);
            //同步执行一次ListenerProcessor
            String json = FILE_CONFIG_CLIENT.getFileContent(key);
            configListenerProcessor(key, json, preProcess, customListener);
        } catch (Throwable throwable) {
            String className = this.getClass().getSimpleName();
            Cat.logError(className, new LowCodeConfigFatalException(throwable));
            log.error("{}<{}> forceInitSingleKey 失败", className, key, throwable);
            throw throwable;
        }
    }

    /**
     * Lion监听器的处理方法
     */
    private synchronized void configListenerProcessor(final String key,
                                                      final String json,
                                                      final BiFunction<String, T, Void> preProcess,
                                                      final Consumer<String> customListener) {
        T data = JacksonUtils.deserialize(json, tClass);
        //如果前置处理器不为空，则执行
        if (preProcess != null) {
            preProcess.apply(key, data);//如果前置处理器失败会抛出异常，打断更新缓存且不执行customListener
        }
        //如果数据不为空则存入缓存
        if (data != null) {
            dataCache.put(key, data);
        }
        //继续执行业务自定义监听器
        if (customListener != null) {
            customListener.accept(key);
        }
    }

}
