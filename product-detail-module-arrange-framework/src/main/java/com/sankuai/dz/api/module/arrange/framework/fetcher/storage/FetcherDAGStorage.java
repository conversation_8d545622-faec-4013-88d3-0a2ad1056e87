package com.sankuai.dz.api.module.arrange.framework.fetcher.storage;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSCycleChecker;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSUtils;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 15:49
 */
public class FetcherDAGStorage {

    private final static Map<String, DAG> dagMap = new HashMap<>();

    public static DAG getDAG(String startFetcherName) {
        DAG dag = dagMap.get(startFetcherName);
        if (dag == null) {
            throw new FetcherFatalException(String.format("不存在该startFetcher(%s)对应的DAG", startFetcherName));
        }
        return dag;
    }

    public static synchronized void initFetcherDAGStorage() {
        final Set<String> startFetcherNames = findStartFetchers();
        final Map<String, Set<String>> entireDAGParentGraph = buildDAGParentGraph();
        //把ParentGraph转换为childrenGraph
        Map<String, Set<String>> entireDAGChildrenGraph = DAGBFSUtils.convertParentGraphToChildrenGraph(entireDAGParentGraph);
        //检查DAGGraph是否完整，Map.Keys是否包含了所有Map.Values
        DAGBFSUtils.checkDAGGraphIntegrity(entireDAGParentGraph);
        DAGBFSUtils.checkDAGGraphIntegrity(entireDAGChildrenGraph);
        //检查DAG是否有环
        DAGBFSCycleChecker.KahnResult kahnResult = DAGBFSCycleChecker.performKahnCheck(entireDAGChildrenGraph, entireDAGParentGraph);
        kahnResult.checkCycle(entireDAGChildrenGraph);
        //针对所有起始点构建DAG子图信息
        for (String startFetcher : startFetcherNames) {
            //对每个起始节点构建子图
            dagMap.put(
                    startFetcher,
                    new DAG(
                            DAG.DAGBuilder.builder()
                                    .start(startFetcher)
                                    .entireDAGChildrenGraph(entireDAGChildrenGraph)
                                    .entireDAGParentGraph(entireDAGParentGraph)
                                    .build()
                    )
            );
        }
    }

    /**
     * 获取DAG所有开始节点
     */
    public static Set<String> findStartFetchers() {
        return FetcherDefinitionStorage.getAllFetcherDefinition().stream()
                .filter(FetcherDefinition::isStartFetcher)
                .map(FetcherDefinition::getFetcherName)
                .collect(Collectors.toSet());
    }

    /**
     * 构建DAG邻接矩阵
     * 每个fetcher只知道父节点有哪些，所以需要反向构建每个fetcher的子节点
     */
    public static Map<String, Set<String>> buildDAGParentGraph() {
        Map<String, Set<String>> map = new HashMap<>();
        for (FetcherDefinition fetcherDefinition : FetcherDefinitionStorage.getAllFetcherDefinition()) {
            map.put(fetcherDefinition.getFetcherName(), fetcherDefinition.getNameOfPreviousLayerDependenciesInDAG());
        }
        return map;
    }

}
