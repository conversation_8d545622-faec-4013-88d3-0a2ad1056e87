package com.sankuai.dz.product.detail.metadata.sdk.page.config;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.LayoutConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.dto.ConfigTemplateResponse;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.dto.MergeResultCache;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler.ConfigMergeHandler;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.AbstractMergeRule;
import com.sankuai.dz.product.detail.metadata.sdk.utils.json.JacksonUtils;
import com.sankuai.dz.product.detail.metadata.sdk.utils.lion.PageConfigLionGetter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 16:27
 */
@Slf4j
@Component
public class ProductDetailPageConfigService implements InitializingBean {

    /**
     * 可选规则执行器缓存
     */
    private final Map<ConfigMergeTypeEnum, ConfigMergeHandler> MERGE_HANDLER_MAP = new ConcurrentHashMap<>();

    /**
     * 可选规则执行器初始化
     */
    private void initMergeHandlerMap() throws InstantiationException, IllegalAccessException {
        for (ConfigMergeTypeEnum mergeType : ConfigMergeTypeEnum.values()) {
            try {
                ConfigMergeHandler configMergeHandler = mergeType.getConfigMergeHandlerClass().newInstance();
                MERGE_HANDLER_MAP.put(mergeType, configMergeHandler);
            } catch (Throwable throwable) {
                Cat.logError("ProductDetailPageConfigService", new LayoutConfigFatalException(throwable));
                log.error("ProductDetailPageConfigService.init.MERGE_HANDLER_MAP", throwable);
                throw throwable;
            }
        }
    }

    private final static String APP_KEY = "com.sankuai.dzshoppingguide.detail.gateway";

    /**
     * 所有页面布局配置的lionKey集合
     */
    private final static String ALL_PAGE_CONFIG_LION_KEY = "com.sankuai.dzshoppingguide.detail.gateway.all.page.config.key";

    /**
     * 所有页面配置缓存
     */
    private final PageConfigLionGetter<PageConfigTemplateDTO> pageConfigTemplateCache = new PageConfigLionGetter<>(PageConfigTemplateDTO.class);

    /**
     * 页面配置merge后的缓存数据，会在对应Key的配置数据刷新后同步清除缓存
     */
    private final MergeResultCache mergeResultCache = new MergeResultCache();

    private void initPageConfigTemplateCache() {
        try {
            List<String> allConfigLionKey = Lion.getList(APP_KEY, ALL_PAGE_CONFIG_LION_KEY, String.class, new ArrayList<>());
            for (String lionKey : allConfigLionKey) {
                //项目启动强制更新所有key的LionFileResourceGetter
                pageConfigTemplateCache.forceInitSingleKey(lionKey, mergeResultCache::clean);//将merge结果的clean方法绑定到Lion监听器上
            }
            //增加ALL_CONFIG_LION_KEY的监听器，随时增加LionFileResourceGetter的监听器，这里用initSingleKey不会强制更新已初始化的key
            Lion.addConfigListener(ALL_PAGE_CONFIG_LION_KEY, configEvent -> {
                List<String> newAllConfigLionKey = Lion.getList(APP_KEY, ALL_PAGE_CONFIG_LION_KEY, String.class, new ArrayList<>());
                for (String lionKey : newAllConfigLionKey) {
                    pageConfigTemplateCache.initSingleKey(lionKey, mergeResultCache::clean);
                }
            });
        } catch (Throwable e) {
            Cat.logError("ProductDetailPageConfigService", new LayoutConfigFatalException(e));
            log.error("ProductDetailPageConfigService.init.pageConfigTemplateCache", e);
            throw e;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        //可选规则执行器初始化
        initMergeHandlerMap();
        //初始化页面配置，抛出异常直接中断启动
        initPageConfigTemplateCache();
    }

    /**
     * 获取页面配置模板
     * 需要框架开发同学注意，返回值的任何属性都不能修改！！！为了性能考虑，就不对返回值进行copy后返回，减少一次序列化损耗。
     */
    public PageConfigTemplateDTO getPageConfigTemplate(PageConfigRoutingKey routingKey) {
        ConfigTemplateResponse configTemplateResponse = getMainConfigTemplate(routingKey);
        return configTemplateResponse.getConfig();
    }

    /**
     * 获取页面配置
     * 需要框架开发同学注意，返回值的任何属性都不能修改！！！为了性能考虑，就不对返回值进行copy后返回，减少一次序列化损耗。
     */
    public PageConfigDataDTO getPageConfig(PageConfigRoutingKey routingKey) {
        //先根据关键路由key获取兜底页面配置
        ConfigTemplateResponse configTemplateResponse = getMainConfigTemplate(routingKey);
        //从缓存中获取merge后的结果
        final String optionalKey = routingKey.buildOptionalKey();
        PageConfigDataDTO mergeConfigCacheData = mergeResultCache.get(configTemplateResponse.getKey(), optionalKey);
        if (mergeConfigCacheData != null) {
            return mergeConfigCacheData;
        }
        //缓存中没有就实时计算
        PageConfigDataDTO newMergeConfigCacheData = mergeConfig(routingKey, configTemplateResponse.getConfig());
        mergeResultCache.put(configTemplateResponse.getKey(), optionalKey, newMergeConfigCacheData);
        return newMergeConfigCacheData;
    }

    /**
     * merge结果，上层会有缓存，所以性能损耗不大
     */
    @SuppressWarnings("unchecked")
    private PageConfigDataDTO mergeConfig(final PageConfigRoutingKey pageConfigRoutingKey,
                                          final PageConfigTemplateDTO configTemplate) {
        if (CollectionUtils.isEmpty(configTemplate.getOptionalRules())) {
            return configTemplate.getMainConfigData();
        }
        //因为会对结果进行merge，会改变mainConfigData的数据，为了不影响缓存数据，用深copy消除引用
        PageConfigTemplateDTO copiedPageConfigTemplate = JacksonUtils.copy(configTemplate, PageConfigTemplateDTO.class);
        for (AbstractMergeRule optionalConfig : copiedPageConfigTemplate.getOptionalRules()) {
            if (!pageConfigRoutingKey.buildSelectedOptionalKey(optionalConfig.getRoutingKeySet()).equals(optionalConfig.buildOptionalKey())) {
                continue;//可选规则有自己的准入条件，如果入参不匹配就跳过
            }
            ConfigMergeHandler configMergeHandler = MERGE_HANDLER_MAP.get(optionalConfig.getMergeType());
            if (configMergeHandler == null) {
                throw new IllegalArgumentException("找不到mergeHandler:" + optionalConfig.getMergeType());
            }
            configMergeHandler.merge(copiedPageConfigTemplate.getMainConfigData(), optionalConfig);
        }
        return copiedPageConfigTemplate.getMainConfigData();
    }

    /**
     * 获取关键配置，有降级策略，从三级类目->二级类目->一级类目->商品类型，一定能获取到配置
     */
    private ConfigTemplateResponse getMainConfigTemplate(PageConfigRoutingKey pageConfigRoutingKey) {
        pageConfigRoutingKey.checkParams();
        //先看三级类目
        String thirdCategoryIdMainKey = pageConfigRoutingKey.buildThirdCategoryIdMainKey();
        PageConfigTemplateDTO thirdCategoryIdConfigTemplate = pageConfigTemplateCache.get(thirdCategoryIdMainKey);
        if (thirdCategoryIdConfigTemplate != null) {
            return new ConfigTemplateResponse(thirdCategoryIdMainKey, thirdCategoryIdConfigTemplate);
        }
        //再降级到二级类目
        String secondCategoryIdMainKey = pageConfigRoutingKey.buildSecondCategoryIdMainKey();
        PageConfigTemplateDTO secondCategoryIdConfigTemplate = pageConfigTemplateCache.get(secondCategoryIdMainKey);
        if (secondCategoryIdConfigTemplate != null) {
            return new ConfigTemplateResponse(secondCategoryIdMainKey, secondCategoryIdConfigTemplate);
        }
        //再降级到一级类目
        String firstCategoryIdMainKey = pageConfigRoutingKey.buildFirstCategoryIdMainKey();
        PageConfigTemplateDTO firstCategoryIdConfigTemplate = pageConfigTemplateCache.get(firstCategoryIdMainKey);
        if (firstCategoryIdConfigTemplate != null) {
            return new ConfigTemplateResponse(firstCategoryIdMainKey, firstCategoryIdConfigTemplate);
        }
        //最后降级到商品类型兜底，这块必须保证有兜底数据
        String defaultMainKey = pageConfigRoutingKey.buildDefaultMainKey();
        PageConfigTemplateDTO defaultConfigTemplate = pageConfigTemplateCache.get(defaultMainKey);
        if (defaultConfigTemplate != null) {
            return new ConfigTemplateResponse(defaultMainKey, defaultConfigTemplate);
        }
        throw new IllegalArgumentException("查不到页面配置模板，兜底被击穿!!!");
    }

    /**
     * 获取当前配置中所有key
     */
    public Set<String> getAllPageConfigTemplateCacheKeys() {
        Set<String> allKeysFromDataCache = pageConfigTemplateCache.getAllKeysFromDataCache();
        Set<String> allKeysFromListener = pageConfigTemplateCache.getAllKeysFromListener();
        if (!CollectionUtils.isEqualCollection(allKeysFromDataCache, allKeysFromListener)) {
            throw new IllegalArgumentException("dataCache与Listener不一致");
        }
        return allKeysFromDataCache;
    }

}
