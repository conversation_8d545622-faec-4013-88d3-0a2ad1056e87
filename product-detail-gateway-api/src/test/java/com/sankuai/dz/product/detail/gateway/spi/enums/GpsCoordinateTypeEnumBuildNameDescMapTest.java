package com.sankuai.dz.product.detail.gateway.spi.enums;

import static org.junit.Assert.assertEquals;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GpsCoordinateTypeEnumBuildNameDescMapTest {

    @Mock
    private Map<Integer, String> mockMap;

    /**
     * 测试 buildNameDescMap 方法，期望返回一个包含所有枚举值名称和描述的 Map
     */
    @Test
    public void testBuildNameDescMap() throws Throwable {
        // arrange
        int expectedSize = GpsCoordinateTypeEnum.values().length;
        // act
        Map<String, String> result = GpsCoordinateTypeEnum.buildNameDescMap();
        // assert
        assertEquals(expectedSize, result.size());
        for (GpsCoordinateTypeEnum enumValue : GpsCoordinateTypeEnum.values()) {
            assertEquals(enumValue.getDesc(), result.get(enumValue.name()));
        }
    }

    @Test
    public void testFromCode_WhenCodeExists_ShouldReturnCorrectEnum() {
        // arrange
        int existingCode = 1;
        String expectedDesc = "火星坐标系";
        // act
        GpsCoordinateTypeEnum result = GpsCoordinateTypeEnum.fromCode(existingCode);
        // assert
        assertNotNull(result);
        assertEquals(existingCode, result.getCode());
        assertEquals(expectedDesc, result.getDesc());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeNotExists_ShouldThrowException() {
        // arrange
        int nonExistingCode = -1;
        // act & assert
        GpsCoordinateTypeEnum.fromCode(nonExistingCode);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeIsMaxInteger_ShouldThrowException() {
        // arrange
        int maxIntCode = Integer.MAX_VALUE;
        // act & assert
        GpsCoordinateTypeEnum.fromCode(maxIntCode);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testFromCode_WhenCodeIsMinInteger_ShouldThrowException() {
        // arrange
        int minIntCode = Integer.MIN_VALUE;
        // act & assert
        GpsCoordinateTypeEnum.fromCode(minIntCode);
    }

    @Test
    public void testFromCode_WhenCodeNotExists_ShouldThrowExceptionWithCorrectMessage() {
        // arrange
        int invalidCode = 999;
        String expectedMessage = "无效code:" + invalidCode;
        // act
        try {
            GpsCoordinateTypeEnum.fromCode(invalidCode);
            fail("Expected IllegalArgumentException to be thrown");
        } catch (IllegalArgumentException e) {
            // assert
            assertEquals(expectedMessage, e.getMessage());
        }
    }
}
