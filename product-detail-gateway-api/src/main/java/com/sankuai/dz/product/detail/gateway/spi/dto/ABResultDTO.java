package com.sankuai.dz.product.detail.gateway.spi.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/1/10 16:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ABResultDTO implements Serializable {

    /**
     * 模块标识
     */
    private String moduleKey;
    /**
     * 模块实验结果
     */
    private List<ABDetailDTO> abDetails;

}
