package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.field;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;

import java.io.Serializable;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/5/29 10:52
 */
@Getter
public abstract class FieldDisplayConfigBO implements Serializable {

    /**
     * 字段名
     */
    private final String fieldName;

    /**
     * 是否是集合类型
     */
    private final boolean collection;

    /**
     * 是否是POJO，如果是POJO类型则进行递归，递归层数不超过5层，这个在元数据层已卡死
     */
    @JsonIgnore
    public abstract boolean isPojo();

    public FieldDisplayConfigBO(final String fieldName, final boolean collection) {
        this.fieldName = fieldName;
        this.collection = collection;
    }

}
