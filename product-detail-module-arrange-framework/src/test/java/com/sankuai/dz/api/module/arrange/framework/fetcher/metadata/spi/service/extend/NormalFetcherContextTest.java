package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NormalFetcherContextTest {

    @Mock
    private FetcherEngine fetcherEngine;

    /**
     * Test case for handling exception scenario in doInitContextExceptionally
     */
    @Test
    public void testDoInitContextExceptionally_ShouldSetFailedFutureAndReturnResult() throws Throwable {
        // arrange
        TestNormalFetcherContext context = new TestNormalFetcherContext();
        RuntimeException testException = new RuntimeException("Test exception");
        // act
        FetcherContextInitResult result = context.doInitContextExceptionally(fetcherEngine, testException);
        // assert
        assertNotNull(result, "Result should not be null");
        assertSame(context, result.getSelf(), "Result should contain the context itself");
        CompletableFuture<FetcherResponse<TestReturnValueDTO>> future = context.getFuture();
        assertNotNull(future, "Future should not be null");
        assertTrue(future.isDone(), "Future should be completed");
        FetcherResponse<TestReturnValueDTO> response = future.get();
        assertNotNull(response, "Response should not be null");
        assertFalse(response.isSuccess(), "Response should indicate failure");
        assertEquals(testException, response.getError(), "Response should contain the original exception");
    }

    // Test implementation class
    private static class TestNormalFetcherContext extends NormalFetcherContext<TestReturnValueDTO> {

        @Override
        public FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) throws Exception {
            return null;
        }

        @Override
        protected CompletableFuture<TestReturnValueDTO> doFetch() throws Exception {
            return null;
        }
    }

    // Test DTO implementation
    private static class TestReturnValueDTO extends FetcherReturnValueDTO {
    }
}
