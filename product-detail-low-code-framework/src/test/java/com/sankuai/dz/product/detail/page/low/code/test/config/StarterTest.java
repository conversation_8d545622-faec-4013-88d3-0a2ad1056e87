package com.sankuai.dz.product.detail.page.low.code.test.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.digital.arch.starter.DAStarter;
import com.sankuai.athena.digital.arch.starter.DAStarterItemEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigStorage;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;
import com.sankuai.dz.product.detail.page.low.code.engine.DisplayRuleEngineFactory;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.ComponentVO;
import com.sankuai.dz.product.detail.page.low.code.runtime.LowCodeRuntimeProcessService;
import com.sankuai.dz.product.detail.page.low.code.runtime.request.LowCodeProcessRequest;
import com.sankuai.dz.product.detail.page.low.code.runtime.response.ComponentProcessResult;
import com.sankuai.dz.product.detail.page.low.code.runtime.response.LowCodeProcessResponse;
import com.sankuai.dz.product.detail.page.low.code.starter.LowCodeStarter;
import org.junit.Test;

import javax.xml.crypto.Data;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/12 16:47
 */
public class StarterTest {


    @Test
    public void test() throws Exception {
        //启动
        DAStarter daStarter = dAStarter();
        daStarter.afterPropertiesSet();
        LowCodeStarter lowCodeStarter = lowCodeStarter();
        lowCodeStarter.afterPropertiesSet();

        //读配置
        final PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.ProductDetail.name());
        pageConfigRoutingKey.setProductType(ProductTypeEnum.DEAL.getCode());
        pageConfigRoutingKey.setProductSecondCategoryId(304);
        pageConfigRoutingKey.getOptionalRoutingKeyMap().put(OptionalRoutingKeyEnum.ObjectId.name(), "********");
        pageConfigRoutingKey.getOptionalRoutingKeyMap().put(OptionalRoutingKeyEnum.ObjectVersion.name(), "12312");
        ConfigQueryResponse config = DisplayConfigStorage.getConfig(pageConfigRoutingKey);
        Map<String, ModuleDisplayConfigBO> matchedModuleDisplayConfigs = config.getConfig().getMatchedModuleDisplayConfigs(pageConfigRoutingKey);
        ModuleDisplayConfigBO moduleDisplayConfigBO = matchedModuleDisplayConfigs.get("module_detail_structured_detail");
        if (moduleDisplayConfigBO == null) {
            throw new IllegalArgumentException("找不到模块配置");
        }

        long startTime = System.currentTimeMillis();

        //执行
        LowCodeProcessRequest request = new LowCodeProcessRequest();
        request.setComponentConfigs(moduleDisplayConfigBO.getComponentConfigList());
        request.setData(buildMockData());
        LowCodeProcessResponse response = LowCodeRuntimeProcessService.process(request);
        System.out.println(System.currentTimeMillis() - startTime);
        List<ComponentVO> collect = response.getResult()
                .stream()
                .map(ComponentProcessResult::getComponentBO)
                .map(bo -> bo.buildVO(0))
                .collect(Collectors.toList());
        System.out.println(System.currentTimeMillis() - startTime);
    }

    private static JSONObject buildMockData() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("XXFetcher", new JSONObject() {
            {
                put("list", new JSONArray(Lists.newArrayList(
                        new JSONObject() {{
                            put("itemName", "itemName1");
                            put("itemDesc", "itemDesc1");
                            put("itemExtra", "itemExtra1");
                        }},
                        new JSONObject() {{
                            put("itemName", "itemName2");
                            put("itemDesc", "itemDesc2");
                            put("itemExtra", "itemExtra2");
                        }},
                        new JSONObject() {{
                            put("itemName", "itemName3");
                            put("itemDesc", "itemDesc3");
                            put("itemExtra", "itemExtra3");
                        }}
                )));
            }
        });
        jsonObject.put(
                "SSFetcher", new JSONObject() {{
                    put("aa", new JSONObject() {{
                        put("bb", "cc");
                    }});
                }}
        );
        return jsonObject;
    }

    public LowCodeStarter lowCodeStarter() {
        List<String> basePackages = getBasePackages();
        LowCodeStarter lowCodeStarter = new LowCodeStarter();
        lowCodeStarter.setBasePackages(basePackages);
        return lowCodeStarter;
    }


    public DAStarter dAStarter() {
        DAStarter daStarter = new DAStarter();
        //仅需要解析类的元数据和数据上报
        daStarter.setStarterItemEnums(Sets.newHashSet(
                DAStarterItemEnum.CLASS_PARSER
        ));
        List<String> basePackages = getBasePackages();
        daStarter.setBasePackages(basePackages);
        return daStarter;
    }

    private static List<String> getBasePackages() {
        List<String> basePackages = Lists.newArrayList(
                "com.sankuai.dz.product.detail.page.low.code.test.config"
        );
        basePackages.add("com.sankuai.dz.product.detail.page.low.code.entity.spi.bo");
        return basePackages;
    }

    @Test
    public void testAvaitor() {
        JSONObject jsonObject = buildMockData();
        jsonObject.put("index",1);
        Object execute = DisplayRuleEngineFactory.execute(
                RuleEngineType.AVIATOR.getCode(),
                "let a = XXFetcher.list[index]; a.itemName",
//                "XXFetcher.list[index].itemName",
                jsonObject
        );
        execute = DisplayRuleEngineFactory.execute(
                RuleEngineType.AVIATOR.getCode(),
                "SSFetcher.aa.bb",
                buildMockData()
        );
        assert Objects.equals(execute, "cc");
    }

}
