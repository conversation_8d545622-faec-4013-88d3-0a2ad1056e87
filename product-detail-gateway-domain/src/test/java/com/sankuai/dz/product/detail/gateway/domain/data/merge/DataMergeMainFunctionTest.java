package com.sankuai.dz.product.detail.gateway.domain.data.merge;

import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.dto.DataMergeRequest;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.service.ProductDetailPageDataMergeService;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;

public class DataMergeMainFunctionTest {

    @InjectMocks
    private DataMergeMainFunction dataMergeMainFunction;

    @Mock
    private ProductDetailPageDataMergeService mergeService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testProcessNormal() throws Throwable {
        // arrange
        GatewayContext context = mock(GatewayContext.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        when(context.getSpiResponseList()).thenReturn(Collections.singletonList(response));
        when(mergeService.merge(any(DataMergeRequest.class))).thenReturn(new com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse());
        // act
        dataMergeMainFunction.process(context);
        // assert
        verify(context, times(1)).setPageResponse(any());
    }

    /**
     * 测试异常场景：数据合并过程中出现异常
     */
    @Test(expected = Exception.class)
    public void testProcessMergeException() throws Throwable {
        // arrange
        GatewayContext context = mock(GatewayContext.class);
        GenericProductDetailPageResponse response = mock(GenericProductDetailPageResponse.class);
        when(context.getSpiResponseList()).thenReturn(Collections.singletonList(response));
        when(mergeService.merge(any(DataMergeRequest.class))).thenThrow(new Exception());
        // act
        dataMergeMainFunction.process(context);
    }
}
