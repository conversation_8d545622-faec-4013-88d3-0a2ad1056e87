package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.tab.TabModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.ModuleRemoveRule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:15
 */
public class ModuleRemoveHandler implements ConfigMergeHandler<ModuleRemoveRule> {

    @Override
    public void merge(final PageConfigDataDTO mainConfigData, final ModuleRemoveRule rule) {
        if (StringUtils.isBlank(rule.getRemoveModuleKey())) {
            return;
        }

        // 处理常规模块
        ModuleGroupConfigDTO moduleGroupConfigDTO = mainConfigData.getModuleGroup(
                rule.getGroupName(), rule.getRemoveModuleKey()
        );
        if (moduleGroupConfigDTO != null) {
            List<ModuleConfigDTO> filteredModuleList = moduleGroupConfigDTO.getModuleList().stream()
                    .filter(module -> !module.getModuleKey().equals(rule.getRemoveModuleKey()))
                    .collect(Collectors.toList());
            moduleGroupConfigDTO.setModuleList(filteredModuleList);
        }

        // 处理tab栏内的模块
        ModuleGroupConfigDTO tabGroupConfigDTO = mainConfigData.getModuleGroup(
                "", "module_detail_tab"
        );
        if (tabGroupConfigDTO == null || CollectionUtils.isEmpty(tabGroupConfigDTO.getModuleList())) {
            return;
        }
        TabModuleConfigDTO tabModuleConfig = (TabModuleConfigDTO) tabGroupConfigDTO.getModuleList().get(0);
        if (CollectionUtils.isEmpty(tabModuleConfig.getTabItemList())) {
            return;
        }
        for (int i = 0; i < tabModuleConfig.getTabItemList().size(); i++) {
            if (CollectionUtils.isEmpty(tabModuleConfig.getTabItemList().get(i).getGroupConfig())) {
                continue;
            }

            List<ModuleGroupConfigDTO> remainingGroupConfigs = new ArrayList<>();
            for (int j = 0; j < tabModuleConfig.getTabItemList().get(i).getGroupConfig().size(); j++) {
                ModuleGroupConfigDTO tabGroupConfig = tabModuleConfig.getTabItemList().get(i).getGroupConfig().get(j);
                if (CollectionUtils.isEmpty(tabGroupConfig.getModuleList())) {
                    continue;
                }

                List<ModuleConfigDTO> filteredTabModuleList = tabGroupConfig.getModuleList().stream()
                        .filter(module -> !module.getModuleKey().equals(rule.getRemoveModuleKey()))
                        .collect(Collectors.toList());

                // 如果过滤后的模块列表不为空，则保留该tabGroupConfig
                if (!filteredTabModuleList.isEmpty()) {
                    tabGroupConfig.setModuleList(filteredTabModuleList);
                    remainingGroupConfigs.add(tabGroupConfig);
                }
            }
            
            // 更新tab项的groupConfig列表，移除空的groupConfig
            if (CollectionUtils.isNotEmpty(remainingGroupConfigs)) {
                tabModuleConfig.getTabItemList().get(i).setGroupConfig(remainingGroupConfigs);
            } else {
                tabModuleConfig.getTabItemList().remove(i);
                break;
            }
        }
    }

    @Override
    @JsonIgnore
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_REMOVE;
    }

}
