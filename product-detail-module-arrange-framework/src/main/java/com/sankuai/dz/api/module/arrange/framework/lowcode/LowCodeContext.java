package com.sankuai.dz.api.module.arrange.framework.lowcode;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/6/4 16:07
 */

public class LowCodeContext {

    /**
     * 本服务相关模块的低代码配置
     * key:moduleKey，全局唯一
     * value:配置
     */
    private final Map<String, ModuleDisplayConfigBO> moduleConfigs;

    /**
     * 本服务相关模块所需的数据源与字段
     */
    private final Map<String, DataSourceConfigBO> allDataSource;

    public LowCodeContext() {
        this.moduleConfigs = new HashMap<>();
        this.allDataSource = new HashMap<>();
    }

    public LowCodeContext(final Map<String, ModuleDisplayConfigBO> moduleConfigs,
                          final Map<String, DataSourceConfigBO> allDataSource) {
        this.moduleConfigs = moduleConfigs;
        this.allDataSource = allDataSource;
    }

    public ModuleDisplayConfigBO getModuleDisplayConfig(String moduleKey) {
        return moduleConfigs.get(moduleKey);
    }

    public DataSourceConfigBO getDataSourceConfig(String fetcherName) {
        return allDataSource.get(fetcherName);
    }

    public List<DataSourceConfigBO> getAllRelatedDataSourceConfig() {
        return new ArrayList<>(allDataSource.values());
    }

    public Set<String> getAllDependentFetcherNames() {
        return allDataSource.keySet();
    }

}
