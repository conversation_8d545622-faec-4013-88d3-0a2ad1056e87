package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.ComponentFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/7 10:09
 */
@Slf4j
public abstract class ComponentFetcherContext<
        REQUEST,//component和aggregate共用的request类型
        AGGREGATE_RESULT extends FetcherReturnValueDTO,//component和aggregate共用的returnValue类型
        RESULT extends FetcherReturnValueDTO//component按需返回的returnValue类型
        > extends BaseFetcherContext<RESULT, ComponentFetcherDefinition> {

    /**
     * 正常执行初始化，因为已经初始化过了，所以理论上不会走到这里，除非AggregateFetcher初始化失败
     * 如果AggregateFetcher初始化失败，则ComponentContext的future为null，会跳过get，相当于ComponentContext调用失败
     */
    @Override
    public FetcherContextInitResult doInitContext(final FetcherEngine fetcherEngine) {
        return new FetcherContextInitResult(this);
    }

    /**
     * 初始化异常兜底，为了不打断整个流程，一定要对初始化异常的情况做Fetcher维度的降级，不能抛出异常
     */
    @Override
    protected FetcherContextInitResult doInitContextExceptionally(final FetcherEngine fetcherEngine,
                                                                  final Throwable throwable) {
        this.future = CompletableFuture.completedFuture(FetcherResponse.fail(throwable));
        return new FetcherContextInitResult(this);
    }

    /**
     * ComponentFetcher不执行doInitContext
     * 提前在对应的AggregateFetcher里执行fulfillRequest方法填充Request
     */
    public abstract void fulfillRequest(final REQUEST request);

    /**
     * ComponentFetcher不执行doInitContext
     * 提前在对应的AggregateFetcher里执行fulfillRequest后，再执行buildFuture方法进行编排
     */
    public void buildFuture(final CompletableFuture<FetcherResponse<AGGREGATE_RESULT>> future) {
        try {
            this.future = future.thenApply(this::mapResult);
        } catch (Throwable throwable) {
            log.error("{}执行buildFuture内部异常", this.fetcherDefinition.getFetcherName(), throwable);
            this.future = CompletableFuture.completedFuture(FetcherResponse.fail(throwable));
        }
    }

    /**
     * 结果转换AGGREGATE_RESULT->RESULT
     */
    protected abstract FetcherResponse<RESULT> mapResult(final FetcherResponse<AGGREGATE_RESULT> aggregateResult);

}
