package com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 21:55
 */
public interface SPICallHandler<T> {

    /**
     * 获取服务
     */
    T getSpiService(final SPIConfigDTO spiConfigDTO);

    /**
     * 刷新spiMap
     */
    void refreshSPIMap(final Map<String, T> spiMap);

    /**
     * 发起请求
     */
    CompletableFuture<GenericProductDetailPageResponse> call(final SPIConfigDTO spiConfigDTO,
                                                             final ProductDetailPageRequest request);

    /**
     * 调用类型
     */
    InterfaceTypeEnum getInterfaceType();

}
