package com.sankuai.dz.product.detail.gateway.domain.data.merge.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.PageConfigDataVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleItemVO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

@RunWith(MockitoJUnitRunner.class)
public class ModulePositionModifyServiceTest {

    @InjectMocks
    private ModulePositionModifyService service;

    private static final String DEAL_AVAILABLE_SHOP_MODULE_KEY = "module_detail_deal_available_shop";

    private static final String DEAL_SHOP_TAG_MODULE_KEY = "module_detail_deal_shop_tag";

    private static final String PURCHASE_NOTE_TAB = "purchase_note";

    private ModuleConfigVO createModuleConfigVO(final String moduleKey) {
        return new ModuleConfigVO() {

            @Override
            public String getModuleKey() {
                return moduleKey;
            }
        };
    }

    private List<List<ModuleConfigVO>> createModuleList(ModuleConfigVO module) {
        List<List<ModuleConfigVO>> moduleList = new ArrayList<>();
        moduleList.add(Collections.singletonList(module));
        return moduleList;
    }

    private TabModuleItemVO createTabModuleItem() {
        TabModuleItemVO tabItem = new TabModuleItemVO();
        tabItem.setTabKey(PURCHASE_NOTE_TAB);
        ModuleConfigVO availableShopModule = createModuleConfigVO(DEAL_AVAILABLE_SHOP_MODULE_KEY);
        tabItem.setModuleList(createModuleList(availableShopModule));
        return tabItem;
    }

    private TabModuleConfigVO createTabModule() {
        TabModuleConfigVO tabModule = new TabModuleConfigVO();
        tabModule.setTabConfigs(Collections.singletonList(createTabModuleItem()));
        return tabModule;
    }

    /**
     * Test when product type is not DEAL, no modification should happen
     */
    @Test
    public void testExecuteWhenProductTypeNotDeal() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // Not DEAL
        request.setProductType(2);
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> originalData = new ArrayList<>();
        TabModuleConfigVO tabModule = createTabModule();
        originalData.add(Collections.singletonList(tabModule));
        pageConfig.setData(originalData);
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 1, pageConfig.getData().size());
        TabModuleConfigVO resultModule = (TabModuleConfigVO) pageConfig.getData().get(0).get(0);
        assertEquals("Tab configs should contain one item", 1, resultModule.getTabConfigs().size());
        assertEquals("Module list should contain one item", 1, resultModule.getTabConfigs().get(0).getModuleList().size());
        assertEquals("Available shop module should remain in original position", DEAL_AVAILABLE_SHOP_MODULE_KEY, resultModule.getTabConfigs().get(0).getModuleList().get(0).get(0).getModuleKey());
    }

    /**
     * Test when available shop should be moved before tabs
     */
    @Test
    public void testExecuteWhenAvailableShopShouldMove() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DEAL
        request.setProductType(1);
        request.setPageSource("test-source");
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> data = new ArrayList<>();
        TabModuleConfigVO tabModule = createTabModule();
        data.add(Collections.singletonList(tabModule));
        pageConfig.setData(data);
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 1, pageConfig.getData().size());
        TabModuleConfigVO resultModule = (TabModuleConfigVO) pageConfig.getData().get(0).get(0);
        assertNotNull("Tab module should exist", resultModule);
        assertFalse("Tab configs should not be empty", resultModule.getTabConfigs().isEmpty());
        assertFalse("Module list should not be empty", resultModule.getTabConfigs().get(0).getModuleList().isEmpty());
    }

    /**
     * Test when shop tag should be hidden
     */
    @Test
    public void testExecuteWhenShopTagShouldHide() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DEAL
        request.setProductType(1);
        request.setPageSource("test-source");
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> data = new ArrayList<>();
        ModuleConfigVO shopTagModule = createModuleConfigVO(DEAL_SHOP_TAG_MODULE_KEY);
        data.add(Collections.singletonList(shopTagModule));
        pageConfig.setData(data);
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 1, pageConfig.getData().size());
        assertEquals("Shop tag module should remain", DEAL_SHOP_TAG_MODULE_KEY, pageConfig.getData().get(0).get(0).getModuleKey());
    }

    /**
     * Test when both operations should be performed
     */
    @Test
    public void testExecuteWhenBothConditionsMet() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DEAL
        request.setProductType(1);
        request.setPageSource("test-source");
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> data = new ArrayList<>();
        ModuleConfigVO shopTagModule = createModuleConfigVO(DEAL_SHOP_TAG_MODULE_KEY);
        data.add(Collections.singletonList(shopTagModule));
        TabModuleConfigVO tabModule = createTabModule();
        data.add(Collections.singletonList(tabModule));
        pageConfig.setData(data);
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 2, pageConfig.getData().size());
        assertEquals("First module should be shop tag", DEAL_SHOP_TAG_MODULE_KEY, pageConfig.getData().get(0).get(0).getModuleKey());
        TabModuleConfigVO resultModule = (TabModuleConfigVO) pageConfig.getData().get(1).get(0);
        assertNotNull("Tab module should exist", resultModule);
        assertFalse("Tab configs should not be empty", resultModule.getTabConfigs().isEmpty());
        assertFalse("Module list should not be empty", resultModule.getTabConfigs().get(0).getModuleList().isEmpty());
    }

    /**
     * Test when exception occurs during execution
     */
    @Test
    public void testExecuteWhenExceptionOccurs() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DEAL
        request.setProductType(1);
        request.setPageSource("test-source");
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> data = new ArrayList<>();
        // This will cause NPE during processing
        data.add(null);
        pageConfig.setData(data);
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 1, pageConfig.getData().size());
        assertNull("Null element should remain null", pageConfig.getData().get(0));
    }

    /**
     * Test when request is null
     */
    @Test
    public void testExecuteWhenRequestIsNull() throws Throwable {
        // arrange
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        List<List<ModuleConfigVO>> data = new ArrayList<>();
        TabModuleConfigVO tabModule = createTabModule();
        data.add(Collections.singletonList(tabModule));
        pageConfig.setData(data);
        // act
        service.execute(pageConfig, null);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertEquals("Data size should remain unchanged", 1, pageConfig.getData().size());
        TabModuleConfigVO resultModule = (TabModuleConfigVO) pageConfig.getData().get(0).get(0);
        assertNotNull("Tab module should exist", resultModule);
        assertFalse("Tab configs should not be empty", resultModule.getTabConfigs().isEmpty());
        assertFalse("Module list should not be empty", resultModule.getTabConfigs().get(0).getModuleList().isEmpty());
    }

    /**
     * Test when page config data list is empty
     */
    @Test
    public void testExecuteWhenPageConfigDataListEmpty() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // DEAL
        request.setProductType(1);
        request.setPageSource("test-source");
        PageConfigDataVO pageConfig = new PageConfigDataVO();
        pageConfig.setData(new ArrayList<>());
        // act
        service.execute(pageConfig, request);
        // assert
        assertNotNull("Data should not be null", pageConfig.getData());
        assertTrue("Data should remain empty", pageConfig.getData().isEmpty());
    }
}
