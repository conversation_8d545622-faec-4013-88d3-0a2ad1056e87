package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BaseFetcherContextGetDependencyResultTest {

    @Spy
    private TestBaseFetcherContext testBaseFetcherContext;

    /**
     * Sets up the private fetcherContextMap field using reflection
     */
    private void setUpFetcherContextMap(BaseFetcherContext context, Map<String, BaseFetcherContext> contextMap) throws Exception {
        Field contextMapField = BaseFetcherContext.class.getDeclaredField("fetcherContextMap");
        contextMapField.setAccessible(true);
        contextMapField.set(context, contextMap);
    }

    /**
     * Test successful case where dependency response returns success with valid data
     */
    @Test
    public void testGetDependencyResultWhenSuccessful() throws Throwable {
        // arrange
        TestFetcherContext dependencyFetcher = new TestFetcherContext();
        TestReturnValueDTO expectedResult = new TestReturnValueDTO();
        FetcherResponse<FetcherReturnValueDTO> successResponse = new FetcherResponse<>();
        successResponse.setSuccess(true);
        successResponse.setReturnValue(expectedResult);
        DAG dag = mock(DAG.class);
        FetcherDefinition fetcherDefinition = mock(FetcherDefinition.class);
        Map<String, BaseFetcherContext> contextMap = new HashMap<>();
        contextMap.put("DependencyFetcher", dependencyFetcher);
        // Set up private fields
        setUpFetcherContextMap(testBaseFetcherContext, contextMap);
        testBaseFetcherContext.dag = dag;
        testBaseFetcherContext.fetcherDefinition = fetcherDefinition;
        doReturn(successResponse).when(testBaseFetcherContext).getDependencyResponse(any());
        // act
        TestReturnValueDTO result = testBaseFetcherContext.getDependencyResult(TestFetcherContext.class);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case where dependency response returns failure
     */
    @Test
    public void testGetDependencyResultWhenResponseFailed() throws Throwable {
        // arrange
        FetcherResponse<FetcherReturnValueDTO> failureResponse = new FetcherResponse<>();
        failureResponse.setSuccess(false);
        DAG dag = mock(DAG.class);
        FetcherDefinition fetcherDefinition = mock(FetcherDefinition.class);
        // Set up private fields
        setUpFetcherContextMap(testBaseFetcherContext, new HashMap<>());
        testBaseFetcherContext.dag = dag;
        testBaseFetcherContext.fetcherDefinition = fetcherDefinition;
        doReturn(failureResponse).when(testBaseFetcherContext).getDependencyResponse(any());
        // act
        TestReturnValueDTO result = testBaseFetcherContext.getDependencyResult(TestFetcherContext.class);
        // assert
        assertNull(result);
    }

    /**
     * Test case where getDependencyResponse throws FetcherFatalException
     */
    @Test
    public void testGetDependencyResultWhenFetcherFatalExceptionThrown() throws Throwable {
        // arrange
        DAG dag = mock(DAG.class);
        FetcherDefinition fetcherDefinition = mock(FetcherDefinition.class);
        when(fetcherDefinition.getFetcherName()).thenReturn("TestFetcher");
        when(dag.isAncestor(anyString(), anyString())).thenReturn(false);
        // Set up private fields
        setUpFetcherContextMap(testBaseFetcherContext, new HashMap<>());
        testBaseFetcherContext.dag = dag;
        testBaseFetcherContext.fetcherDefinition = fetcherDefinition;
        // act & assert
        assertThrows(FetcherFatalException.class, () -> testBaseFetcherContext.getDependencyResult(TestFetcherContext.class));
    }

    // Test helper classes
    private static class TestBaseFetcherContext extends BaseFetcherContext<TestReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    private static class TestFetcherContext extends BaseFetcherContext<TestReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    private static class TestReturnValueDTO extends FetcherReturnValueDTO {
    }
}
