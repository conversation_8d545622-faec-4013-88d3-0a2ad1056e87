package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.AggregateFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.ComponentFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.Getter;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/2/7 09:44
 */
@Getter
@SuppressWarnings("rawtypes")
public enum FetcherTypeEnum {

    //一个Fetcher对应一个外部服务，最常见的用法
    NORMAL_FETCHER("普通Fetcher",
            NormalFetcherContext.class,
            null
    ),
    //实现Fetcher内部的按需索取，如果外部服务支持按需索取可以用这套Fetcher架构
    AGGREGATE_FETCHER("聚合Fetcher，为组件Fetcher提供future，但是不执行get",
            AggregateFetcherContext.class,
            AggregateFetcherDefinition.class
    ),
    COMPONENT_FETCHER("组件Fetcher，为聚合Fetcher构建request，但不提供future，只执行get",
            ComponentFetcherContext.class,
            ComponentFetcherDefinition.class
    );

    private final String desc;
    /**
     * fetcherContext，多例bean，作为编排中上下文使用
     */
    private final Class<? extends BaseFetcherContext> extendBaseFetcherClass;
    /**
     * fetcherDefinition扩展信息
     */
    private final Class<? extends FetcherDefinition> fetcherDefinitionClass;

    FetcherTypeEnum(final String desc,
                    final Class<? extends BaseFetcherContext> extendBaseFetcherClass,
                    final Class<? extends FetcherDefinition> fetcherDefinitionClass) {
        this.desc = desc;
        this.extendBaseFetcherClass = extendBaseFetcherClass;
        this.fetcherDefinitionClass = fetcherDefinitionClass;
    }

}