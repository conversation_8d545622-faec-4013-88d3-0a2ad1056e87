package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.tab;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleItemVO;
import com.sankuai.dz.product.detail.metadata.sdk.error.LayoutConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TabModuleConfigDTOBuildVOTest {

    /**
     * Tests that buildVO throws exception when depth exceeds maximum allowed
     */
    @Test
    public void testBuildVO_ThrowsExceptionWhenDepthExceedsLimit() throws Throwable {
        // arrange
        TabModuleConfigDTO dto = new TabModuleConfigDTO();
        // 初始化空列表避免NPE
        dto.setTabItemList(new ArrayList<>());
        BuildModuleVORequest request = new BuildModuleVORequest();
        int excessiveDepth = 10;
        // act & assert
        assertThrows(LayoutConfigFatalException.class, () -> dto.buildVO(request, excessiveDepth));
    }

    /**
     * Tests successful VO construction with valid depth and populated tab items
     */
    @Test
    public void testBuildVO_SuccessWithValidDepthAndTabItems() throws Throwable {
        // arrange
        TabModuleConfigDTO dto = new TabModuleConfigDTO();
        dto.setModuleKey("testKey");
        dto.setFirstScreenFlag(1);
        TabModuleItemDTO mockItem = mock(TabModuleItemDTO.class);
        TabModuleItemVO mockItemVO = mock(TabModuleItemVO.class);
        when(mockItem.buildVO(any(), anyInt())).thenReturn(mockItemVO);
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        tabItems.add(mockItem);
        dto.setTabItemList(tabItems);
        BuildModuleVORequest request = new BuildModuleVORequest();
        int validDepth = 5;
        // act
        ModuleConfigVO resultVO = dto.buildVO(request, validDepth);
        // assert
        assertTrue(resultVO instanceof TabModuleConfigVO);
        TabModuleConfigVO result = (TabModuleConfigVO) resultVO;
        assertEquals("testKey", result.getModuleKey());
        assertEquals(1, result.getFirstScreenFlag());
        assertEquals("tab", result.getModuleType());
        assertNotNull(result.getTabConfigs());
        assertEquals(1, result.getTabConfigs().size());
        verify(mockItem).buildVO(request, validDepth + 1);
    }

    /**
     * Tests successful VO construction with empty tab items list
     */
    @Test
    public void testBuildVO_SuccessWithEmptyTabItems() throws Throwable {
        // arrange
        TabModuleConfigDTO dto = new TabModuleConfigDTO();
        dto.setModuleKey("testKey");
        dto.setFirstScreenFlag(0);
        dto.setTabItemList(Collections.emptyList());
        BuildModuleVORequest request = new BuildModuleVORequest();
        int validDepth = 0;
        // act
        ModuleConfigVO resultVO = dto.buildVO(request, validDepth);
        // assert
        assertTrue(resultVO instanceof TabModuleConfigVO);
        TabModuleConfigVO result = (TabModuleConfigVO) resultVO;
        assertEquals("testKey", result.getModuleKey());
        assertEquals(0, result.getFirstScreenFlag());
        assertEquals("tab", result.getModuleType());
        assertNotNull(result.getTabConfigs());
        assertTrue(result.getTabConfigs().isEmpty());
    }

    /**
     * Tests that buildVO throws NullPointerException when tabItemList is null
     */
    @Test
    public void testBuildVO_ThrowsExceptionWithNullTabItems() throws Throwable {
        // arrange
        TabModuleConfigDTO dto = new TabModuleConfigDTO();
        dto.setModuleKey("testKey");
        dto.setFirstScreenFlag(1);
        dto.setTabItemList(null);
        BuildModuleVORequest request = new BuildModuleVORequest();
        int validDepth = 1;
        // act & assert
        assertThrows(NullPointerException.class, () -> dto.buildVO(request, validDepth));
    }

    /**
     * Tests that depth is properly propagated to child items
     */
    @Test
    public void testBuildVO_DepthPropagationToChildItems() throws Throwable {
        // arrange
        TabModuleConfigDTO dto = new TabModuleConfigDTO();
        List<TabModuleItemDTO> tabItems = new ArrayList<>();
        TabModuleItemDTO mockItem1 = mock(TabModuleItemDTO.class);
        TabModuleItemDTO mockItem2 = mock(TabModuleItemDTO.class);
        tabItems.add(mockItem1);
        tabItems.add(mockItem2);
        dto.setTabItemList(tabItems);
        BuildModuleVORequest request = new BuildModuleVORequest();
        int initialDepth = 3;
        // act
        dto.buildVO(request, initialDepth);
        // assert
        verify(mockItem1).buildVO(request, initialDepth + 1);
        verify(mockItem2).buildVO(request, initialDepth + 1);
    }
}
