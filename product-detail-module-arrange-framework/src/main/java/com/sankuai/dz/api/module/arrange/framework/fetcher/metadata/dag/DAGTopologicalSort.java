package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/2/8 13:31
 */
public class DAGTopologicalSort {

    /**
     * DAG拓扑排序，用Kahn算法
     */
    private static List<String> topologicalSort(final Map<String, Set<String>> childrenGraph,
                                                final Map<String, Set<String>> parentGraph,
                                                final String start) {
        // 验证起始节点是否存在
        if (!parentGraph.containsKey(start) || !childrenGraph.containsKey(start)) {
            throw new FetcherDAGException("DAG中没有起始点");
        }

        // 初始化入度表
        Map<String, Integer> inDegreeMap = DAGBFSUtils.initInDegreeMap(parentGraph);

        //初始化队列（入度为0的节点的队列），用于BFS循环
        Queue<String> queue = DAGBFSUtils.initQueue(inDegreeMap, start);

        //开始BFS循环获取所有节点的拓扑排序
        List<String> topologicalSort = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            topologicalSort.add(node);

            // 更新子节点入度
            for (String child : childrenGraph.getOrDefault(node, new HashSet<>())) {
                inDegreeMap.put(child, inDegreeMap.get(child) - 1);
                if (inDegreeMap.get(child) == 0) {
                    queue.add(child);
                }
            }
        }
        return topologicalSort;
    }

}
