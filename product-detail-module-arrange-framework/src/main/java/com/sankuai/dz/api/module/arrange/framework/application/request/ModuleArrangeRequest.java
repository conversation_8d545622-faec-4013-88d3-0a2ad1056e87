package com.sankuai.dz.api.module.arrange.framework.application.request;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.Getter;

import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/4 13:48
 */
@Getter
public class ModuleArrangeRequest {

    /**
     * 公共参数
     * tips：非只读，非全局变量，随意修改只会影响单次请求结果
     */
    private final ProductDetailPageRequest request;

    /**
     * 需要返回的模块的moduleKeys
     */
    private final Set<String> moduleKeys;

    public ModuleArrangeRequest(final ProductDetailPageRequest request,
                                final Set<String> moduleKeys) {
        this.request = request;
        this.moduleKeys = moduleKeys;
    }

}
