package com.sankuai.dz.product.detail.metadata.sdk.model.layout.lion.config;

import com.dianping.pigeon.util.CollectionUtils;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.metadata.sdk.utils.json.JacksonUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * @Author: guangyujie
 * @Date: 2025/5/16 14:27
 */
public class OldProductDetail_FirstCategory_1_12 extends BaseConfigTool {

    @Test
    public void testConfig() {
        PageConfigTemplateDTO pageConfigTemplateDTO = new PageConfigTemplateDTO();
        PageConfigDataDTO mainConfigData = new PageConfigDataDTO();
        pageConfigTemplateDTO.setMainConfigData(mainConfigData);
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "商品基础信息模块组",
                buildNormalModuleConfigDTO(
                        "module_study_before_pay_module", "DealTrade", 1
                )//须知条和须知浮层
        ));
        System.out.println(JacksonUtils.serialize(pageConfigTemplateDTO));
        Assert.assertTrue(pageConfigTemplateDTO.getMainConfigData() != null && !CollectionUtils.isEmpty(pageConfigTemplateDTO.getMainConfigData().getModuleGroupConfigs()));
    }


}
