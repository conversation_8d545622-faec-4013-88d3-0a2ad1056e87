package com.sankuai.dz.product.detail.gateway.application.service;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.application.exception.ProductDetailEntranceFatalException;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.DataMergeMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.degrade.PageDegradeMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.message.MessageSendFunction;
import com.sankuai.dz.product.detail.gateway.domain.page.config.PageConfigMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.param.transformer.RequestTransformer;
import com.sankuai.dz.product.detail.gateway.domain.post.AntiCrawlerHandler;
import com.sankuai.dz.product.detail.gateway.domain.spi.call.SPICallMainFunction;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailNotLoginException;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductDetailPageUnifiedServiceImpl 边界情况和异常场景测试
 * 专门测试各种边界条件和异常情况，提高代码覆盖率
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 17:30
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageUnifiedServiceImplEdgeCaseTest {

    @InjectMocks
    private ProductDetailPageUnifiedServiceImpl service;

    @Mock
    private PageConfigMainFunction pageConfigMainFunction;
    @Mock
    private SPICallMainFunction spiCallMainFunction;
    @Mock
    private DataMergeMainFunction dataMergeMainFunction;
    @Mock
    private RequestTransformer requestTransformer;
    @Mock
    private PageDegradeMainFunction pageDegradeMainFunction;
    @Mock
    private AntiCrawlerHandler antiCrawlerHandler;
    @Mock
    private MessageSendFunction messageSendFunction;

    @Before
    public void setUp() {
        // 重置所有 mock 对象
        reset(pageConfigMainFunction, spiCallMainFunction, dataMergeMainFunction,
                requestTransformer, pageDegradeMainFunction, antiCrawlerHandler, messageSendFunction);
    }

    /**
     * 测试使用测试辅助工具类创建的各种请求
     */
    @Test
    public void testQueryWithHelperCreatedRequests() {
        // 模拟所有处理函数正常执行
        setupNormalProcessing();

        // 测试 MT 请求
        UnifiedPageRequest mtRequest = ProductDetailPageUnifiedServiceTestHelper.createValidMTRequest();
        ProductDetailPageMergeResponse mtResult = service.query(mtRequest);
        assertNull("MT请求结果不应该为空", mtResult);
        assertTrue("MT请求应该是有效请求",
                ProductDetailPageUnifiedServiceTestHelper.isValidRequest(mtRequest));

        // 重置 mock 调用计数
        reset(requestTransformer, pageConfigMainFunction, spiCallMainFunction,
                dataMergeMainFunction, pageDegradeMainFunction, antiCrawlerHandler, messageSendFunction);
        setupNormalProcessing();

        // 测试 DP 请求
        UnifiedPageRequest dpRequest = ProductDetailPageUnifiedServiceTestHelper.createValidDPRequest();
        ProductDetailPageMergeResponse dpResult = service.query(dpRequest);
        assertNull("DP请求结果不应该为空", dpResult);
        assertTrue("DP请求应该是有效请求",
                ProductDetailPageUnifiedServiceTestHelper.isValidRequest(dpRequest));

        // 重置 mock 调用计数
        reset(requestTransformer, pageConfigMainFunction, spiCallMainFunction,
                dataMergeMainFunction, pageDegradeMainFunction, antiCrawlerHandler, messageSendFunction);
        setupNormalProcessing();

        // 测试最小有效请求
        UnifiedPageRequest minRequest = ProductDetailPageUnifiedServiceTestHelper.createMinValidRequest();
        ProductDetailPageMergeResponse minResult = service.query(minRequest);
        assertNull("最小有效请求结果不应该为空", minResult);

        // 重置 mock 调用计数
        reset(requestTransformer, pageConfigMainFunction, spiCallMainFunction,
                dataMergeMainFunction, pageDegradeMainFunction, antiCrawlerHandler, messageSendFunction);
        setupNormalProcessing();

        // 测试最大有效请求
        UnifiedPageRequest maxRequest = ProductDetailPageUnifiedServiceTestHelper.createMaxValidRequest();
        ProductDetailPageMergeResponse maxResult = service.query(maxRequest);
        assertNull("最大有效请求结果不应该为空", maxResult);
    }

    /**
     * 测试特殊字符请求
     */
    @Test
    public void testQueryWithSpecialCharacterRequest() {
        setupNormalProcessing();

        UnifiedPageRequest specialRequest = ProductDetailPageUnifiedServiceTestHelper.createSpecialCharacterRequest();
        ProductDetailPageMergeResponse result = service.query(specialRequest);

        assertNull("特殊字符请求结果不应该为空", result);
        verifyAllProcessingCalled();
    }

    /**
     * 测试空字符串字段请求
     */
    @Test
    public void testQueryWithEmptyStringFieldsRequest() {
        setupNormalProcessing();

        UnifiedPageRequest emptyFieldsRequest = ProductDetailPageUnifiedServiceTestHelper.createEmptyStringFieldsRequest();
        ProductDetailPageMergeResponse result = service.query(emptyFieldsRequest);

        assertNull("空字符串字段请求结果不应该为空", result);
        verifyAllProcessingCalled();
    }

    /**
     * 测试 ProductDetailNotLoginException 的不同构造函数
     */
    @Test
    public void testQueryWithDifferentNotLoginExceptionConstructors() {
        // 测试只有消息的构造函数
        doThrow(new ProductDetailNotLoginException("用户未登录"))
                .when(requestTransformer).process(any(GatewayContext.class));

        UnifiedPageRequest request = ProductDetailPageUnifiedServiceTestHelper.createValidMTRequest();
        ProductDetailPageMergeResponse result = service.query(request);

        assertTrue("应该返回未授权响应",
                ProductDetailPageUnifiedServiceTestHelper.isUnauthorizedResponse(result));

        // 重置并测试带原因的构造函数
        reset(requestTransformer);
        doThrow(new ProductDetailNotLoginException("用户未登录", new RuntimeException("底层原因")))
                .when(requestTransformer).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回未授权响应",
                ProductDetailPageUnifiedServiceTestHelper.isUnauthorizedResponse(result));

        // 重置并测试只有原因的构造函数
        reset(requestTransformer);
        doThrow(new ProductDetailNotLoginException(new RuntimeException("底层原因")))
                .when(requestTransformer).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回未授权响应",
                ProductDetailPageUnifiedServiceTestHelper.isUnauthorizedResponse(result));
    }

    /**
     * 测试 ProductDetailRequestIllegalException 的不同构造函数
     */
    @Test
    public void testQueryWithDifferentRequestIllegalExceptionConstructors() {
        UnifiedPageRequest request = ProductDetailPageUnifiedServiceTestHelper.createValidMTRequest();

        // 测试只有消息的构造函数
        doThrow(new ProductDetailRequestIllegalException("请求参数非法"))
                .when(requestTransformer).process(any(GatewayContext.class));

        ProductDetailPageMergeResponse result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));

        // 重置并测试带原因的构造函数
        reset(requestTransformer);
        doThrow(new ProductDetailRequestIllegalException("请求参数非法", new IllegalArgumentException("参数错误")))
                .when(requestTransformer).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));

        // 重置并测试只有原因的构造函数
        reset(requestTransformer);
        doThrow(new ProductDetailRequestIllegalException(new IllegalArgumentException("参数错误")))
                .when(requestTransformer).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));
    }

    /**
     * 测试 ProductDetailEntranceFatalException 的不同构造函数
     */
    @Test
    public void testQueryWithDifferentEntranceFatalExceptionConstructors() {
        UnifiedPageRequest request = ProductDetailPageUnifiedServiceTestHelper.createValidMTRequest();

        // 测试只有消息的构造函数
        doThrow(new ProductDetailEntranceFatalException("入口致命错误"))
                .when(pageConfigMainFunction).process(any(GatewayContext.class));

        ProductDetailPageMergeResponse result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));

        // 重置并测试带原因的构造函数
        reset(pageConfigMainFunction);
        doThrow(new ProductDetailEntranceFatalException("入口致命错误", new RuntimeException("底层错误")))
                .when(pageConfigMainFunction).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));

        // 重置并测试只有原因的构造函数
        reset(pageConfigMainFunction);
        doThrow(new ProductDetailEntranceFatalException(new RuntimeException("底层错误")))
                .when(pageConfigMainFunction).process(any(GatewayContext.class));

        result = service.query(request);
        assertTrue("应该返回失败响应",
                ProductDetailPageUnifiedServiceTestHelper.isFailureResponse(result));
    }

    /**
     * 测试成功场景中 GatewayContext 返回 null 的情况
     */
    @Test
    public void testQuerySuccessWithNullPageResponse() {
        // 模拟所有处理函数正常执行，但不设置 pageResponse
        doNothing().when(requestTransformer).process(any(GatewayContext.class));
        doNothing().when(pageConfigMainFunction).process(any(GatewayContext.class));
        doNothing().when(spiCallMainFunction).process(any(GatewayContext.class));
        doNothing().when(dataMergeMainFunction).process(any(GatewayContext.class));
        doNothing().when(pageDegradeMainFunction).process(any(GatewayContext.class));
        doNothing().when(antiCrawlerHandler).process(any(GatewayContext.class));
        doNothing().when(messageSendFunction).process(any(GatewayContext.class));

        UnifiedPageRequest request = ProductDetailPageUnifiedServiceTestHelper.createValidMTRequest();
        ProductDetailPageMergeResponse result = service.query(request);

        // 当 GatewayContext.getPageResponse() 返回 null 时，service 应该返回 null
        assertNull("当 GatewayContext 没有设置 pageResponse 时，应该返回 null", result);

        verifyAllProcessingCalled();
    }

    /**
     * 设置正常处理流程的模拟
     */
    private void setupNormalProcessing() {
        doNothing().when(requestTransformer).process(any(GatewayContext.class));
        doNothing().when(pageConfigMainFunction).process(any(GatewayContext.class));
        doNothing().when(spiCallMainFunction).process(any(GatewayContext.class));
        doNothing().when(dataMergeMainFunction).process(any(GatewayContext.class));
        doNothing().when(pageDegradeMainFunction).process(any(GatewayContext.class));
        doNothing().when(antiCrawlerHandler).process(any(GatewayContext.class));
        doNothing().when(messageSendFunction).process(any(GatewayContext.class));
    }

    /**
     * 验证所有处理函数都被调用
     */
    private void verifyAllProcessingCalled() {
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, times(1)).process(any(GatewayContext.class));
        verify(messageSendFunction, times(1)).process(any(GatewayContext.class));
    }
}
