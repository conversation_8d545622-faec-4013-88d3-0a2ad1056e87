package com.sankuai.dz.api.module.arrange.framework.component.storage;

import com.sankuai.dz.api.module.arrange.framework.component.definition.ComponentDefinition;
import com.sankuai.dz.api.module.arrange.framework.component.exception.ComponentFatalException;
import com.sankuai.dz.api.module.arrange.framework.component.spi.BaseComponent;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ComponentDefinitionStorageTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private ComponentDefinition componentDefinition;

    // 创建具体的测试组件类
    static class TestComponent extends BaseComponent {

        @Override
        protected void doInit(final RunnerContext runnerContext) {
        }

        @Override
        protected boolean containsDependencyFetcher(String dependencyFetcherName) {
            return false;
        }
    }

    private void setApplicationContext(ApplicationContext context) throws Exception {
        Field field = ComponentDefinitionStorage.class.getDeclaredField("applicationContext");
        field.setAccessible(true);
        field.set(null, context);
    }

    @AfterEach
    void tearDown() throws Exception {
        setApplicationContext(null);
    }

    /**
     * Test successful retrieval of component bean from application context
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetComponentBeanSuccess() throws Throwable {
        // arrange
        TestComponent testComponent = new TestComponent();
        Class<TestComponent> componentClass = TestComponent.class;
        when(componentDefinition.getComponetClass()).thenReturn((Class) componentClass);
        when(applicationContext.getBean(componentClass)).thenReturn(testComponent);
        setApplicationContext(applicationContext);
        // act
        BaseComponent result = ComponentDefinitionStorage.getComponentBean(componentDefinition);
        // assert
        assertNotNull(result);
        assertEquals(testComponent, result);
        verify(componentDefinition).getComponetClass();
        verify(applicationContext).getBean(componentClass);
    }

    /**
     * Test when application context is null
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetComponentBeanApplicationContextNull() throws Throwable {
        // arrange
        Class<TestComponent> componentClass = TestComponent.class;
        when(componentDefinition.getComponetClass()).thenReturn((Class) componentClass);
        setApplicationContext(null);
        // act & assert
        ComponentFatalException exception = assertThrows(ComponentFatalException.class, () -> ComponentDefinitionStorage.getComponentBean(componentDefinition));
        assertEquals("运行时Fatal Error!!! getComponentBean 失败!!!", exception.getMessage());
        assertNotNull(exception.getCause());
        verify(componentDefinition).getComponetClass();
    }

    /**
     * Test when bean not found in application context
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetComponentBeanBeanNotFound() throws Throwable {
        // arrange
        Class<TestComponent> componentClass = TestComponent.class;
        when(componentDefinition.getComponetClass()).thenReturn((Class) componentClass);
        when(applicationContext.getBean(componentClass)).thenThrow(new RuntimeException("Bean not found"));
        setApplicationContext(applicationContext);
        // act & assert
        ComponentFatalException exception = assertThrows(ComponentFatalException.class, () -> ComponentDefinitionStorage.getComponentBean(componentDefinition));
        assertEquals("运行时Fatal Error!!! getComponentBean 失败!!!", exception.getMessage());
        assertNotNull(exception.getCause());
        verify(componentDefinition).getComponetClass();
        verify(applicationContext).getBean(componentClass);
    }

    /**
     * Test when componentDefinition is null
     */
    @Test
    public void testGetComponentBeanComponentDefinitionNull() throws Throwable {
        // arrange
        setApplicationContext(applicationContext);
        // act & assert
        ComponentFatalException exception = assertThrows(ComponentFatalException.class, () -> ComponentDefinitionStorage.getComponentBean(null));
        assertEquals("运行时Fatal Error!!! getComponentBean 失败!!!", exception.getMessage());
        assertNotNull(exception.getCause());
    }

    /**
     * Test when componentDefinition.getComponetClass() throws exception
     */
    @Test
    public void testGetComponentBeanGetClassThrowsException() throws Throwable {
        // arrange
        when(componentDefinition.getComponetClass()).thenThrow(new RuntimeException("Class loading error"));
        setApplicationContext(applicationContext);
        // act & assert
        ComponentFatalException exception = assertThrows(ComponentFatalException.class, () -> ComponentDefinitionStorage.getComponentBean(componentDefinition));
        assertEquals("运行时Fatal Error!!! getComponentBean 失败!!!", exception.getMessage());
        assertNotNull(exception.getCause());
        verify(componentDefinition).getComponetClass();
    }
}
