package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.BuilderFactoryDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;

/**
 * @Author: guangyujie
 * @Date: 2025/3/7 09:01
 */
@SuppressWarnings("rawtypes")
public abstract class BaseBuilderFactory<T extends AbstractModuleVO> extends AbstractBuilder<T, BuilderFactoryDefinition> {

    @Override
    protected boolean isBuilderSupportedForLowCode() {
        return true;
    }

    /**
     * 执行Builder逻辑
     */
    @SuppressWarnings("unchecked")
    public T doBuild() {
        BuilderDefinition variableBuilderDefinition = getVariableBuilderDefinition();
        BaseVariableBuilder baseVariableBuilder = BuilderDefinitionStorage.getBuilderBean(variableBuilderDefinition);
        if (baseVariableBuilder == null) {
            throw new IllegalStateException("获取泛型Builder失败，具体原因参考BuilderFatalError");
        }
        return (T) baseVariableBuilder.doBuild();
    }

    /**
     * 工厂方法
     */
    public BuilderDefinition getVariableBuilderDefinition() {
        Class<? extends BaseVariableBuilder> variableBuilderClass = selectVariableBuilder();
        if (!builderDefinition.getVariableBuilderDefinitions().contains(variableBuilderClass)) {
            throw new BuilderFatalError(String.format(
                    "BuilderFactory(%s)选择的VariableBuilder(%s)不在其定义范围内",
                    builderDefinition.getBuilderClass().getName(), variableBuilderClass.getName()
            ));
        }
        return BuilderDefinitionStorage.builderStorageOfModuleClass.get(variableBuilderClass);
    }

    protected abstract Class<? extends BaseVariableBuilder> selectVariableBuilder();

}
