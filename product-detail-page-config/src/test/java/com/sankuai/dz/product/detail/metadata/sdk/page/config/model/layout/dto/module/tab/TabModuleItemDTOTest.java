package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.tab;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.tab.TabModuleItemVO;
import com.sankuai.dz.product.detail.metadata.sdk.enums.TabItemTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import java.util.ArrayList;
import java.util.Collections;

import org.junit.jupiter.api.Test;

public class TabModuleItemDTOTest {

    /**
     * 测试正常场景 - 所有字段都有值
     */
    @Test
    public void testBuildVONormalCase() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        ModuleConfigDTO moduleConfig = mock(ModuleConfigDTO.class);
        when(groupConfig.getModuleList()).thenReturn(Collections.singletonList(moduleConfig));
        when(moduleConfig.buildVO(any(), anyInt())).thenReturn(mock(ModuleConfigVO.class));
        dto.setGroupConfig(Collections.singletonList(groupConfig));
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        assertEquals("testKey", result.getTabKey());
        assertEquals("testName", result.getTabName());
        assertEquals("normal", result.getTabType());
        assertNotNull(result.getModuleList());
        assertEquals(1, result.getModuleList().size());
    }

    /**
     * 测试groupConfig为null的情况
     */
    @Test
    public void testBuildVOWithNullGroupConfig() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        // 修改为空列表而不是null
        dto.setGroupConfig(new ArrayList<>());
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        assertEquals("testKey", result.getTabKey());
        assertEquals("testName", result.getTabName());
        assertEquals("normal", result.getTabType());
        assertNotNull(result.getModuleList());
        assertTrue(result.getModuleList().isEmpty());
    }

    /**
     * 测试groupConfig为空列表的情况
     */
    @Test
    public void testBuildVOWithEmptyGroupConfig() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        dto.setGroupConfig(new ArrayList<>());
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        assertEquals("testKey", result.getTabKey());
        assertEquals("testName", result.getTabName());
        assertEquals("normal", result.getTabType());
        assertNotNull(result.getModuleList());
        assertTrue(result.getModuleList().isEmpty());
    }

    /**
     * 测试groupConfig中的moduleList为null的情况
     */
    @Test
    public void testBuildVOWithNullModuleListInGroupConfig() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        // 修改为返回空列表而不是null
        when(groupConfig.getModuleList()).thenReturn(new ArrayList<>());
        dto.setGroupConfig(Collections.singletonList(groupConfig));
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        assertEquals("testKey", result.getTabKey());
        assertEquals("testName", result.getTabName());
        assertEquals("normal", result.getTabType());
        assertNotNull(result.getModuleList());
        assertEquals(1, result.getModuleList().size());
        assertTrue(result.getModuleList().get(0).isEmpty());
    }

    /**
     * 测试groupConfig中的moduleList为空列表的情况
     */
    @Test
    public void testBuildVOWithEmptyModuleListInGroupConfig() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        when(groupConfig.getModuleList()).thenReturn(new ArrayList<>());
        dto.setGroupConfig(Collections.singletonList(groupConfig));
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        assertEquals("testKey", result.getTabKey());
        assertEquals("testName", result.getTabName());
        assertEquals("normal", result.getTabType());
        assertNotNull(result.getModuleList());
        assertEquals(1, result.getModuleList().size());
        assertTrue(result.getModuleList().get(0).isEmpty());
    }

    /**
     * 测试tabType为null的情况
     */
    @Test
    public void testBuildVOWithNullTabType() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(null);
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act & assert
        assertThrows(NullPointerException.class, () -> dto.buildVO(request, depth));
    }

    /**
     * 测试递归深度是否正确传递
     */
    @Test
    public void testBuildVORecursiveDepth() throws Throwable {
        // arrange
        TabModuleItemDTO dto = new TabModuleItemDTO();
        dto.setTabKey("testKey");
        dto.setTabName("testName");
        dto.setTabType(TabItemTypeEnum.normal);
        ModuleGroupConfigDTO groupConfig = mock(ModuleGroupConfigDTO.class);
        ModuleConfigDTO moduleConfig = mock(ModuleConfigDTO.class);
        when(groupConfig.getModuleList()).thenReturn(Collections.singletonList(moduleConfig));
        when(moduleConfig.buildVO(any(), eq(1))).thenReturn(mock(ModuleConfigVO.class));
        dto.setGroupConfig(Collections.singletonList(groupConfig));
        BuildModuleVORequest request = new BuildModuleVORequest();
        int depth = 0;
        // act
        TabModuleItemVO result = dto.buildVO(request, depth);
        // assert
        assertNotNull(result);
        verify(moduleConfig).buildVO(request, 1);
    }
}
