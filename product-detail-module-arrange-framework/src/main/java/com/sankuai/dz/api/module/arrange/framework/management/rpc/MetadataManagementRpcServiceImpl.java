package com.sankuai.dz.api.module.arrange.framework.management.rpc;

import com.alibaba.fastjson.JSON;
import com.dianping.pigeon.remoting.ServiceFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.management.constant.MetadataManagementConstant;
import com.sankuai.dz.api.module.arrange.framework.management.utils.DagToDotConverter;

import java.util.*;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/2/25 13:44
 */
public class MetadataManagementRpcServiceImpl implements MetadataManagementRpcService {

    @Override
    public String getAllFetcherDefinition() {
        Collection<FetcherDefinition> allFetcherDefinitions = FetcherDefinitionStorage.getAllFetcherDefinition();
        return JSON.toJSONString(allFetcherDefinitions);
    }

    @Override
    public String getAllBuilderDefinition() {
        List<BuilderDefinition> allBuilderDefinitions = BuilderDefinitionStorage.builderStorageOfModuleKey.getAllValue();
        return JSON.toJSONString(allBuilderDefinitions);
    }

    @Override
    public String getAllDotGraphByBuilder(String moduleKey) {
        return getAllDotGraphByBuilder(moduleKey, false);
    }

    @Override
    public String getAllDotGraphByBuilder(String moduleKey, boolean needBuilderLine) {
        BuilderDefinition builderDefinition = BuilderDefinitionStorage.builderStorageOfModuleKey.get(moduleKey);
        if (builderDefinition == null) {
            return "找不到该builder";
        }
        return getDAG(builderDefinition, needBuilderLine);
    }

    private static String getDAG(final BuilderDefinition builderDefinition,
                                 final boolean needBuilderLine) {
        String startFetcherName = builderDefinition.getStartFetcherDefinition().getFetcherName();
        DAG dag = FetcherDAGStorage.getDAG(startFetcherName);
        //找到该模块依赖的所有fetcher，包括直接依赖fetcher和fetcher的祖先
        Set<String> allDependentFetcherNames = new HashSet<>();
        for (String dependentFetcherName : builderDefinition.getDependentFetcherNames()) {
            allDependentFetcherNames.add(dependentFetcherName);
            allDependentFetcherNames.addAll(dag.getAncestors(dependentFetcherName));
        }
        //遍历DAG，过滤所有不依赖的fetcher
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        for (Map.Entry<String, Set<String>> entry : dag.getChildrenGraph().entrySet()) {
            if (adjacencyMatrix.containsKey(entry.getKey())) {
                continue;
            }
            if (!allDependentFetcherNames.contains(entry.getKey())) {
                continue;
            }
            Set<String> children = new HashSet<>();
            for (String child : entry.getValue()) {
                if (allDependentFetcherNames.contains(child)) {
                    children.add(child);
                }
            }
            adjacencyMatrix.put(entry.getKey(), children);
        }
        //对直接依赖fetcher连线到builder（可选）
        if (needBuilderLine) {
            for (String dependentFetcherName : builderDefinition.getDependentFetcherNames()) {
                adjacencyMatrix.get(dependentFetcherName).add(builderDefinition.getModuleKey());
            }
        }
        return DagToDotConverter.generateDot(adjacencyMatrix);
    }

    public static void registerRpcService() {
        ServiceFactory.addService(
                MetadataManagementConstant.buildSPIRpcUrl("management"),
                MetadataManagementRpcService.class,
                new MetadataManagementRpcServiceImpl()
        );
    }

}
