# 低代码配置一致性校验

## 概述

本文档介绍了低代码框架中配置一致性校验的实现，确保 `StructComponentDisplayConfigBO` 的 `fieldConfigs` 递归结构与对应 `ComponentDefinition` 的 `classInfo` 递归结构保持一致。

## 核心功能

### 1. getAllDataSource 方法

在 `ModuleDisplayConfigBO` 类中实现了静态方法 `getAllDataSource`，用于获取组件配置列表中的所有数据源：

```java
public static Map<String, DataSourceConfigBO> getAllDataSource(List<ComponentDisplayConfigBO> componentConfigList)
```

**功能特点：**
- 按 `DataSourceConfigBO.fetcherName` 进行去重
- 合并相同 `fetcherName` 的 `optionalFieldName` 字段
- 返回以 `fetcherName` 为 key 的 Map

### 2. 配置一致性校验

#### ConfigConsistencyValidator

核心校验类，提供以下功能：

- **validateStructComponentConfig**: 校验单个结构化组件配置
- **validateFieldStructure**: 递归校验字段结构一致性
- **validateFieldType**: 校验字段类型一致性
- **validatePojoField**: 校验 POJO 类型字段
- **validateBaseTypeField**: 校验基本类型字段

#### StartupConfigValidator

启动时批量校验类：

- **validatePageConfig**: 校验整个页面配置
- **validateModuleConfig**: 校验模块配置
- **validateComponentConfig**: 校验单个组件配置

## 校验规则

### 1. 字段存在性校验
- 配置中的每个字段必须在元数据中存在
- 字段名必须完全匹配

### 2. 类型一致性校验
- 集合类型标记必须一致
- POJO 类型标记必须一致
- 基本类型标记必须一致

### 3. 递归结构校验
- 对于 POJO 类型字段，递归校验其子字段结构
- 检查递归深度，防止无限递归
- 最大递归深度由 `ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH` 控制

### 4. 泛型类型校验
- 对于集合类型，校验泛型参数类型
- 确保集合的元素类型与配置一致

## 集成方式

### 启动时自动校验

在 `LowCodeStarter` 中集成了自动校验逻辑：

```java
@Override
public void afterPropertiesSet() throws Exception {
    // 初始化组件元数据
    ComponentDefinitionStorage.init(basePackages);
    
    // 拉取所有配置
    DisplayConfigStorage.init();
    
    // 校验配置与元数据的正确性
    validateConfigConsistency();
}
```

### 手动校验

也可以手动调用校验方法：

```java
// 校验单个组件
ConfigConsistencyValidator.validateStructComponentConfig(structConfig);

// 校验模块配置
StartupConfigValidator.validateModuleConfig(moduleConfig);

// 校验页面配置
StartupConfigValidator.validatePageConfig(pageConfig);
```

## 错误处理

### 异常类型
- `LowCodeConfigFatalException`: 配置一致性校验失败时抛出

### 错误信息
校验失败时会提供详细的错误信息，包括：
- 组件 key
- 字段名
- 具体的不一致原因
- 类名和递归深度信息

### 日志记录
- 使用 `@Slf4j` 记录校验过程和结果
- 使用 Cat 监控记录校验耗时和状态

## 性能考虑

### 1. 启动时校验
- 只在项目启动时执行一次完整校验
- 使用 Cat 监控校验耗时

### 2. 递归深度控制
- 限制最大递归深度，防止性能问题
- 提前检测并阻止过深的嵌套结构

### 3. 缓存利用
- 利用已有的元数据缓存 (`DAClassInfoCache`)
- 避免重复的类信息查询

## 使用示例

### 基本使用

```java
// 获取所有数据源
List<ComponentDisplayConfigBO> componentConfigs = getComponentConfigs();
Map<String, DataSourceConfigBO> allDataSources = 
    ModuleDisplayConfigBO.getAllDataSource(componentConfigs);

// 校验配置一致性
StructComponentDisplayConfigBO structConfig = getStructConfig();
ConfigConsistencyValidator.validateStructComponentConfig(structConfig);
```

### 批量校验

```java
// 校验整个页面配置
PageDisplayConfigBO pageConfig = getPageConfig();
StartupConfigValidator.validatePageConfig(pageConfig);
```

## 注意事项

1. **元数据依赖**: 校验前必须确保组件元数据已正确初始化
2. **配置完整性**: 校验的配置必须是完整的，包含所有必要的字段信息
3. **类型匹配**: 配置中的类型标记必须与实际的 Java 类型定义完全一致
4. **递归限制**: 注意递归深度限制，避免设计过深的嵌套结构

## 扩展性

该校验框架设计为可扩展的：

1. **新增校验规则**: 可以在 `ConfigConsistencyValidator` 中添加新的校验方法
2. **自定义校验器**: 可以实现新的校验器类，遵循相同的接口规范
3. **校验结果处理**: 可以扩展 `ValidationResult` 类，添加更多的结果信息

## 故障排查

### 常见错误

1. **字段不存在**: 配置中定义了元数据中不存在的字段
2. **类型不匹配**: 配置中的类型标记与元数据不一致
3. **递归过深**: 组件定义的嵌套层数超过限制
4. **泛型缺失**: 集合类型缺少泛型参数定义

### 排查步骤

1. 检查错误日志中的详细信息
2. 确认组件类的 `@ComponentMetadata` 注解配置
3. 验证字段的类型定义和注解
4. 检查配置文件中的字段定义
