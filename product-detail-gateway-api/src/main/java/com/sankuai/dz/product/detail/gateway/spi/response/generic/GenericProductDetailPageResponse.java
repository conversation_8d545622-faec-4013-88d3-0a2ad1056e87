package com.sankuai.dz.product.detail.gateway.spi.response.generic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 19:06
 */
@Data
@TypeDoc(description = "泛化调用聚合接口返回值")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenericProductDetailPageResponse implements Serializable {

    @FieldDoc(description = "状态码", requiredness = Requiredness.REQUIRED, type = PageResponseCodeEnum.class)
    private int code;

    @FieldDoc(description = "失败原因", rule = "code != MergeResponseCodeEnum.SUCCESS.getCode()才有效", requiredness = Requiredness.OPTIONAL)
    private String msg;

    @FieldDoc(description = "请求耗时", rule = "接口请求成功才会统计耗时", requiredness = Requiredness.OPTIONAL)
    private long duration;

    @FieldDoc(description = "AB结果聚合", requiredness = Requiredness.OPTIONAL)
    private List<ABResultDTO> abResultList;

    @FieldDoc(description = "模块返回值，具体模块是否可用也需要判断", requiredness = Requiredness.REQUIRED)
    private Map<String, GenericModuleResponse> moduleResponse;

    public static GenericProductDetailPageResponse fail(final String failReason) {
        return GenericProductDetailPageResponse.builder().code(PageResponseCodeEnum.FAILURE.getCode()).msg(failReason).build();
    }

}
