package com.sankuai.dz.product.detail.page.config.enums;

import com.sankuai.dz.product.detail.page.config.page.config.merge.handler.*;
import com.sankuai.dz.product.detail.page.config.page.config.model.merge.rule.*;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:06
 */
@Getter
public enum ConfigMergeTypeEnum {

    PAGE_REPLACE("替换模块", PageReplaceHandler.class, PageReplaceRule.class),
    MODULE_COVER("模块覆盖", ModuleCoverHandler.class, ModuleCoverRule.class),
    MODULE_INSERT("模块插入", ModuleInsertHandler.class, ModuleInsertRule.class),
    MODULE_REMOVE("移除模块", ModuleRemoveHandler.class, ModuleRemoveRule.class);

    private final String desc;

    private final Class<? extends ConfigMergeHandler<? extends AbstractMergeRule>> configMergeHandlerClass;

    private final Class<? extends AbstractMergeRule> ruleClass;

    ConfigMergeTypeEnum(final String desc,
                        final Class<? extends ConfigMergeHandler<? extends AbstractMergeRule>> configMergeHandlerClass,
                        final Class<? extends AbstractMergeRule> ruleClass) {
        this.desc = desc;
        this.configMergeHandlerClass = configMergeHandlerClass;
        this.ruleClass = ruleClass;
    }

}