package com.sankuai.dz.product.detail.page.low.code.entity.component.metadata;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeMetadataFatalException;
import org.apache.commons.collections4.CollectionUtils;
import org.reflections.Reflections;
import org.reflections.scanners.TypeAnnotationsScanner;
import org.reflections.util.ConfigurationBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/5/30 11:44
 */
public class ComponentDefinitionStorage {

    private static final Map<String, ComponentDefinition> COMPONENT_KEY_TO_DEFINITION_MAP = new HashMap<>();

    public static ComponentDefinition getComponentMetadata(String componentKey) {
        ComponentDefinition componentDefinition = COMPONENT_KEY_TO_DEFINITION_MAP.get(componentKey);
        if (componentDefinition == null) {
            throw new LowCodeMetadataFatalException("查不到元数据,componentKey=" + componentKey);
        }
        return componentDefinition;
    }

    private static final Map<Class<? extends ComponentBO>, ComponentDefinition> COMPONENT_CLASS_TO_DEFINITION_MAP = new HashMap<>();

    public static ComponentDefinition getComponentMetadata(Class<? extends ComponentBO> componentClass) {
        ComponentDefinition componentDefinition = COMPONENT_CLASS_TO_DEFINITION_MAP.get(componentClass);
        if (componentDefinition == null) {
            throw new LowCodeMetadataFatalException("查不到元数据,componentClass=" + componentClass.getName());
        }
        return componentDefinition;
    }

    private static boolean lock;

    @SuppressWarnings("unchecked")
    public static void init(List<String> basePackages) {
        if (CollectionUtils.isEmpty(basePackages)) {
            return;
        }
        if (lock) {
            throw new UnsupportedOperationException("不允许初始化两次");
        }
        lock = true;
        for (String basePackage : basePackages) {
            basePackage = basePackage.replaceAll(" ", "");
            Reflections f = new Reflections(ConfigurationBuilder.build(basePackage, new TypeAnnotationsScanner()));
            Set<Class<?>> classes = f.getTypesAnnotatedWith(ComponentMetadata.class);
            for (Class<?> tClass : classes) {
                if (!ComponentBO.class.isAssignableFrom(tClass)) {
                    throw new IllegalArgumentException("被@BizComponent标注的类没有继承自ComponentBO:" + tClass.getName());
                }
                ComponentDefinition componentDefinition = new ComponentDefinition((Class<? extends ComponentBO>) tClass);
                COMPONENT_KEY_TO_DEFINITION_MAP.put(componentDefinition.getComponentKey(), componentDefinition);
                COMPONENT_CLASS_TO_DEFINITION_MAP.put(componentDefinition.getComponentClass(), componentDefinition);
            }
        }
    }

}
