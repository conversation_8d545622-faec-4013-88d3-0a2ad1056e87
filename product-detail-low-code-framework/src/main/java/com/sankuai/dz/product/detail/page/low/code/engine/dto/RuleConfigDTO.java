package com.sankuai.dz.product.detail.page.low.code.engine.dto;

import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigIncorrectException;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/6/5 20:19
 */
@Getter
public class RuleConfigDTO implements Serializable {

    /**
     * 规则引擎类型
     */
    private final RuleEngineType ruleEngineType;

    /**
     * DSL
     */
    private final String expression;

    public RuleConfigDTO(final RuleEngineType ruleEngineType,
                         final String expression) {
        this.ruleEngineType = ruleEngineType;
        this.expression = expression;
    }

    public void check() throws LowCodeConfigIncorrectException {
        if (this.ruleEngineType == null) {
            throw new LowCodeConfigIncorrectException("规则引擎类型不能为空");
        }
        if (StringUtils.isBlank(this.expression)) {
            throw new LowCodeConfigIncorrectException("DSL不能为空");
        }
    }

}
