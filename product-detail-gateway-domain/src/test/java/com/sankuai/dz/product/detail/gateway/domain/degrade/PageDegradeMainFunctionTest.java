package com.sankuai.dz.product.detail.gateway.domain.degrade;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.BizDataVO;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.context.InvokerInfo;
import com.sankuai.dz.product.detail.gateway.domain.degrade.exception.PageDegradeException;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PageDegradeMainFunctionTest {

    @Mock
    private GatewayContext context;

    @Mock
    private ProductDetailPageMergeResponse pageResponse;

    @Mock
    private ProductCategory productCategory;

    @Mock
    private InvokerInfo invokerInfo;

    @Mock
    private GenericModuleResponse genericModuleResponse;

    @Mock
    private BizDataVO bizDataVO;

    private PageDegradeMainFunction pageDegradeMainFunction;

    @Before
    public void setUp() {
        pageDegradeMainFunction = new PageDegradeMainFunction();
    }

    /**
     * 测试process方法，正常情况
     */
    @Test
    public void testProcessNormal() throws Throwable {
        // arrange
        Map<String, GenericModuleResponse> responseMap = new HashMap<>();
        responseMap.put("moduleKey1", genericModuleResponse);
        when(context.getInvokerInfoList()).thenReturn(Collections.singletonList(invokerInfo));
        when(invokerInfo.getModuleKeys()).thenReturn(new HashSet<>(Arrays.asList("moduleKey1", "moduleKey2")));
        when(context.getPageResponse()).thenReturn(pageResponse);
        when(pageResponse.getData()).thenReturn(bizDataVO);
        when(bizDataVO.getResponse()).thenReturn(responseMap);
        when(genericModuleResponse.getCode()).thenReturn(200);
        when(context.getProductCategory()).thenReturn(productCategory);
        when(productCategory.getSecondId()).thenReturn(123);
        // act
        pageDegradeMainFunction.process(context);
        // assert
        verify(context, times(1)).getInvokerInfoList();
        verify(invokerInfo, times(1)).getModuleKeys();
        verify(context, times(1)).getPageResponse();
        verify(pageResponse, times(1)).getData();
        verify(bizDataVO, times(1)).getResponse();
        verify(genericModuleResponse, times(1)).getCode();
    }

    /**
     * 测试process方法，异常情况
     */
    @Test
    public void testProcessException() throws Throwable {
        // arrange
        when(context.getInvokerInfoList()).thenThrow(new RuntimeException("Test Exception"));
        // act
        pageDegradeMainFunction.process(context);
        // assert
        verify(context, times(1)).getInvokerInfoList();
    }

    /**
     * 测试process方法，空响应情况
     */
    @Test
    public void testProcessWithNullResponse() throws Throwable {
        // arrange
        Map<String, GenericModuleResponse> responseMap = new HashMap<>();
        responseMap.put("moduleKey1", null);
        when(context.getInvokerInfoList()).thenReturn(Collections.singletonList(invokerInfo));
        when(invokerInfo.getModuleKeys()).thenReturn(new HashSet<>(Arrays.asList("moduleKey1")));
        when(context.getPageResponse()).thenReturn(pageResponse);
        when(pageResponse.getData()).thenReturn(bizDataVO);
        when(bizDataVO.getResponse()).thenReturn(responseMap);
        when(context.getProductCategory()).thenReturn(productCategory);
        when(productCategory.getSecondId()).thenReturn(123);
        // act
        pageDegradeMainFunction.process(context);
        // assert
        verify(context, times(1)).getInvokerInfoList();
        verify(invokerInfo, times(1)).getModuleKeys();
        verify(context, times(1)).getPageResponse();
        verify(pageResponse, times(1)).getData();
        verify(bizDataVO, times(1)).getResponse();
    }

    /**
     * 测试process方法，空ProductCategory情况
     */
    @Test
    public void testProcessWithNullProductCategory() throws Throwable {
        // arrange
        Map<String, GenericModuleResponse> responseMap = new HashMap<>();
        responseMap.put("moduleKey1", genericModuleResponse);
        when(context.getInvokerInfoList()).thenReturn(Collections.singletonList(invokerInfo));
        when(invokerInfo.getModuleKeys()).thenReturn(new HashSet<>(Arrays.asList("moduleKey1")));
        when(context.getPageResponse()).thenReturn(pageResponse);
        when(pageResponse.getData()).thenReturn(bizDataVO);
        when(bizDataVO.getResponse()).thenReturn(responseMap);
        when(context.getProductCategory()).thenReturn(null);
        when(genericModuleResponse.getCode()).thenReturn(200);
        // act
        pageDegradeMainFunction.process(context);
        // assert
        verify(context, times(1)).getInvokerInfoList();
        verify(invokerInfo, times(1)).getModuleKeys();
        verify(context, times(1)).getPageResponse();
        verify(pageResponse, times(1)).getData();
        verify(bizDataVO, times(1)).getResponse();
        verify(genericModuleResponse, times(1)).getCode();
    }
}
