package com.sankuai.dz.product.detail.gateway.api.bff.request;

import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.spy;

@RunWith(MockitoJUnitRunner.class)
public class PageConfigRoutingKeyBuildSelectedOptionalKeyTest {

    @InjectMocks
    private PageConfigRoutingKey pageConfigRoutingKey;

    @Test
    public void testBuildSelectedOptionalKey_NormalCase() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put(OptionalRoutingKeyEnum.ClientType.name(), "android");
        mockMap.put(OptionalRoutingKeyEnum.TradeType.name(), "group");
        spyRoutingKey.setOptionalRoutingKeyMap(mockMap);
        Set<OptionalRoutingKeyEnum> selectedKeys = new HashSet<>();
        selectedKeys.add(OptionalRoutingKeyEnum.ClientType);
        selectedKeys.add(OptionalRoutingKeyEnum.TradeType);
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("ClientType:android-TradeType:group", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_EmptyInputSet() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put(OptionalRoutingKeyEnum.ClientType.name(), "android");
        spyRoutingKey.setOptionalRoutingKeyMap(mockMap);
        Set<OptionalRoutingKeyEnum> selectedKeys = Collections.emptySet();
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_NonExistingKeysInInput() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        Map<String, String> mockMap = new HashMap<>();
        // Corrected the usage of OptionalRoutingKeyEnum as a key in the map
        mockMap.put(OptionalRoutingKeyEnum.ClientType.name(), "android");
        spyRoutingKey.setOptionalRoutingKeyMap(mockMap);
        Set<OptionalRoutingKeyEnum> selectedKeys = new HashSet<>();
        selectedKeys.add(OptionalRoutingKeyEnum.PageSource);
        selectedKeys.add(OptionalRoutingKeyEnum.ClientType);
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("ClientType:android", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_EmptyMap() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        spyRoutingKey.setOptionalRoutingKeyMap(Collections.emptyMap());
        Set<OptionalRoutingKeyEnum> selectedKeys = new HashSet<>();
        selectedKeys.add(OptionalRoutingKeyEnum.ClientType);
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_EmptySetAndMap() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        spyRoutingKey.setOptionalRoutingKeyMap(Collections.emptyMap());
        Set<OptionalRoutingKeyEnum> selectedKeys = Collections.emptySet();
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_OrderVerification() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put(OptionalRoutingKeyEnum.TradeType.name(), "group");
        mockMap.put(OptionalRoutingKeyEnum.ClientType.name(), "android");
        spyRoutingKey.setOptionalRoutingKeyMap(mockMap);
        Set<OptionalRoutingKeyEnum> selectedKeys = new HashSet<>();
        selectedKeys.add(OptionalRoutingKeyEnum.ClientType);
        selectedKeys.add(OptionalRoutingKeyEnum.TradeType);
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("ClientType:android-TradeType:group", result);
    }

    @Test
    public void testBuildSelectedOptionalKey_SpecialCharacters() throws Throwable {
        PageConfigRoutingKey spyRoutingKey = spy(pageConfigRoutingKey);
        Map<String, String> mockMap = new HashMap<>();
        mockMap.put(OptionalRoutingKeyEnum.AB.name(), "a:b-c");
        spyRoutingKey.setOptionalRoutingKeyMap(mockMap);
        Set<OptionalRoutingKeyEnum> selectedKeys = new HashSet<>();
        selectedKeys.add(OptionalRoutingKeyEnum.AB);
        String result = spyRoutingKey.buildSelectedOptionalKey(selectedKeys);
        assertEquals("AB:a:b-c", result);
    }
}
