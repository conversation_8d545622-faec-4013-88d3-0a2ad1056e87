package com.sankuai.dz.product.detail.page.low.code.engine.util;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.ContainerComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.DisplayRuleEngineFactory;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;

@Slf4j
public class EngineCacheRefreshUtils {

    private static final ThreadPool threadPool = Rhino.newThreadPool("RefreshEngineCacheThreadPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(20)
                    .withMaxSize(40)
                    .withMaxQueueSize(1000)
    );

    public static void init4PageDisplayConfigBO(PageDisplayConfigBO displayConfigBO) {
        if (MapUtils.isEmpty(displayConfigBO.getModuleConfigs())) {
            return;
        }
        for (ModuleDisplayConfigBO moduleConfig : displayConfigBO.getModuleConfigs().values()) {
            if (CollectionUtils.isEmpty(moduleConfig.getComponentConfigList())) {
                continue;
            }
            // 遍历模块中的所有组件
            for (ComponentDisplayConfigBO componentConfig : moduleConfig.getComponentConfigList()) {
                // 检查是否是业务组件
                if (componentConfig instanceof BizComponentDisplayConfigBO) {
                    init4BizComponentDisplayConfigBO((BizComponentDisplayConfigBO) componentConfig);
                }
                // 检查是否是业务组件
                if (componentConfig instanceof ContainerComponentDisplayConfigBO) {
                    init4ContainerComponentDisplayConfigBO((ContainerComponentDisplayConfigBO) componentConfig);
                }
            }
        }
    }

    private static void init4ContainerComponentDisplayConfigBO(ContainerComponentDisplayConfigBO componentConfig) {
        if (componentConfig == null || CollectionUtils.isEmpty(componentConfig.getSubComponentDisplayConfigs())) {
            return;
        }
        List<BizComponentDisplayConfigBO> allBizComponentDisplayConfigs = componentConfig.getAllBizComponentDisplayConfigsRecursively();
        for (BizComponentDisplayConfigBO configBO : allBizComponentDisplayConfigs) {
            init4BizComponentDisplayConfigBO(configBO);
        }
    }

    private static void init4BizComponentDisplayConfigBO(BizComponentDisplayConfigBO componentConfig) {
        List<RuleConfigDTO> allRuleConfigs = componentConfig.getAllRuleConfigs();
        for (RuleConfigDTO allRuleConfig : allRuleConfigs) {
            final String engineCode = allRuleConfig.getRuleEngineType().getCode();
            final String expression = allRuleConfig.getExpression();
            // 异步执行preLoad
            threadPool.execute(() -> DisplayRuleEngineFactory.preLoad(engineCode, expression));
        }

    }

}