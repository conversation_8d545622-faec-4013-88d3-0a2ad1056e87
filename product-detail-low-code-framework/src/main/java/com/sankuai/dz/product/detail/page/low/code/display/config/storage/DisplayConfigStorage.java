package com.sankuai.dz.product.detail.page.low.code.display.config.storage;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;
import com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion.DisplayConfigLionGetter;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/5/23 16:20
 */
@Slf4j
public class DisplayConfigStorage {

    private final static String APP_KEY = "com.sankuai.dzshoppingguide.detail.config";

    /**
     * 所有lionKey集合
     */
    private final static String ALL_CONFIG_LION_KEY = "com.sankuai.dzshoppingguide.detail.config.all.low.code.config.key";

    /**
     * 所有lowcode配置缓存
     */
    private final static DisplayConfigLionGetter<PageDisplayConfigBO> lowcodeConfigCache = new DisplayConfigLionGetter<>(PageDisplayConfigBO.class);

    public static void init() {
        try {
            List<String> allConfigLionKey = Lion.getList(APP_KEY, ALL_CONFIG_LION_KEY, String.class, new ArrayList<>());
            //项目启动强制更新所有key的LionFileResourceGetter
            for (String lionKey : allConfigLionKey) {
                //强制更新缓存并重新绑定监听器
                lowcodeConfigCache.forceInitSingleKey(lionKey, DisplayConfigCustomProcessor::preProcess, null);
            }
            //增加ALL_CONFIG_LION_KEY的监听器，随时增加LionFileResourceGetter的监听器，这里用initSingleKey不会强制更新已初始化的key
            Lion.addConfigListener(ALL_CONFIG_LION_KEY, configEvent -> {
                List<String> newAllConfigLionKey = Lion.getList(APP_KEY, ALL_CONFIG_LION_KEY, String.class, new ArrayList<>());
                for (String lionKey : newAllConfigLionKey) {
                    //非强制更新缓存并重新绑定监听器，如果已经绑定了监听器则跳过
                    lowcodeConfigCache.initSingleKey(lionKey, DisplayConfigCustomProcessor::preProcess, null);
                }
            });
        } catch (Throwable e) {
            Cat.logError("LowCodeConfigService", new LowCodeConfigFatalException(e));
            log.error("LowCodeConfigService.init", e);
            throw e;
        }
    }

    /**
     * 获取关键配置，有降级策略，从三级类目->二级类目->一级类目->商品类型，一定能获取到配置
     */
    public static ConfigQueryResponse getConfig(final PageConfigRoutingKey pageConfigRoutingKey) {
        pageConfigRoutingKey.checkParams();
        //先看三级类目
        String thirdCategoryIdMainKey = pageConfigRoutingKey.buildThirdCategoryIdMainKey();
        PageDisplayConfigBO thirdCategoryIdConfigTemplate = lowcodeConfigCache.get(thirdCategoryIdMainKey);
        if (thirdCategoryIdConfigTemplate != null) {
            return new ConfigQueryResponse(thirdCategoryIdMainKey, thirdCategoryIdConfigTemplate);
        }
        //再降级到二级类目
        String secondCategoryIdMainKey = pageConfigRoutingKey.buildSecondCategoryIdMainKey();
        PageDisplayConfigBO secondCategoryIdConfigTemplate = lowcodeConfigCache.get(secondCategoryIdMainKey);
        if (secondCategoryIdConfigTemplate != null) {
            return new ConfigQueryResponse(secondCategoryIdMainKey, secondCategoryIdConfigTemplate);
        }
        //再降级到一级类目
        String firstCategoryIdMainKey = pageConfigRoutingKey.buildFirstCategoryIdMainKey();
        PageDisplayConfigBO firstCategoryIdConfigTemplate = lowcodeConfigCache.get(firstCategoryIdMainKey);
        if (firstCategoryIdConfigTemplate != null) {
            return new ConfigQueryResponse(firstCategoryIdMainKey, firstCategoryIdConfigTemplate);
        }
        //最后降级到商品类型兜底
        String defaultMainKey = pageConfigRoutingKey.buildDefaultMainKey();
        PageDisplayConfigBO defaultConfigTemplate = lowcodeConfigCache.get(defaultMainKey);
        return new ConfigQueryResponse(defaultMainKey, defaultConfigTemplate);
    }

}
