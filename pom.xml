<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.6</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.dz</groupId>
    <artifactId>product-detail-gateway</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>deal-detail-gateway</name>

    <modules>
        <module>product-detail-gateway-api</module>
        <module>product-detail-gateway-starter</module>
        <module>product-detail-gateway-application</module>
        <module>product-detail-gateway-domain</module>
        <module>product-detail-gateway-infrastructure</module>
        <module>product-detail-page-config</module>
        <module>product-detail-module-arrange-framework</module>
        <module>product-detail-common</module>
        <module>product-detail-low-code-framework</module>
    </modules>

    <properties>
        <revision>1.0.0</revision>
        <product-detail-common.version>1.0.7</product-detail-common.version>
        <!--因为用了flatten-maven-plugin, 要求jar包版本必须一致-->
        <product-detail-gateway-api.version>2.0.0</product-detail-gateway-api.version>
        <product-detail-low-code-framework.version>2.0.0</product-detail-low-code-framework.version>
        <product-detail-module-arrange-framework.version>2.0.0</product-detail-module-arrange-framework.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-common</artifactId>
                <version>${product-detail-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-api</artifactId>
                <version>${product-detail-gateway-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-module-arrange-framework</artifactId>
                <version>${product-detail-module-arrange-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-low-code-framework</artifactId>
                <version>${product-detail-low-code-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-page-config</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
