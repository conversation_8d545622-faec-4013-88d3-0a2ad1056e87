package com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/7 11:25
 */
@Getter
@SuppressWarnings("rawtypes")
@Slf4j
public class BuilderFactoryDefinition extends BuilderDefinition {

    @Override
    public BuilderTypeEnum getBuilderType() {
        return BuilderTypeEnum.BUILDER_FACTORY;
    }

    /**
     * 可变Builder的实现类
     */
    private final Set<Class<? extends AbstractBuilder>> variableBuilderDefinitions;//已转为只读

    /**
     * 所有依赖的fetcher，包括所有泛型Builder
     */
    private final Set<String> allDependentFetcherNames;//已转为只读

    public BuilderFactoryDefinition(final Class<? extends BaseBuilderFactory> builderFactoryClass,
                                    final List<VariableBuilderDefinition> variableBuilderDefinitions) {
        super(builderFactoryClass);
        this.variableBuilderDefinitions = Collections.unmodifiableSet(
                variableBuilderDefinitions.stream()
                        .map(BuilderDefinition::getBuilderClass)
                        .collect(Collectors.toSet())
        );
        Set<String> allDependentFetcherNames = variableBuilderDefinitions.stream()
                .map(BuilderDefinition::getDependentFetcherNames)
                .reduce((set1, set2) -> {
                    Set<String> newSet = new HashSet<>();
                    newSet.addAll(set1);
                    newSet.addAll(set2);
                    return newSet;
                }).orElse(new HashSet<>());//所有关联泛型Builder依赖
        allDependentFetcherNames.addAll(super.getDependentFetcherNames());//BuilderFactory自身的依赖
        this.allDependentFetcherNames = Collections.unmodifiableSet(allDependentFetcherNames);
    }

    @SuppressWarnings("rawtypes")
    @Override
    public Set<String> getRuntimeDependentFetcherNames(ProductDetailPageRequest request) {
        try {
            if (CollectionUtils.isEmpty(super.getDependentFetcherNames())) {
                //这里是性能优化，如果factory没有任何依赖，则前置识别出对应的泛型Builder的DependentFetcher，从而可以减少无效调用，缩短最长路径
                BaseBuilderFactory builderFactoryBean = BuilderDefinitionStorage.getBuilderBean(this);
                if (builderFactoryBean == null) {
                    throw new IllegalStateException("获取Builder工厂bean失败，具体原因参考BuilderFatalError");
                }
                BuilderDefinition variableBuilderDefinition = builderFactoryBean.getVariableBuilderDefinition();
                return variableBuilderDefinition.getDependentFetcherNames();
            } else {
                //如果factory有依赖，则需要等待Fetcher执行完才能确定VariableBuilder，所以所有VariableBuilder对应的Fetcher都要执行
                return this.allDependentFetcherNames;
            }
        } catch (Throwable throwable) {
            log.error("BuilderFactoryDefinition.getRuntimeDependentFetcherNames",
                    new BuilderFatalError("前置获取Builder工厂依赖失败", throwable));
            return new HashSet<>();
        }
    }

}
