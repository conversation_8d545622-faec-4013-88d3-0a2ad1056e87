package com.sankuai.dz.product.detail.page.low.code.runtime.response;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/6/4 14:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComponentProcessResult {

    private String componentKey;

    private boolean success;

    private Throwable throwable;

    private ComponentBO componentBO;

    public static ComponentProcessResult succeed(ComponentBO componentBO) {
        return new ComponentProcessResult(componentBO.getComponentKey(), true, null, componentBO);
    }

    public static ComponentProcessResult fail(String componentKey, Throwable throwable) {
        return new ComponentProcessResult(componentKey, false, throwable, null);
    }

}
