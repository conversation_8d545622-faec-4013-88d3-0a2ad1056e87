package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDAGException;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/1/30 00:26
 */
public class DAGBFSCycleChecker {

    /**
     * 广度遍历判断是否存在环，入度检测，相比DFS不会发生递归栈溢出，建议使用
     */
    public static KahnResult performKahnCheck(final Map<String, Set<String>> childrenGraph,
                                              final Map<String, Set<String>> parentGraph) {
        Map<String, Integer> inDegreeMap = DAGBFSUtils.initInDegreeMap(parentGraph);
        Queue<String> queue = DAGBFSUtils.initQueue(inDegreeMap, null);

        int processedCount = 0;
        while (!queue.isEmpty()) {
            String current = queue.poll();
            processedCount++;
            for (String child : childrenGraph.getOrDefault(current, new HashSet<>())) {
                int newDegree = inDegreeMap.get(child) - 1;
                inDegreeMap.put(child, newDegree);
                if (newDegree == 0) queue.add(child);
            }
        }

        // 计算剩余节点（入度仍大于0的节点）
        Set<String> remaining = new HashSet<>();
        inDegreeMap.forEach((node, degree) -> {
            if (degree > 0) remaining.add(node);
        });

        return new KahnResult(processedCount != childrenGraph.keySet().size(), remaining, inDegreeMap);
    }

    // Kahn算法执行结果封装
    @Data
    public static class KahnResult {
        public boolean hasCycle;
        public Set<String> remainingNodes;
        public Map<String, Integer> inDegree;

        KahnResult(boolean hasCycle, Set<String> remainingNodes, Map<String, Integer> inDegree) {
            this.hasCycle = hasCycle;
            this.remainingNodes = remainingNodes;
            this.inDegree = inDegree;
        }

        public void checkCycle(final Map<String, Set<String>> entireDAGChildrenGraph) throws FetcherDAGException {
            if (this.hasCycle) {
                List<String> cycleNodes = DAGBFSCycleChecker.findCycleNodes(entireDAGChildrenGraph, this);
                if (CollectionUtils.isNotEmpty(cycleNodes)) {
                    throw new FetcherDAGException(String.format("DAG中存在环(%s)", cycleNodes));
                } else {
                    throw new FetcherDAGException("发生系统未知异常！！！DAG中存在环，但是找不到具体路径");
                }
            }
        }
    }

    public static List<String> findCycleNodes(final Map<String, Set<String>> childrenGraph,
                                              final KahnResult result) {
        if (!result.hasCycle) return Collections.emptyList();

        // 在剩余节点中寻找环路径
        for (String node : result.remainingNodes) {
            List<String> cycle = findCyclePath(childrenGraph, node, result.inDegree);
            if (!cycle.isEmpty()) return cycle;
        }
        return Collections.emptyList();
    }

    private static List<String> findCyclePath(final Map<String, Set<String>> childrenGraph,
                                              final String startNode,
                                              final Map<String, Integer> inDegree) {
        Map<String, String> parentRoadMap = new HashMap<>();
        Queue<String> queue = new LinkedList<>();
        Set<String> visited = new HashSet<>();

        queue.add(startNode);
        visited.add(startNode);
        parentRoadMap.put(startNode, null);

        while (!queue.isEmpty()) {
            String current = queue.poll();
            for (String child : childrenGraph.getOrDefault(current, new HashSet<>())) {
                // 仅处理环中的节点
                if (inDegree.get(child) <= 0) continue;

                if (!visited.contains(child)) {
                    visited.add(child);
                    parentRoadMap.put(child, current);
                    queue.add(child);
                } else {
                    // 发现环，回溯路径
                    return buildCyclePath(parentRoadMap, current, child);
                }
            }
        }
        return Collections.emptyList();
    }

    private static List<String> buildCyclePath(final Map<String, String> parentRoadMap,
                                               final String end, String start) {
        LinkedList<String> path = new LinkedList<>();
        String node = end;
        while (node != null && !node.equals(start)) {
            path.addFirst(node);
            node = parentRoadMap.get(node);
        }
        path.addFirst(start);
        return path;
    }

}
