package com.sankuai.dz.product.detail.gateway.domain.data.merge.service;

import com.google.common.collect.Maps;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.BizDataVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.PageConfigDataVO;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.dto.DataMergeRequest;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/1/17 17:46
 */
@Component
public class ProductDetailPageDataMergeService {

    @Resource
    private ModulePositionModifyService modulePositionModifyService;

    @Resource
    private CommonDataMergeService commonDataMergeService;

    public ProductDetailPageMergeResponse merge(final DataMergeRequest request) {
        request.checkParam();
        PageConfigDataVO layout = buildLayout(request);
        BizDataVO data = buildData(request);
        List<ABResultDTO> abResultList = buildABResult(request);
        //todo 这里定制化开发了，后续要想办法去掉
        modulePositionModifyService.execute(layout, request.getSpiRequest());
        return ProductDetailPageMergeResponse.succeed(layout, data, abResultList);
    }

    private PageConfigDataVO buildLayout(final DataMergeRequest request) {
        PageConfigDataVO vo = new PageConfigDataVO();
        vo.setRouteKey(request.getSpiRequest().getPageConfigRoutingKey());
        BuildModuleVORequest buildModuleVORequest = new BuildModuleVORequest();
        buildModuleVORequest.setModuleResponseMap(responseMapping(request.getModuleResponse()));
        vo.setData(request.getPageConfigData().buildVO(buildModuleVORequest));
        return vo;
    }

    private BizDataVO buildData(final DataMergeRequest request) {
        BizDataVO data = new BizDataVO();
        //合并不同接口的common_data
        data.setCommonData(commonDataMergeService.merge(request));
        data.setResponse(request.getModuleResponse());
        return data;
    }

    private List<ABResultDTO> buildABResult(final DataMergeRequest request) {
        return request.getAbResultList();
    }

    private Map<String, Boolean> responseMapping(final Map<String, GenericModuleResponse> responseMap) {
        if (MapUtils.isEmpty(responseMap)) {
            return Maps.newHashMap();
        }
        Map<String, Boolean> statusMap = Maps.newHashMapWithExpectedSize(responseMap.size());
        responseMap.forEach((moduleKey, response) -> statusMap.put(
                moduleKey,
                response != null
                        && response.isSuccess()
                        && response.getModuleVO() != null
        ));
        return statusMap;
    }

}
