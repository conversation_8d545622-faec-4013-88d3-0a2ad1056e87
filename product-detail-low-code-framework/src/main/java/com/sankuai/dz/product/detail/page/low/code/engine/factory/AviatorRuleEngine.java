package com.sankuai.dz.product.detail.page.low.code.engine.factory;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.Options;
import com.googlecode.aviator.exception.ExpressionSyntaxErrorException;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import com.sankuai.dz.product.detail.page.low.code.engine.util.MD5Utils;
import com.sankuai.dz.product.detail.page.low.code.engine.util.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Aviator规则引擎实现类
 */
public class AviatorRuleEngine extends AbstractDetailRuleEngine {

    private static final Logger LOGGER = LoggerFactory.getLogger(AviatorRuleEngine.class);

    /**
     * Aviator表达式缓存
     */
    private static final Cache<String, Expression> EXPRESSION_CACHE = CacheBuilder.newBuilder()
            //设置并发级别为8，并发级别是指可以同时写缓存的线程数
            .concurrencyLevel(100)
            //设置写缓存后24小时过期
            .expireAfterWrite(24, TimeUnit.DAYS)
            //设置缓存容器的初始容量为10
            .initialCapacity(10)
            //设置缓存最大容量为2000，超过之后就会按照LRU最近虽少使用算法来移除缓存项
            //单个script大概占用340KB,1000个大概340M
            .maximumSize(2000)
            //设置要统计缓存的命中率
            .recordStats()
            //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存
            .build();

    /**
     * Aviator引擎实例
     */
    private static final AviatorEvaluatorInstance AVIATOR_INSTANCE;

    static {
        // 初始化Aviator引擎实例
        AVIATOR_INSTANCE = AviatorEvaluator.newInstance();
        AVIATOR_INSTANCE.setOption(Options.OPTIMIZE_LEVEL, 0);
        AVIATOR_INSTANCE.setCachedExpressionByDefault(true);

        // 注册自定义函数
    }

    /**
     * 获取Aviator规则引擎实例
     *
     * @return Aviator规则引擎实例
     */
    public static AviatorRuleEngine getInstance() {
        return Holder.INSTANCE;
    }

    /**
     * 单例持有者
     */
    private static class Holder {
        private static final AviatorRuleEngine INSTANCE = new AviatorRuleEngine();
    }

    /**
     * 私有构造函数
     */
    private AviatorRuleEngine() {
    }

    @Override
    public Object execute(String ruleExpression, Map<String, Object> context) {
        try {
            Map<String, Object> env = initEnv(context);
            if (StringUtils.isBlank(ruleExpression)) {
                return null;
            }
            Expression expression = getExpression(ruleExpression);
            return expression.execute(env);
        } catch (Exception e) {
            LOGGER.error("Execute aviator rule error, expression: {}, context: {}", ruleExpression, context, e);
            return null;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T execute(String ruleExpression, Map<String, Object> context, Class<T> resultType) {
        try {
            if (StringUtils.isBlank(ruleExpression)) {
                return null;
            }
            Object result = execute(ruleExpression, context);
            if (result == null) {
                return null;
            }

            // 如果结果类型已经匹配，直接返回
            if (resultType.isAssignableFrom(result.getClass())) {
                return (T) result;
            }

            // 处理数值类型转换
            if (NumberUtils.isNumericType(resultType) && NumberUtils.isNumericType(result.getClass())) {
                return NumberUtils.convertNumericType(result, resultType);
            }

            // 处理字符串类型转换
            if (resultType == String.class) {
                return (T) result.toString();
            }

            LOGGER.warn("Result type mismatch, expected: {}, actual: {}", resultType.getName(), result.getClass().getName());
            return null;
        } catch (Exception e) {
            LOGGER.error("Execute aviator rule error, expression: {}, context: {}", ruleExpression, context, e);
            return null;
        }
    }

    @Override
    public boolean validate(String ruleExpression) {
        try {
            AVIATOR_INSTANCE.compile(ruleExpression, true);
            return true;
        } catch (ExpressionSyntaxErrorException e) {
            LOGGER.warn("Invalid aviator expression: {}, error: {}", ruleExpression, e.getMessage());
            return false;
        } catch (Exception e) {
            LOGGER.error("Validate aviator expression error: {}", ruleExpression, e);
            return false;
        }
    }

    @Override
    public boolean preLoad(String ruleExpression) {
        if (StringUtils.isEmpty(ruleExpression)) {
            return false;
        }
        try {
            getExpression(ruleExpression);
        } catch (Exception e) {
            LOGGER.error("Preload aviator expression error: {}", ruleExpression, e);
            return false;
        }
        return false;
    }

    @Override
    public String getType() {
        return RuleEngineType.AVIATOR.getCode();
    }

    /**
     * 获取或编译表达式
     * 使用Guava Cache缓存编译后的表达式，并对表达式字符串进行MD5处理作为缓存的key
     *
     * @param ruleExpression 规则表达式
     * @return 编译后的表达式
     */
    private Expression getExpression(String ruleExpression) {
        try {
            // 对表达式进行MD5处理，生成缓存key
            String cacheKey = MD5Utils.getMD5(ruleExpression);

            // 从缓存获取或编译表达式
            return EXPRESSION_CACHE.get(cacheKey, () -> AVIATOR_INSTANCE.compile(ruleExpression, true));
        } catch (ExecutionException e) {
            LOGGER.error("Failed to get or compile expression: {}", ruleExpression, e);
            // 如果缓存获取失败，直接编译返回（不缓存）
            return AVIATOR_INSTANCE.compile(ruleExpression, true);
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public static String getCacheStats() {
        return EXPRESSION_CACHE.stats().toString();
    }

    /**
     * 静态方法：执行规则
     *
     * @param ruleExpression 规则表达式
     * @param context 上下文参数
     * @return 执行结果
     */
    public static Object executeRule(String ruleExpression, Map<String, Object> context) {
        return getInstance().execute(ruleExpression, context);
    }

    /**
     * 静态方法：执行规则并指定返回类型
     *
     * @param ruleExpression 规则表达式
     * @param context 上下文参数
     * @param resultType 返回结果类型
     * @param <T> 返回类型泛型
     * @return 执行结果
     */
    public static <T> T executeRule(String ruleExpression, Map<String, Object> context, Class<T> resultType) {
        return getInstance().execute(ruleExpression, context, resultType);
    }

    /**
     * 静态方法：验证规则表达式是否有效
     *
     * @param ruleExpression 规则表达式
     * @return 是否有效
     */
    public static boolean validateRule(String ruleExpression) {
        return getInstance().validate(ruleExpression);
    }
}