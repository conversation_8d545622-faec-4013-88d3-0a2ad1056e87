package com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/5/27 15:09
 */
@Getter
public class ModuleDisplayConfigBO implements Serializable {

    /**
     * 模块key，全局唯一
     */
    private final String moduleKey;

    /**
     * 模块的组件列表
     */
    private final List<ComponentDisplayConfigBO> componentConfigList;

    /**
     * 模块维度所需数据源与字段
     */
    private final Map<String, DataSourceConfigBO> allDataSource;

    public ModuleDisplayConfigBO(final String moduleKey,
                                 final List<ComponentDisplayConfigBO> componentConfigList) {
        this.moduleKey = moduleKey;
        this.componentConfigList = Collections.unmodifiableList(componentConfigList);
        this.allDataSource = Collections.unmodifiableMap(buildAllDataSource());
    }

    private Map<String, DataSourceConfigBO> buildAllDataSource() {
        if (CollectionUtils.isEmpty(componentConfigList)) {
            return new HashMap<>();
        }
        return componentConfigList.stream()
                .map(ComponentDisplayConfigBO::getDataSourceConfigs)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(
                        // 按fetcherName分组，并合并optionalFieldName
                        DataSourceConfigBO::getFetcherName,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                dataSourceList -> {
                                    // 合并所有optionalFieldName
                                    Set<String> mergedOptionalFields = dataSourceList.stream()
                                            .map(DataSourceConfigBO::getOptionalFieldName)
                                            .filter(Objects::nonNull)
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toSet());
                                    return new DataSourceConfigBO(dataSourceList.get(0).getFetcherName(), mergedOptionalFields);
                                }
                        )
                ));
    }

}
