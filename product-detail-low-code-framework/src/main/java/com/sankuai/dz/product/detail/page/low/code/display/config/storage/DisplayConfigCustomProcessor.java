package com.sankuai.dz.product.detail.page.low.code.display.config.storage;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.util.EngineCacheRefreshUtils;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guangyujie
 * @Date: 2025/6/6 16:28
 */
@Slf4j
public class DisplayConfigCustomProcessor {

    public static Void preProcess(final String lionKey,
                                  final PageDisplayConfigBO pageDisplayConfig) {
        Transaction transaction = Cat.newTransaction("DisplayConfigCustomProcessor", "preProcess");
        try {
            //校验配置正确性
            if (pageDisplayConfig == null) {
                throw new LowCodeConfigFatalException("配置为空");
            }
            pageDisplayConfig.check();
            //预热规则引擎
            EngineCacheRefreshUtils.init4PageDisplayConfigBO(pageDisplayConfig);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            transaction.setStatus(e);
            LowCodeConfigFatalException exception = new LowCodeConfigFatalException(e);
            log.error("DisplayConfigCustomProcessor.preProcess,lionKey={},pageConfig={}",
                    lionKey, JSON.toJSONString(pageDisplayConfig), exception);
            throw exception;
        } finally {
            transaction.complete();
        }
        return null;
    }

}
