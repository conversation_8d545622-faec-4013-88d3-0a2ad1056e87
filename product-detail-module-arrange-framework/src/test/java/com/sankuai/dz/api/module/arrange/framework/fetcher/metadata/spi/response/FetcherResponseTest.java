package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Unit tests for {@link FetcherResponse#succeed(FetcherReturnValueDTO)}
 */
@DisplayName("FetcherResponse.succeed() method tests")
class FetcherResponseTest {

    /**
     * Test case for successful response creation with non-null return value
     */
    @Test
    @DisplayName("Should create successful response with non-null return value")
    public void testSucceedWithNonNullReturnValue() {
        // Arrange
        FetcherReturnValueDTO mockReturnValue = mock(FetcherReturnValueDTO.class);
        // Act
        FetcherResponse<FetcherReturnValueDTO> response = FetcherResponse.succeed(mockReturnValue);
        // Assert
        assertAll(() -> assertTrue(response.isSuccess(), "Response should be successful"), () -> assertNull(response.getError(), "Error should be null"), () -> assertEquals(mockReturnValue, response.getReturnValue(), "Return value should match input"), () -> assertNotNull(response, "Response object should not be null"));
    }

    /**
     * Test case for successful response creation with null return value
     */
    @Test
    @DisplayName("Should create successful response with null return value")
    public void testSucceedWithNullReturnValue() {
        // Arrange
        FetcherReturnValueDTO nullReturnValue = null;
        // Act
        FetcherResponse<FetcherReturnValueDTO> response = FetcherResponse.succeed(nullReturnValue);
        // Assert
        assertAll(() -> assertTrue(response.isSuccess(), "Response should be successful"), () -> assertNull(response.getError(), "Error should be null"), () -> assertNull(response.getReturnValue(), "Return value should be null"));
    }

    /**
     * Test case to verify new instance is created on each call
     */
    @Test
    @DisplayName("Should return new instance on each call")
    public void testSucceedReturnsNewInstanceEachTime() {
        // Arrange
        FetcherReturnValueDTO mockReturnValue = mock(FetcherReturnValueDTO.class);
        // Act
        FetcherResponse<FetcherReturnValueDTO> response1 = FetcherResponse.succeed(mockReturnValue);
        FetcherResponse<FetcherReturnValueDTO> response2 = FetcherResponse.succeed(mockReturnValue);
        // Assert
        assertAll(() -> assertNotSame(response1, response2, "Should return different instances"), () -> assertEquals(response1, response2, "Instances should be equal in value"));
    }

    /**
     * Test case to verify response properties are set correctly
     */
    @Test
    @DisplayName("Should set all response properties correctly")
    public void testSucceedSetsPropertiesCorrectly() {
        // Arrange
        FetcherReturnValueDTO mockReturnValue = mock(FetcherReturnValueDTO.class);
        // Act
        FetcherResponse<FetcherReturnValueDTO> response = FetcherResponse.succeed(mockReturnValue);
        // Assert
        assertAll(() -> assertTrue(response.isSuccess(), "Success flag should be true"), () -> assertNull(response.getError(), "Error should be null"), () -> assertEquals(mockReturnValue, response.getReturnValue(), "Return value should match input"));
    }

    @Test
    public void testFailWithNonNullThrowable() throws Throwable {
        // arrange
        Throwable mockError = mock(Throwable.class);
        // act
        FetcherResponse<FetcherReturnValueDTO> response = FetcherResponse.fail(mockError);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(mockError, response.getError());
        assertNull(response.getReturnValue());
    }

    @Test
    public void testFailWithNullThrowable() throws Throwable {
        // arrange
        Throwable nullError = null;
        // act
        FetcherResponse<FetcherReturnValueDTO> response = FetcherResponse.fail(nullError);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getError());
        assertNull(response.getReturnValue());
    }
}
