package com.sankuai.dz.product.detail.page.low.code.entity.component.metadata;

import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeMetadataFatalException;
import lombok.Getter;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 17:27
 */
@Getter
public class ComponentDefinition implements Serializable {

    private final String componentKey;

    private final Class<? extends ComponentBO> componentClass;

    private final String moduleKey;

    private final String desc;

    private final ComponentGroupEnum componentGroup;

    private final ComponentTypeEnum componentType;

    private final DAClassInfoBO classInfo;

    public ComponentDefinition(final Class<? extends ComponentBO> componentClass) {
        ComponentMetadata componentMetadata = componentClass.getAnnotation(ComponentMetadata.class);
        if (componentMetadata == null) {
            throw new LowCodeMetadataFatalException("组件没有标注@BizComponent:" + componentClass.getName());
        }
        DAClassInfoBO daClassInfoBO = DAClassInfoCache.getClassInfo(componentClass);
        if (daClassInfoBO == null) {
            throw new LowCodeMetadataFatalException("没有该类的元数据信息:" + componentClass.getName());
        }
        //校验元数据是否符合要求，组件定义的字段只允许是①基本类型、②POJO、③List（List的泛型只能是①和②，不允许List嵌套）
        ComponentDefinitionValidator.checkClassField(daClassInfoBO, 0);
        this.componentKey = componentMetadata.componentKey();
        this.componentClass = componentClass;
        this.moduleKey = componentMetadata.moduleKey();
        this.desc = componentMetadata.desc();
        this.componentGroup = componentMetadata.componentGroup();
        this.componentType = componentMetadata.componentType();
        this.classInfo = daClassInfoBO;
    }

}
