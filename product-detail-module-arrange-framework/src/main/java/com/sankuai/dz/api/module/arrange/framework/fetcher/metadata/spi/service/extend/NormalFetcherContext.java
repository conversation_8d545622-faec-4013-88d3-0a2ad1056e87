package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.NormalFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/7 10:23
 */
public abstract class NormalFetcherContext<RESULT extends FetcherReturnValueDTO>
        extends BaseFetcherContext<RESULT, NormalFetcherDefinition> {

    /**
     * 正常执行初始化
     */
    @Override
    protected FetcherContextInitResult doInitContext(final FetcherEngine fetcherEngine) throws Exception {
        this.future = this.doFetch()
                    .thenApply(FetcherResponse::succeed)
                    .exceptionally(FetcherResponse::fail);
        return new FetcherContextInitResult(this);
    }

    /**
     * 初始化异常兜底，为了不打断整个流程，一定要对初始化异常的情况做Fetcher维度的降级，不能抛出异常
     */
    @Override
    protected FetcherContextInitResult doInitContextExceptionally(final FetcherEngine fetcherEngine,
                                                                  final Throwable throwable) {
        this.future = CompletableFuture.completedFuture(FetcherResponse.fail(throwable));
        return new FetcherContextInitResult(this);
    }

    protected abstract CompletableFuture<RESULT> doFetch() throws Exception;

}
