package com.sankuai.dz.product.detail.metadata.sdk.management;

import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.ProductDetailPageConfigService;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductDetailPageConfigRpcServiceImpl 单元测试
 * 目标覆盖率: >70%
 * 
 * @Author: guangyujie
 * @Date: 2025/1/15 19:00
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageConfigRpcServiceImplTest {

    @InjectMocks
    private ProductDetailPageConfigRpcServiceImpl rpcService;

    @Mock
    private ProductDetailPageConfigService productDetailPageConfigService;

    private PageConfigRoutingKey validRoutingKey;
    private PageConfigTemplateDTO mockTemplate;
    private PageConfigDataDTO mockPageConfig;
    private Set<String> mockRoutingKeys;

    @Before
    public void setUp() {
        // 创建有效的路由key
        validRoutingKey = new PageConfigRoutingKey();
        validRoutingKey.setScene("detail");
        validRoutingKey.setProductType(1);
        validRoutingKey.setProductFirstCategoryId(100);
        validRoutingKey.setProductSecondCategoryId(200);
        validRoutingKey.setProductThirdCategoryId(300);

        // 创建模拟的配置模板
        mockTemplate = new PageConfigTemplateDTO();
        mockPageConfig = new PageConfigDataDTO();

        // 创建模拟的路由key集合
        mockRoutingKeys = new HashSet<>();
        mockRoutingKeys.add("detail-default-1");
        mockRoutingKeys.add("detail-first-1-100");
        mockRoutingKeys.add("detail-second-1-200");
        mockRoutingKeys.add("detail-third-1-300");
    }

    /**
     * 测试 queryConfigTemplate 方法 - 正常场景
     */
    @Test
    public void testQueryConfigTemplateSuccess() {
        // 模拟 productDetailPageConfigService 返回配置模板
        when(productDetailPageConfigService.getPageConfigTemplate(validRoutingKey))
                .thenReturn(mockTemplate);

        // 执行测试
        PageConfigTemplateDTO result = rpcService.queryConfigTemplate(validRoutingKey);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回模拟的配置模板", mockTemplate, result);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(validRoutingKey);
    }

    /**
     * 测试 queryConfigTemplate 方法 - 空参数
     */
    @Test
    public void testQueryConfigTemplateWithNullRoutingKey() {
        // 模拟 productDetailPageConfigService 处理空参数
        when(productDetailPageConfigService.getPageConfigTemplate(null))
                .thenReturn(null);

        // 执行测试
        PageConfigTemplateDTO result = rpcService.queryConfigTemplate(null);

        // 验证结果
        assertNull("空参数应该返回null", result);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(null);
    }

    /**
     * 测试 queryConfigTemplate 方法 - 服务抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testQueryConfigTemplateWithException() {
        // 模拟 productDetailPageConfigService 抛出异常
        when(productDetailPageConfigService.getPageConfigTemplate(validRoutingKey))
                .thenThrow(new RuntimeException("配置服务异常"));

        // 执行测试，期望抛出异常
        rpcService.queryConfigTemplate(validRoutingKey);
    }

    /**
     * 测试 getPageConfig 方法 - 正常场景
     */
    @Test
    public void testGetPageConfigSuccess() {
        // 模拟 productDetailPageConfigService 返回页面配置
        when(productDetailPageConfigService.getPageConfig(validRoutingKey))
                .thenReturn(mockPageConfig);

        // 执行测试
        PageConfigDataDTO result = rpcService.getPageConfig(validRoutingKey);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回模拟的页面配置", mockPageConfig, result);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfig(validRoutingKey);
    }

    /**
     * 测试 getPageConfig 方法 - 空参数
     */
    @Test
    public void testGetPageConfigWithNullRoutingKey() {
        // 模拟 productDetailPageConfigService 处理空参数
        when(productDetailPageConfigService.getPageConfig(null))
                .thenReturn(null);

        // 执行测试
        PageConfigDataDTO result = rpcService.getPageConfig(null);

        // 验证结果
        assertNull("空参数应该返回null", result);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfig(null);
    }

    /**
     * 测试 getPageConfig 方法 - 服务抛出异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testGetPageConfigWithException() {
        // 模拟 productDetailPageConfigService 抛出异常
        when(productDetailPageConfigService.getPageConfig(validRoutingKey))
                .thenThrow(new IllegalArgumentException("页面配置不存在"));

        // 执行测试，期望抛出异常
        rpcService.getPageConfig(validRoutingKey);
    }

    /**
     * 测试 getAllPageRoutingKey 方法 - 正常场景
     */
    @Test
    public void testGetAllPageRoutingKeySuccess() {
        // 模拟 productDetailPageConfigService 返回路由key集合
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys())
                .thenReturn(mockRoutingKeys);

        // 执行测试
        Set<String> result = rpcService.getAllPageRoutingKey();

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回模拟的路由key集合", mockRoutingKeys, result);
        assertEquals("路由key数量应该正确", 4, result.size());
        assertTrue("应该包含默认路由key", result.contains("detail-default-1"));
        assertTrue("应该包含一级类目路由key", result.contains("detail-first-1-100"));
        assertTrue("应该包含二级类目路由key", result.contains("detail-second-1-200"));
        assertTrue("应该包含三级类目路由key", result.contains("detail-third-1-300"));

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getAllPageConfigTemplateCacheKeys();
    }

    /**
     * 测试 getAllPageRoutingKey 方法 - 返回空集合
     */
    @Test
    public void testGetAllPageRoutingKeyWithEmptySet() {
        // 模拟 productDetailPageConfigService 返回空集合
        Set<String> emptySet = new HashSet<>();
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys())
                .thenReturn(emptySet);

        // 执行测试
        Set<String> result = rpcService.getAllPageRoutingKey();

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertTrue("应该返回空集合", result.isEmpty());
        assertEquals("集合大小应该为0", 0, result.size());

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getAllPageConfigTemplateCacheKeys();
    }

    /**
     * 测试 getAllPageRoutingKey 方法 - 服务抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testGetAllPageRoutingKeyWithException() {
        // 模拟 productDetailPageConfigService 抛出异常
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys())
                .thenThrow(new RuntimeException("获取路由key失败"));

        // 执行测试，期望抛出异常
        rpcService.getAllPageRoutingKey();
    }

    /**
     * 测试不同类型的路由key参数
     */
    @Test
    public void testWithDifferentRoutingKeyTypes() {
        // 创建不同类型的路由key
        PageConfigRoutingKey dpRoutingKey = new PageConfigRoutingKey();
        dpRoutingKey.setScene("detail");
        dpRoutingKey.setProductType(2); // DP商品类型
        dpRoutingKey.setProductFirstCategoryId(500);

        PageConfigTemplateDTO dpTemplate = new PageConfigTemplateDTO();
        PageConfigDataDTO dpPageConfig = new PageConfigDataDTO();

        // 模拟服务返回
        when(productDetailPageConfigService.getPageConfigTemplate(dpRoutingKey))
                .thenReturn(dpTemplate);
        when(productDetailPageConfigService.getPageConfig(dpRoutingKey))
                .thenReturn(dpPageConfig);

        // 测试 queryConfigTemplate
        PageConfigTemplateDTO templateResult = rpcService.queryConfigTemplate(dpRoutingKey);
        assertNotNull("DP路由key应该返回配置模板", templateResult);
        assertEquals("应该返回DP配置模板", dpTemplate, templateResult);

        // 测试 getPageConfig
        PageConfigDataDTO configResult = rpcService.getPageConfig(dpRoutingKey);
        assertNotNull("DP路由key应该返回页面配置", configResult);
        assertEquals("应该返回DP页面配置", dpPageConfig, configResult);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(dpRoutingKey);
        verify(productDetailPageConfigService, times(1)).getPageConfig(dpRoutingKey);
    }

    /**
     * 测试边界条件 - 最小有效路由key
     */
    @Test
    public void testWithMinimalValidRoutingKey() {
        // 创建最小有效路由key
        PageConfigRoutingKey minimalKey = new PageConfigRoutingKey();
        minimalKey.setScene("detail");
        minimalKey.setProductType(1);

        PageConfigTemplateDTO minimalTemplate = new PageConfigTemplateDTO();
        PageConfigDataDTO minimalConfig = new PageConfigDataDTO();

        // 模拟服务返回
        when(productDetailPageConfigService.getPageConfigTemplate(minimalKey))
                .thenReturn(minimalTemplate);
        when(productDetailPageConfigService.getPageConfig(minimalKey))
                .thenReturn(minimalConfig);

        // 执行测试
        PageConfigTemplateDTO templateResult = rpcService.queryConfigTemplate(minimalKey);
        PageConfigDataDTO configResult = rpcService.getPageConfig(minimalKey);

        // 验证结果
        assertNotNull("最小路由key应该返回配置模板", templateResult);
        assertNotNull("最小路由key应该返回页面配置", configResult);
        assertEquals("应该返回最小配置模板", minimalTemplate, templateResult);
        assertEquals("应该返回最小页面配置", minimalConfig, configResult);

        // 验证方法调用
        verify(productDetailPageConfigService, times(1)).getPageConfigTemplate(minimalKey);
        verify(productDetailPageConfigService, times(1)).getPageConfig(minimalKey);
    }

    /**
     * 测试多次调用的一致性
     */
    @Test
    public void testMultipleCallsConsistency() {
        // 模拟服务返回
        when(productDetailPageConfigService.getPageConfigTemplate(validRoutingKey))
                .thenReturn(mockTemplate);
        when(productDetailPageConfigService.getPageConfig(validRoutingKey))
                .thenReturn(mockPageConfig);
        when(productDetailPageConfigService.getAllPageConfigTemplateCacheKeys())
                .thenReturn(mockRoutingKeys);

        // 多次调用相同方法
        PageConfigTemplateDTO template1 = rpcService.queryConfigTemplate(validRoutingKey);
        PageConfigTemplateDTO template2 = rpcService.queryConfigTemplate(validRoutingKey);
        PageConfigDataDTO config1 = rpcService.getPageConfig(validRoutingKey);
        PageConfigDataDTO config2 = rpcService.getPageConfig(validRoutingKey);
        Set<String> keys1 = rpcService.getAllPageRoutingKey();
        Set<String> keys2 = rpcService.getAllPageRoutingKey();

        // 验证结果一致性
        assertEquals("多次调用应该返回相同的配置模板", template1, template2);
        assertEquals("多次调用应该返回相同的页面配置", config1, config2);
        assertEquals("多次调用应该返回相同的路由key集合", keys1, keys2);

        // 验证方法调用次数
        verify(productDetailPageConfigService, times(2)).getPageConfigTemplate(validRoutingKey);
        verify(productDetailPageConfigService, times(2)).getPageConfig(validRoutingKey);
        verify(productDetailPageConfigService, times(2)).getAllPageConfigTemplateCacheKeys();
    }
}
