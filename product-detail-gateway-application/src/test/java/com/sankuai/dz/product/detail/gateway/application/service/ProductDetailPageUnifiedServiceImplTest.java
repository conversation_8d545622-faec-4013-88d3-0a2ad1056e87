package com.sankuai.dz.product.detail.gateway.application.service;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.application.exception.ProductDetailEntranceFatalException;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.data.merge.DataMergeMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.degrade.PageDegradeMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.message.MessageSendFunction;
import com.sankuai.dz.product.detail.gateway.domain.page.config.PageConfigMainFunction;
import com.sankuai.dz.product.detail.gateway.domain.param.transformer.RequestTransformer;
import com.sankuai.dz.product.detail.gateway.domain.post.AntiCrawlerHandler;
import com.sankuai.dz.product.detail.gateway.domain.spi.call.SPICallMainFunction;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailNotLoginException;
import com.sankuai.dz.product.detail.gateway.spi.exception.ProductDetailRequestIllegalException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductDetailPageUnifiedServiceImpl 单元测试
 * 目标覆盖率: >70%
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 16:00
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductDetailPageUnifiedServiceImplTest {

    @InjectMocks
    private ProductDetailPageUnifiedServiceImpl service;

    @Mock
    private PageConfigMainFunction pageConfigMainFunction;
    @Mock
    private SPICallMainFunction spiCallMainFunction;
    @Mock
    private DataMergeMainFunction dataMergeMainFunction;
    @Mock
    private RequestTransformer requestTransformer;
    @Mock
    private PageDegradeMainFunction pageDegradeMainFunction;
    @Mock
    private AntiCrawlerHandler antiCrawlerHandler;
    @Mock
    private MessageSendFunction messageSendFunction;

    private UnifiedPageRequest validRequest;
    private ProductDetailPageMergeResponse mockResponse;

    @Before
    public void setUp() {
        // 创建有效的请求对象
        validRequest = new UnifiedPageRequest();
        validRequest.setProductId(12345L);
        validRequest.setProductType(1);
        validRequest.setClientType(100);

        // 创建模拟的响应对象
        mockResponse = new ProductDetailPageMergeResponse();
        mockResponse.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        mockResponse.setMsg("success");
    }

    /**
     * 测试空请求参数场景
     */
    @Test
    public void testQueryWithNullRequest() {
        // 执行测试
        ProductDetailPageMergeResponse result = service.query(null);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证没有调用任何处理函数
        verify(requestTransformer, never()).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, never()).process(any(GatewayContext.class));
        verify(spiCallMainFunction, never()).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, never()).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, never()).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, never()).process(any(GatewayContext.class));
        verify(messageSendFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试未登录异常场景
     */
    @Test
    public void testQueryWithNotLoginException() {
        // 模拟 requestTransformer 抛出未登录异常
        doThrow(new ProductDetailNotLoginException("用户未登录"))
                .when(requestTransformer).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回未授权状态码", PageResponseCodeEnum.UNAUTHORIZED.getCode(), result.getCode());
        assertEquals("应该返回未授权描述", PageResponseCodeEnum.UNAUTHORIZED.getDesc(), result.getMsg());

        // 验证只调用了 requestTransformer
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试请求参数非法异常场景
     */
    @Test
    public void testQueryWithRequestIllegalException() {
        // 模拟 requestTransformer 抛出请求非法异常
        doThrow(new ProductDetailRequestIllegalException("请求参数非法"))
                .when(requestTransformer).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证只调用了 requestTransformer
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试入口致命异常场景
     */
    @Test
    public void testQueryWithEntranceFatalException() {
        // 模拟 pageConfigMainFunction 抛出入口致命异常
        doThrow(new ProductDetailEntranceFatalException("入口致命错误"))
                .when(pageConfigMainFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了 requestTransformer 和 pageConfigMainFunction
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试通用异常场景
     */
    @Test
    public void testQueryWithGeneralException() {
        // 模拟 spiCallMainFunction 抛出通用异常
        doThrow(new RuntimeException("通用运行时异常"))
                .when(spiCallMainFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了前三个处理函数
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试数据合并阶段异常
     */
    @Test
    public void testQueryWithDataMergeException() {
        // 模拟 dataMergeMainFunction 抛出异常
        doThrow(new IllegalStateException("数据合并异常"))
                .when(dataMergeMainFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了前四个处理函数
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试降级阶段异常
     */
    @Test
    public void testQueryWithDegradeException() {
        // 模拟 pageDegradeMainFunction 抛出异常
        doThrow(new NullPointerException("降级处理异常"))
                .when(pageDegradeMainFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了前五个处理函数
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试反爬虫处理阶段异常
     */
    @Test
    public void testQueryWithAntiCrawlerException() {
        // 模拟 antiCrawlerHandler 抛出异常
        doThrow(new IllegalArgumentException("反爬虫处理异常"))
                .when(antiCrawlerHandler).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了前六个处理函数
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, times(1)).process(any(GatewayContext.class));
        verify(messageSendFunction, never()).process(any(GatewayContext.class));
    }

    /**
     * 测试消息发送阶段异常
     */
    @Test
    public void testQueryWithMessageSendException() {
        // 模拟 messageSendFunction 抛出异常
        doThrow(new UnsupportedOperationException("消息发送异常"))
                .when(messageSendFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证调用了所有处理函数
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, times(1)).process(any(GatewayContext.class));
        verify(messageSendFunction, times(1)).process(any(GatewayContext.class));
    }

    /**
     * 测试成功场景 - 验证 GatewayContext 的创建和使用
     */
    @Test
    public void testQuerySuccessWithGatewayContextValidation() {
        // 准备模拟响应
        ProductDetailPageMergeResponse expectedResponse = new ProductDetailPageMergeResponse();
        expectedResponse.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        expectedResponse.setMsg("success");

        // 模拟所有处理函数正常执行，并验证 GatewayContext 参数
        doAnswer(invocation -> {
            GatewayContext context = invocation.getArgument(0);
            assertNotNull("GatewayContext 不应该为空", context);
            assertEquals("UnifiedRequest 应该正确设置", validRequest, context.getUnifiedRequest());
            return null;
        }).when(requestTransformer).process(any(GatewayContext.class));

        doAnswer(invocation -> {
            GatewayContext context = invocation.getArgument(0);
            // 模拟设置 pageResponse
            context.setPageResponse(expectedResponse);
            return null;
        }).when(dataMergeMainFunction).process(any(GatewayContext.class));

        doAnswer(invocation -> {
            GatewayContext context = invocation.getArgument(0);
            // 模拟设置 logMetricDimension
            Map<String, String> dimensions = new HashMap<>();
            dimensions.put("productId", "12345");
            dimensions.put("clientType", "100");
            context.setLogMetricDimension(dimensions);
            return null;
        }).when(pageConfigMainFunction).process(any(GatewayContext.class));

        doNothing().when(spiCallMainFunction).process(any(GatewayContext.class));
        doNothing().when(pageDegradeMainFunction).process(any(GatewayContext.class));
        doNothing().when(antiCrawlerHandler).process(any(GatewayContext.class));
        doNothing().when(messageSendFunction).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回预期的响应", expectedResponse, result);

        // 验证所有处理函数都被调用
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, times(1)).process(any(GatewayContext.class));
        verify(spiCallMainFunction, times(1)).process(any(GatewayContext.class));
        verify(dataMergeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(pageDegradeMainFunction, times(1)).process(any(GatewayContext.class));
        verify(antiCrawlerHandler, times(1)).process(any(GatewayContext.class));
        verify(messageSendFunction, times(1)).process(any(GatewayContext.class));
    }

    /**
     * 测试异常链 - ProductDetailRequestIllegalException 和 ProductDetailEntranceFatalException 组合
     */
    @Test
    public void testQueryWithCombinedExceptions() {
        // 模拟 requestTransformer 抛出 ProductDetailRequestIllegalException
        doThrow(new ProductDetailRequestIllegalException("请求参数非法",
                new ProductDetailEntranceFatalException("底层致命错误")))
                .when(requestTransformer).process(any(GatewayContext.class));

        // 执行测试
        ProductDetailPageMergeResponse result = service.query(validRequest);

        // 验证结果
        assertNotNull("结果不应该为空", result);
        assertEquals("应该返回失败状态码", PageResponseCodeEnum.FAILURE.getCode(), result.getCode());
        assertEquals("应该返回错误信息", "服务器开小差了，请稍后重试", result.getMsg());

        // 验证只调用了 requestTransformer
        verify(requestTransformer, times(1)).process(any(GatewayContext.class));
        verify(pageConfigMainFunction, never()).process(any(GatewayContext.class));
    }

}
