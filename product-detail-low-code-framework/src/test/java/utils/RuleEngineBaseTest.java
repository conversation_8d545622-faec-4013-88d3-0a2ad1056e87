package utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.AviatorRuleEngine;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RuleEngineBaseTest {

    public AviatorRuleEngine getAviatorRuleEngine() {
        AviatorEvaluatorInstance aviatorInstance = getAviatorEvaluatorInstance();
        aviatorInstance.addFunction(new GetProductAttrJSONValueFunction());
        aviatorInstance.addFunction(new GetProductAttributeFunction());
        return AviatorRuleEngine.getInstance();
    }

    private AviatorEvaluatorInstance getAviatorEvaluatorInstance() {
        try {
            // 假设你已经有了AviatorRuleEngine的实例
            AviatorRuleEngine instance = AviatorRuleEngine.getInstance();
            Class<?> aviatorRuleEngineClass = instance.getClass();
            Field aviatorInstanceField = aviatorRuleEngineClass.getDeclaredField("AVIATOR_INSTANCE");
            // 设置字段可访问
            aviatorInstanceField.setAccessible(true);
            // 获取字段值（静态字段，所以传null即可，不需要实例）
            AviatorEvaluatorInstance aviatorInstance = (AviatorEvaluatorInstance) aviatorInstanceField.get(null);
            return aviatorInstance;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 自定义函数：从属性列表中获取JSON值
     */
    public static class GetProductAttrJSONValueFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttrJSONValue";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            JSONObject productAttributeModel = (JSONObject) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);

            if (productAttributeModel != null && productAttributeModel.containsKey("attrs")) {
                JSONArray attrs = productAttributeModel.getJSONArray("attrs");
                if (attrs != null) {
                    for (int i = 0; i < attrs.size(); i++) {
                        JSONObject attribute = attrs.getJSONObject(i);
                        if (stringValue.equals(attribute.getString("attrName")) &&
                                attribute.containsKey("attrValues") &&
                                !attribute.getJSONArray("attrValues").isEmpty()) {

                            JSONObject attrValue = attribute.getJSONArray("attrValues").getJSONObject(0);
                            if (attrValue != null &&
                                    attrValue.containsKey("simpleValues") &&
                                    !attrValue.getJSONArray("simpleValues").isEmpty()) {

                                String jsonString = attrValue.getJSONArray("simpleValues").getString(0);
                                List<String> strings = JSON.parseArray(jsonString, String.class);
                                return AviatorRuntimeJavaType.valueOf(strings);
                            }
                        }
                    }
                }
            }
            return AviatorRuntimeJavaType.valueOf(new ArrayList<String>());
        }
    }

    /**
     * 自定义函数：从属性列表中获取属性值
     */
    public static class GetProductAttributeFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttribute";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            JSONObject productAttributeModel = (JSONObject) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);
            if (productAttributeModel != null && productAttributeModel.containsKey("attrs")) {
                JSONArray attrs = productAttributeModel.getJSONArray("attrs");
                if (attrs != null) {
                    for (int i = 0; i < attrs.size(); i++) {
                        JSONObject attribute = attrs.getJSONObject(i);
                        if (stringValue.equals(attribute.getString("attrName")) &&
                                attribute.containsKey("attrValues") &&
                                !attribute.getJSONArray("attrValues").isEmpty()) {
                            JSONObject attrValue = attribute.getJSONArray("attrValues").getJSONObject(0);
                            if (attrValue != null &&
                                    attrValue.containsKey("simpleValues") &&
                                    !attrValue.getJSONArray("simpleValues").isEmpty()) {
                                return new AviatorString(attrValue.getJSONArray("simpleValues").getString(0));
                            }
                        }
                    }
                }
            }
            return new AviatorString("");
        }
    }
}
