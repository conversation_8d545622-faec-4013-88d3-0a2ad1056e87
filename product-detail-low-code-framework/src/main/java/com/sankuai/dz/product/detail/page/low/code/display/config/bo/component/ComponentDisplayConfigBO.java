package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import lombok.Getter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 17:14
 */
@Getter
public abstract class ComponentDisplayConfigBO implements Serializable {

    /**
     * 模块key，全局唯一
     */
    private final String componentKey;

    protected ComponentDisplayConfigBO(final String componentKey) {
        this.componentKey = componentKey;
    }

    public abstract List<DataSourceConfigBO> getDataSourceConfigs();

    /**
     * 获取组件类型
     */
    public abstract ComponentGroupEnum getComponentGroup();

    /**
     * 获取支持的组件分类
     */
    public abstract Set<ComponentTypeEnum> getSupportedComponentType();

    /**
     * 构建ComponentBO
     * @param depth 递归深度，如果出现容器组件则需要递归
     */
    public abstract ComponentBO buildComponentBO(final Map<String, Object> data,
                                                 final int depth);

}
