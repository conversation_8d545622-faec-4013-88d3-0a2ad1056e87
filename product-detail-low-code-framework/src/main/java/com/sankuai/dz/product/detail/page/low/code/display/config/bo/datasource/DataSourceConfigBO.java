package com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource;

import lombok.Getter;

import java.io.Serializable;
import java.util.Collections;
import java.util.Set;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/5/27 15:07
 */
@Getter
public class DataSourceConfigBO implements Serializable {

    /**
     * fetcher名称
     */
    private final String fetcherName;

    /**
     * 按需索取字段，部分fetcher有效
     */
    private final Set<String> optionalFieldName;

    public DataSourceConfigBO(String fetcherName,
                              Set<String> optionalFieldName) {
        this.fetcherName = fetcherName;
        this.optionalFieldName = Collections.unmodifiableSet(optionalFieldName);
    }
}
