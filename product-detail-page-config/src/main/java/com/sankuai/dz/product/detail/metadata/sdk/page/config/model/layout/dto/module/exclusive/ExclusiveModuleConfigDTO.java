package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.exclusive;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.normal.NormalModuleConfigDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 11:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "互斥模块")
public class ExclusiveModuleConfigDTO extends ModuleConfigDTO {

    @FieldDoc(description = "互斥模块列表", requiredness = Requiredness.REQUIRED)
    private List<NormalModuleConfigDTO> moduleList;

    @JsonIgnore
    public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
        List<ModuleConfigDTO> all = new ArrayList<>();
        all.add(this);
        all.addAll(moduleList);
        return all;
    }

    @Override
    @JsonIgnore
    public ModuleConfigVO buildVO(final BuildModuleVORequest request, final int depth) {
        for (NormalModuleConfigDTO normalModuleConfigDTO : moduleList) {
            if (request.isModuleKeyResponseValid(normalModuleConfigDTO.getModuleKey())) {
                return normalModuleConfigDTO.buildVO(request, depth);
            }
        }
        return null;
    }

    @Override
    @JsonIgnore
    public ModuleTypeEnum getModuleType() {
        return ModuleTypeEnum.exclusive;
    }

}
