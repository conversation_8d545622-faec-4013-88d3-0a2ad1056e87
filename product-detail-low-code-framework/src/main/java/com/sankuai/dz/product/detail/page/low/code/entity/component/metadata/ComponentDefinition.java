package com.sankuai.dz.product.detail.page.low.code.entity.component.metadata;

import com.sankuai.athena.digital.arch.enums.DAFieldClassTypeEnum;
import com.sankuai.athena.digital.arch.enums.DAFieldTypeEnum;
import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.DAGenericTypeBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAFieldInfoBO;
import com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeMetadataFatalException;
import lombok.Getter;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 17:27
 */
@Getter
public class ComponentDefinition implements Serializable {

    private final String componentKey;

    private final Class<? extends ComponentBO> componentClass;

    private final String moduleKey;

    private final String desc;

    private final ComponentGroupEnum componentGroup;

    private final ComponentTypeEnum componentType;

    private final DAClassInfoBO classInfo;

    public ComponentDefinition(final Class<? extends ComponentBO> componentClass) {
        ComponentMetadata componentMetadata = componentClass.getAnnotation(ComponentMetadata.class);
        if (componentMetadata == null) {
            throw new LowCodeMetadataFatalException("组件没有标注@BizComponent:" + componentClass.getName());
        }
        DAClassInfoBO daClassInfoBO = DAClassInfoCache.getClassInfo(componentClass);
        if (daClassInfoBO == null) {
            throw new LowCodeMetadataFatalException("没有该类的元数据信息:" + componentClass.getName());
        }
        //校验Class属性是否符合要求
        checkClassField(daClassInfoBO, 0);
        this.componentKey = componentMetadata.componentKey();
        this.componentClass = componentClass;
        this.moduleKey = componentMetadata.moduleKey();
        this.desc = componentMetadata.desc();
        this.componentGroup = componentMetadata.componentGroup();
        this.componentType = componentMetadata.componentType();
        this.classInfo = daClassInfoBO;
    }

    /**
     * 组件定义的字段只允许是
     * ①基本类型，基本类型定义参考DAFieldClassTypeEnum
     * ②POJO，且POJO嵌套层数不能大于5层，否则会影响性能
     * ③List，List的泛型只能是①和②，不允许List嵌套
     *
     * @see DAFieldClassTypeEnum
     */
    private static void checkClassField(final DAClassInfoBO daClassInfoBO,
                                        final int depth) {
        if (depth >= ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new IllegalArgumentException(String.format("类型定义嵌套层数过深，最多%s层", ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH));
        }
        for (DAFieldInfoBO field : daClassInfoBO.getFieldInfoMap().values()) {
            checkType(field.getGenericType(), depth);
        }
    }

    private static void checkType(final DAGenericTypeBO genericType,
                                  final int depth) {
        DAFieldTypeEnum rawTypeEnum = genericType.getRawTypeEnum();
        if (rawTypeEnum == DAFieldTypeEnum.BASE_TYPE) {
            return;
        } else if (rawTypeEnum == DAFieldTypeEnum.COLLECTION) {
            if (genericType.getParameterizedTypeList().size() > 1) {
                throw new LowCodeMetadataFatalException("集合的泛型数量>1，不合法");
            }
            checkCollectionType(genericType.getParameterizedTypeList().get(0), depth);
        } else if (rawTypeEnum == DAFieldTypeEnum.POJO) {
            DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(genericType.getRawType().getTClass());
            if (subClassInfo == null) {
                throw new LowCodeMetadataFatalException("找不到POJO字段的元数据:" + genericType.getRawType().getTClass().getName());
            }
            checkClassField(subClassInfo, depth + 1);
        } else {
            throw new LowCodeMetadataFatalException("组件定义只允许出现①基本类型、②集合、③POJO、④枚举!!!");
        }
    }

    /**
     * List的泛型只能是①和②，不允许List嵌套
     */
    private static void checkCollectionType(final DAGenericTypeBO genericType,
                                            final int depth) {
        DAFieldTypeEnum rawTypeEnum = genericType.getRawTypeEnum();
        if (rawTypeEnum == DAFieldTypeEnum.BASE_TYPE) {
            return;
        } else if (rawTypeEnum == DAFieldTypeEnum.POJO) {
            DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(genericType.getRawType().getTClass());
            if (subClassInfo == null) {
                throw new LowCodeMetadataFatalException("找不到POJO字段的元数据:" + genericType.getRawType().getTClass().getName());
            }
            checkClassField(subClassInfo, depth + 1);
        } else {
            throw new LowCodeMetadataFatalException("集合的泛型只允许是①基本类型、②集合、④枚举!!!");
        }
    }

}
