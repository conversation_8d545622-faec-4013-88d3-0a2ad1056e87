package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition;

import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.constant.FetcherConstant;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDefinitionException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.utils.GenericTypeResolver;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/1/26 10:31
 */
@Getter
@SuppressWarnings("rawtypes")
public abstract class FetcherDefinition {//所有属性只读

    /**
     * Fetcher类型
     */
    public abstract FetcherTypeEnum getFetcherType();

    /**
     * fetcher唯一标识
     * 默认是FetcherClass#getSimpleName()，如果冲突会导致项目启动报错
     * 为什么不用fetcherClass作为唯一标识？
     * 主要是因为不同ClassLoader加载的Class会不相同，其次是Class<? extends BaseFetcher>太过于复杂
     */
    protected final String fetcherName;

    /**
     * fetcher的Class
     * 不能用作唯一标识，有可能ClassLoader不同，唯一标识只有fetcherName！！！
     */
    protected final Class<? extends BaseFetcherContext> fetcherClass;

    /**
     * Fetcher返回值类型，必须返回强类型
     */
    protected final Class<? extends FetcherReturnValueDTO> fetcherResponseClass;

    /**
     * 依赖的fetcher类型集合，用于构建DAG和编排执行顺序
     */
    public abstract Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG();

    /**
     * 依赖的fetcher名称集合，用于构建DAG和编排执行顺序
     */
    public abstract Set<String> getNameOfPreviousLayerDependenciesInDAG();

    /**
     * 是否是开始节点
     */
    protected final boolean isStartFetcher;

    /**
     * 超时时间
     */
    protected final long timeout;

    public FetcherDefinition(final Class<? extends BaseFetcherContext> fetcherClass,
                             final FetcherDefinitionInitParam initParam) throws Exception {
        if (fetcherClass == null) {
            throw new FetcherDefinitionException("初始化FetcherConfig的入参为空");
        }
        this.fetcherName = getFetcherName(fetcherClass);
        this.fetcherClass = fetcherClass;
        this.fetcherResponseClass = parseFetcherReturnValueClass(fetcherClass);
        this.isStartFetcher = initParam.isStartFetcher();
        this.timeout = Math.min(FetcherConstant.MAX_TIME_OUT, initParam.getTimeout());
    }

    /**
     * 获取BaseFetcher的returnValueClass，默认为第一个泛型
     */
    @SuppressWarnings("unchecked")
    private Class<? extends FetcherReturnValueDTO> parseFetcherReturnValueClass(Class<? extends BaseFetcherContext> fetcherClass) {
        Class<?> fetcherReturnValueClass = GenericTypeResolver.getGenericActualType(
                fetcherClass, BaseFetcherContext.class, 0
        );
        if (!FetcherReturnValueDTO.class.isAssignableFrom(fetcherReturnValueClass)) {
            throw new FetcherDefinitionException(String.format("FATAL ERROR!!!BaseFetcherContext(%s)的第一个泛型不是fetcher返回值类型了", fetcherClass.getName()));
        }
        return (Class<? extends FetcherReturnValueDTO>) fetcherReturnValueClass;
    }

    protected void checkPreviousLayerDependenciesInDAG() {
        if (!isStartFetcher && CollectionUtils.isEmpty(getClassOfPreviousLayerDependenciesInDAG())) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)不是起始点，但是无依赖", fetcherClass.getName()));
        }
        if (isStartFetcher && CollectionUtils.isNotEmpty(getClassOfPreviousLayerDependenciesInDAG())) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)是起始点，但是有依赖", fetcherClass.getName()));
        }
    }

    /**
     * 所有fetcherClass转fetcherName都必须使用这个方法
     */
    public static String getFetcherName(Class<? extends BaseFetcherContext> fetcherClass) {
        return fetcherClass.getSimpleName();
    }

}
