package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/2/3 09:44
 */
public class BaseFetcherContextTest {

    @Test
    public void test() {
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return "future1";
        }).exceptionally(throwable -> "");
        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(new Supplier<String>() {
            @Override
            public String get() {
                throw new IllegalStateException("sdfsdf");
            }
        }).exceptionally(throwable -> "future2");

        try {
            CompletableFuture.allOf(future1, future2).get(500, TimeUnit.MILLISECONDS);
        } catch (Throwable throwable) {
        }

        String future11;
        String future22;
        try {
            future11 = future1.get(500, TimeUnit.MILLISECONDS);
        } catch (Exception ignored) {
            future11 = "future1";
        }
        try {
            future22 = future2.get(500, TimeUnit.MILLISECONDS);
        } catch (Exception ignored) {
            future22 = "future2";
        }

        System.out.println(future11);
        System.out.println(future22);
        Assert.assertEquals(future11, "future1");
    }

}