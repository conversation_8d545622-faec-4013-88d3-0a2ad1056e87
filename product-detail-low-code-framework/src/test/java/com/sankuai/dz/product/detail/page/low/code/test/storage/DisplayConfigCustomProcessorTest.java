package com.sankuai.dz.product.detail.page.low.code.test.storage;

import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigCustomProcessor;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import org.junit.Test;

/**
 * @Author: guangyujie
 * @Date: 2025/6/13 15:44
 */
public class DisplayConfigCustomProcessorTest {

    @Test(expected = LowCodeConfigFatalException.class)
    public void preProcess() {
        DisplayConfigCustomProcessor.preProcess("sdfsdf", null);

    }
}