package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.reponse;

import com.sankuai.dz.product.detail.gateway.spi.dto.ABDetailDTO;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/2/3 14:27
 */
@Data
public class BuilderResponse<T extends AbstractModuleVO> {

    protected boolean success;

    protected Throwable error;

    private List<ABDetailDTO> abDetails;

    private T returnValue;

    public static <T extends AbstractModuleVO> BuilderResponse<T> succeed(T returnValue) {
        BuilderResponse<T> builderResponse = new BuilderResponse<>();
        builderResponse.setSuccess(true);
        builderResponse.setReturnValue(returnValue);
        return builderResponse;
    }

    public static <T extends AbstractModuleVO> BuilderResponse<T> succeed(T returnValue, List<ABDetailDTO> abDetails) {
        BuilderResponse<T> builderResponse = new BuilderResponse<>();
        builderResponse.setSuccess(true);
        builderResponse.setReturnValue(returnValue);
        builderResponse.setAbDetails(abDetails);
        return builderResponse;
    }

    public static <T extends AbstractModuleVO> BuilderResponse<T> fail(Throwable error) {
        BuilderResponse<T> builderResponse = new BuilderResponse<>();
        builderResponse.setSuccess(false);
        builderResponse.setError(error);
        return builderResponse;
    }

}
