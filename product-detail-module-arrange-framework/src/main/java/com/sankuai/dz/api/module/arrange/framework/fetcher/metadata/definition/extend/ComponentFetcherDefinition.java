package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDefinitionException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.utils.GenericTypeResolver;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/9 10:37
 */
@Getter
@SuppressWarnings("rawtypes")
public class ComponentFetcherDefinition extends FetcherDefinition {

    @Override
    public FetcherTypeEnum getFetcherType() {
        return FetcherTypeEnum.COMPONENT_FETCHER;
    }

    @Override
    public Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG() {
        return classOfBuildResultDependencies;
    }

    @Override
    public Set<String> getNameOfPreviousLayerDependenciesInDAG() {
        return nameOfBuildResultDependencies;
    }

    /**
     * 聚合Fetcher信息
     */
    private final Class<? extends AggregateFetcherContext> aggregateFetcherClass;
    private final String aggregateFetcherName;
    /**
     * 填充request的依赖项，会被聚合到aggregateFetcher上执行
     */
    private final Set<Class<? extends BaseFetcherContext>> classOfFillRequestDependencies;//已转换为只读
    private final Set<String> nameOfFillRequestDependencies;//已转换为只读
    /**
     * 构建result的依赖项，默认有aggregateFetcher，作为componentFetcher自己的依赖项
     */
    private final Set<Class<? extends BaseFetcherContext>> classOfBuildResultDependencies;//已转换为只读
    private final Set<String> nameOfBuildResultDependencies;//已转换为只读

    public ComponentFetcherDefinition(final Class<? extends ComponentFetcherContext> fetcherClass) throws Exception {
        super(fetcherClass, new FetcherDefinitionInitParam(fetcherClass, fetcherClass.getAnnotation(ComponentFetcher.class)));
        ComponentFetcher annotation = fetcherClass.getAnnotation(ComponentFetcher.class);
        this.aggregateFetcherClass = annotation.aggregateFetcher();
        this.aggregateFetcherName = getFetcherName(this.aggregateFetcherClass);
        this.classOfFillRequestDependencies = Collections.unmodifiableSet(parseClassOfDependencies("FillRequest"));
        this.nameOfFillRequestDependencies = Collections.unmodifiableSet(
                this.classOfFillRequestDependencies.stream().map(FetcherDefinition::getFetcherName).collect(Collectors.toSet())
        );
        //componentFetcher一定依赖aggregateFetcher
        Set<Class<? extends BaseFetcherContext>> buildResultDependencies = parseClassOfDependencies("BuildResult");
        buildResultDependencies.add(this.aggregateFetcherClass);
        this.classOfBuildResultDependencies = Collections.unmodifiableSet(buildResultDependencies);
        this.nameOfBuildResultDependencies = Collections.unmodifiableSet(
                this.classOfBuildResultDependencies.stream().map(FetcherDefinition::getFetcherName).collect(Collectors.toSet())
        );
        //REQUEST泛型校验，分别是ComponentBaseFetcher和AggregateBaseFetcher的第一个泛型
        checkComponentRequestClass();
        //AGGREGATE_RESULT泛型校验，分别是ComponentBaseFetcher和AggregateBaseFetcher的第二个泛型
        checkComponentAggregateResultClass();
        if (!isStartFetcher && CollectionUtils.isEmpty(getClassOfPreviousLayerDependenciesInDAG())) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)不是起始点，但是无依赖", fetcherClass.getName()));
        }
        if (isStartFetcher && CollectionUtils.isNotEmpty(getClassOfPreviousLayerDependenciesInDAG())) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)是起始点，但是有依赖", fetcherClass.getName()));
        }
        checkPreviousLayerDependenciesInDAG();
    }

    private Set<Class<? extends BaseFetcherContext>> parseClassOfDependencies(String type) {
        final Set<Class<? extends BaseFetcherContext>> dependencies = new HashSet<>();
        //循环解析
        Class<?> currentClass = this.fetcherClass;
        while (currentClass != null) {
            if (!ComponentFetcherContext.class.isAssignableFrom(currentClass)) {
                break;
            }
            ComponentFetcher annotation = currentClass.getAnnotation(ComponentFetcher.class);
            if (annotation != null) {
                if ("FillRequest".equals(type)) {
                    dependencies.addAll(Arrays.asList(annotation.fillRequestDependencies()));
                } else if ("BuildResult".equals(type)) {
                    dependencies.addAll(Arrays.asList(annotation.buildResultDependencies()));
                } else {
                    throw new FetcherDefinitionException("未知Dependencies类型:" + type);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        //转化为不可编辑的集合返回
        return dependencies;
    }

    /**
     /**
     * REQUEST泛型校验，分别是ComponentBaseFetcher和AggregateBaseFetcher的第一个泛型
     */
    public void checkComponentRequestClass() {
        Class<?> requestOfComponentFetcher = GenericTypeResolver.getGenericActualType(
                fetcherClass, ComponentFetcherContext.class, 0
        );
        Class<?> requestOfAggregateFetcher = GenericTypeResolver.getGenericActualType(
                aggregateFetcherClass, AggregateFetcherContext.class, 0
        );
        if (requestOfComponentFetcher != requestOfAggregateFetcher) {
            throw new FetcherDefinitionException(String.format(
                    "ComponentFetcher(%s)的REQUEST泛型类型不等于其对应的AggregateFetcher(%s)的REQUEST泛型类型",
                    fetcherClass.getName(), aggregateFetcherClass.getName()
            ));
        }
    }

    /**
     * AGGREGATE_RESULT泛型校验，分别是ComponentBaseFetcher和AggregateBaseFetcher的第二个泛型
     */
    public void checkComponentAggregateResultClass() {
        Class<?> aggregateResultOfComponentFetcher = GenericTypeResolver.getGenericActualType(
                fetcherClass, ComponentFetcherContext.class, 1
        );
        Class<?> aggregateResultOfAggregateFetcher = GenericTypeResolver.getGenericActualType(
                aggregateFetcherClass, AggregateFetcherContext.class, 1
        );
        if (aggregateResultOfComponentFetcher != aggregateResultOfAggregateFetcher) {
            throw new FetcherDefinitionException(String.format(
                    "ComponentFetcher(%s)的AGGREGATE_RESULT泛型类型不等于其对应的AggregateFetcher(%s)的AGGREGATE_RESULT泛型类型",
                    fetcherClass.getName(), aggregateFetcherClass.getName()
            ));
        }
    }

}
