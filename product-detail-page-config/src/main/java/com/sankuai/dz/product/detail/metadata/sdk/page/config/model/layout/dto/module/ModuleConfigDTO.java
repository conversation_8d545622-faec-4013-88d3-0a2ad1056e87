package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 11:12
 */
@Data
@TypeDoc(description = "模块配置")
public abstract class ModuleConfigDTO implements Serializable {

    @FieldDoc(description = "模块key", rule = "整个页面全局唯一", requiredness = Requiredness.REQUIRED,
            typeName = "参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护")
    private String moduleKey;

    @FieldDoc(description = "模块类型", requiredness = Requiredness.REQUIRED)
    @JsonIgnore
    public abstract ModuleTypeEnum getModuleType();

    /**
     * 递归获取页面配置树上所有模块信息
     */
    @JsonIgnore
    public List<ModuleConfigDTO> getAllModuleConfigs() {
        return getAllModuleConfigs(0);
    }

    /**
     * 子类实现，递归获取页面配置树上所有模块信息
     */
    @JsonIgnore
    public abstract List<ModuleConfigDTO> getAllModuleConfigs(final int depth);

    /**
     * 递归构建ModuleVO
     * 可能返回null，需要过滤
     */
    public abstract ModuleConfigVO buildVO(final BuildModuleVORequest request, final int depth);

    /**
     * 是否是业务模块
     */
    @JsonIgnore
    public boolean isBizModule() {
        return this instanceof BizModuleConfigDTO;
    }

}
