package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.ModuleCoverRule;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

public class ModuleCoverHandlerTest {

    /**
     * 测试coverModule为null时方法直接返回不做任何操作
     */
    @Test
    public void testMergeWhenCoverModuleIsNull() {
        // arrange
        ModuleCoverHandler handler = new ModuleCoverHandler();
        PageConfigDataDTO mainConfigData = mock(PageConfigDataDTO.class);
        ModuleCoverRule rule = mock(ModuleCoverRule.class);
        when(rule.getCoverModule()).thenReturn(null);
        // act
        handler.merge(mainConfigData, rule);
        // assert
        verify(mainConfigData, never()).getModuleGroup(anyString(), anyString());
    }

    /**
     * 测试找不到对应ModuleGroupConfigDTO时方法直接返回不做任何操作
     */
    @Test
    public void testMergeWhenModuleGroupNotFound() {
        // arrange
        ModuleCoverHandler handler = new ModuleCoverHandler();
        PageConfigDataDTO mainConfigData = mock(PageConfigDataDTO.class);
        ModuleCoverRule rule = mock(ModuleCoverRule.class);
        ModuleConfigDTO coverModule = mock(ModuleConfigDTO.class);
        when(rule.getCoverModule()).thenReturn(coverModule);
        when(coverModule.getModuleKey()).thenReturn("testKey");
        when(rule.getGroupName()).thenReturn("testGroup");
        when(mainConfigData.getModuleGroup("testGroup", "testKey")).thenReturn(null);
        // act
        handler.merge(mainConfigData, rule);
        // assert
        verify(mainConfigData).getModuleGroup("testGroup", "testKey");
    }

    /**
     * 测试找到ModuleGroupConfigDTO但找不到对应moduleKey时不进行替换
     */
    @Test
    public void testMergeWhenModuleKeyNotMatch() {
        // arrange
        ModuleCoverHandler handler = new ModuleCoverHandler();
        PageConfigDataDTO mainConfigData = mock(PageConfigDataDTO.class);
        ModuleCoverRule rule = mock(ModuleCoverRule.class);
        ModuleConfigDTO coverModule = mock(ModuleConfigDTO.class);
        ModuleGroupConfigDTO moduleGroup = mock(ModuleGroupConfigDTO.class);
        ModuleConfigDTO existingModule = mock(ModuleConfigDTO.class);
        List<ModuleConfigDTO> moduleList = new ArrayList<>();
        moduleList.add(existingModule);
        when(rule.getCoverModule()).thenReturn(coverModule);
        when(coverModule.getModuleKey()).thenReturn("newKey");
        when(rule.getGroupName()).thenReturn("testGroup");
        when(mainConfigData.getModuleGroup("testGroup", "newKey")).thenReturn(moduleGroup);
        when(moduleGroup.getModuleList()).thenReturn(moduleList);
        when(existingModule.getModuleKey()).thenReturn("oldKey");
        // act
        handler.merge(mainConfigData, rule);
        // assert
        assertEquals(1, moduleList.size());
        assertEquals(existingModule, moduleList.get(0));
    }

    /**
     * 测试正常替换module的情况
     */
    @Test
    public void testMergeSuccessfully() {
        // arrange
        ModuleCoverHandler handler = new ModuleCoverHandler();
        PageConfigDataDTO mainConfigData = mock(PageConfigDataDTO.class);
        ModuleCoverRule rule = mock(ModuleCoverRule.class);
        ModuleConfigDTO coverModule = mock(ModuleConfigDTO.class);
        ModuleGroupConfigDTO moduleGroup = mock(ModuleGroupConfigDTO.class);
        ModuleConfigDTO existingModule = mock(ModuleConfigDTO.class);
        List<ModuleConfigDTO> moduleList = new ArrayList<>();
        moduleList.add(existingModule);
        when(rule.getCoverModule()).thenReturn(coverModule);
        when(coverModule.getModuleKey()).thenReturn("testKey");
        when(rule.getGroupName()).thenReturn("testGroup");
        when(mainConfigData.getModuleGroup("testGroup", "testKey")).thenReturn(moduleGroup);
        when(moduleGroup.getModuleList()).thenReturn(moduleList);
        when(existingModule.getModuleKey()).thenReturn("testKey");
        // act
        handler.merge(mainConfigData, rule);
        // assert
        assertEquals(1, moduleList.size());
        assertEquals(coverModule, moduleList.get(0));
    }
}
