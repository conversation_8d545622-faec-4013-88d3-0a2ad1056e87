package com.sankuai.dz.product.detail.gateway.spi.response;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.mockito.Mockito;

public class ProductDetailPageResponseTest {

    /**
     * Test buildResponse with all modules successful.
     */
    @Test
    public void testBuildResponseAllModulesSuccessful() throws Throwable {
        // arrange
        ModuleResponse module1 = Mockito.mock(ModuleResponse.class);
        when(module1.isSuccess()).thenReturn(true);
        when(module1.getModuleKey()).thenReturn("module1");
        ModuleResponse module2 = Mockito.mock(ModuleResponse.class);
        when(module2.isSuccess()).thenReturn(true);
        when(module2.getModuleKey()).thenReturn("module2");
        List<ModuleResponse> moduleList = Arrays.asList(module1, module2);
        List<ABResultDTO> abResultList = new ArrayList<>();
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(moduleList, abResultList);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(2, response.getModuleResponse().size());
        assertEquals(module1, response.getModuleResponse().get("module1"));
        assertEquals(module2, response.getModuleResponse().get("module2"));
    }

    /**
     * Test buildResponse with some modules failing.
     */
    @Test
    public void testBuildResponseSomeModulesFailing() throws Throwable {
        // arrange
        ModuleResponse module1 = Mockito.mock(ModuleResponse.class);
        when(module1.isSuccess()).thenReturn(true);
        when(module1.getModuleKey()).thenReturn("module1");
        ModuleResponse module2 = Mockito.mock(ModuleResponse.class);
        when(module2.isSuccess()).thenReturn(false);
        when(module2.getModuleKey()).thenReturn("module2");
        List<ModuleResponse> moduleList = Arrays.asList(module1, module2);
        List<ABResultDTO> abResultList = new ArrayList<>();
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(moduleList, abResultList);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.PARTLY_FAIL.getCode(), response.getCode());
        assertEquals(2, response.getModuleResponse().size());
        assertEquals(module1, response.getModuleResponse().get("module1"));
        assertEquals(module2, response.getModuleResponse().get("module2"));
    }

    /**
     * Test buildResponse with null module list.
     */
    @Test
    public void testBuildResponseWithNullModuleList() throws Throwable {
        // arrange
        List<ABResultDTO> abResultList = new ArrayList<>();
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(new ArrayList<>(), abResultList);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getModuleResponse());
        assertEquals(0, response.getModuleResponse().size());
    }

    /**
     * Test buildResponse with empty module list.
     */
    @Test
    public void testBuildResponseWithEmptyModuleList() throws Throwable {
        // arrange
        List<ModuleResponse> moduleList = new ArrayList<>();
        List<ABResultDTO> abResultList = new ArrayList<>();
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(moduleList, abResultList);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertNotNull(response.getModuleResponse());
        assertEquals(0, response.getModuleResponse().size());
    }

    /**
     * Test buildResponse with modules having null or blank keys.
     */
    @Test
    public void testBuildResponseModulesWithNullOrBlankKeys() throws Throwable {
        // arrange
        ModuleResponse module1 = Mockito.mock(ModuleResponse.class);
        when(module1.isSuccess()).thenReturn(true);
        when(module1.getModuleKey()).thenReturn(null);
        ModuleResponse module2 = Mockito.mock(ModuleResponse.class);
        when(module2.isSuccess()).thenReturn(true);
        when(module2.getModuleKey()).thenReturn("");
        ModuleResponse module3 = Mockito.mock(ModuleResponse.class);
        when(module3.isSuccess()).thenReturn(true);
        when(module3.getModuleKey()).thenReturn("validKey");
        List<ModuleResponse> moduleList = Arrays.asList(module1, module2, module3);
        List<ABResultDTO> abResultList = new ArrayList<>();
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(moduleList, abResultList);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(1, response.getModuleResponse().size());
        assertEquals(module3, response.getModuleResponse().get("validKey"));
    }

    /**
     * Test buildResponse with null ABResultList.
     */
    @Test
    public void testBuildResponseWithNullABResultList() throws Throwable {
        // arrange
        ModuleResponse module = Mockito.mock(ModuleResponse.class);
        when(module.isSuccess()).thenReturn(true);
        when(module.getModuleKey()).thenReturn("module1");
        List<ModuleResponse> moduleList = Arrays.asList(module);
        // act
        ProductDetailPageResponse response = ProductDetailPageResponse.buildResponse(moduleList, null);
        // assert
        assertNotNull(response);
        assertEquals(PageResponseCodeEnum.SUCCESS.getCode(), response.getCode());
        assertEquals(1, response.getModuleResponse().size());
        assertNotNull(response.getModuleResponse());
        assertEquals(module, response.getModuleResponse().get("module1"));
    }
}
