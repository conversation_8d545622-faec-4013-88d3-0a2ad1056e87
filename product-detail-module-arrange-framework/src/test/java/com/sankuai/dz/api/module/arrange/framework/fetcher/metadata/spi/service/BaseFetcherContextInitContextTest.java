package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.Optional;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;

@ExtendWith(MockitoExtension.class)
@DisplayName("BaseFetcherContext checkFutureAndSetDefaultFuture Tests")
class BaseFetcherContextInitContextTest {

    @Mock
    private FetcherEngine fetcherEngine;

    @Mock
    private FetcherDefinition fetcherDefinition;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private DAG dag;

    @Spy
    @InjectMocks
    private TestBaseFetcherContext baseFetcherContext;

    /**
     * Test successful initialization scenario
     */
    @Test
    public void testInitContext_SuccessfulInitialization() throws Exception {
        // arrange
        TestFetcherContext testContext = new TestFetcherContext();
        Map<String, BaseFetcherContext> contextMap = new HashMap<>();
        FetcherContextInitResult expectedResult = new FetcherContextInitResult(testContext);
        when(fetcherEngine.getRequest()).thenReturn(request);
        when(fetcherEngine.getFetcherContextMap()).thenReturn(contextMap);
        when(fetcherEngine.getDag()).thenReturn(dag);
        // act
        FetcherContextInitResult result = testContext.initContext(fetcherDefinition, fetcherEngine);
        // assert
        assertNotNull(result);
        assertEquals(testContext, result.getSelf());
        assertEquals(fetcherDefinition, testContext.getFetcherDefinition());
        assertEquals(dag, testContext.getDag());
        assertTrue(testContext.getStartTime() > 0);
    }

    /**
     * Test initialization when fetcherDefinition is already set
     */
    @Test
    public void testInitContext_AlreadyInitialized() {
        // arrange
        TestFetcherContext testContext = new TestFetcherContext();
        testContext.fetcherDefinition = fetcherDefinition;
        // act & assert
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> testContext.initContext(fetcherDefinition, fetcherEngine));
        assertEquals("DAG遍历中Fetcher不能重复初始化", exception.getMessage());
    }

    /**
     * Test scenario where doInitContext throws exception but error handling succeeds
     */
    @Test
    public void testInitContext_InitFailsWithSuccessfulErrorHandling() throws Exception {
        // arrange
        TestFetcherContext testContext = spy(new TestFetcherContext());
        Exception testException = new RuntimeException("Test exception");
        FetcherContextInitResult errorResult = new FetcherContextInitResult(testContext);
        when(fetcherEngine.getRequest()).thenReturn(request);
        when(fetcherEngine.getFetcherContextMap()).thenReturn(new HashMap<>());
        when(fetcherEngine.getDag()).thenReturn(dag);
        when(fetcherDefinition.getFetcherName()).thenReturn("testFetcher");
        doThrow(testException).when(testContext).doInitContext(any());
        doReturn(errorResult).when(testContext).doInitContextExceptionally(any(), any());
        // act
        FetcherContextInitResult result = testContext.initContext(fetcherDefinition, fetcherEngine);
        // assert
        assertNotNull(result);
        assertEquals(errorResult, result);
        verify(testContext).doInitContextExceptionally(fetcherEngine, testException);
    }

    /**
     * Test scenario where both normal initialization and error handling fail
     */
    @Test
    public void testInitContext_BothInitAndErrorHandlingFail() throws Exception {
        // arrange
        TestFetcherContext testContext = spy(new TestFetcherContext());
        RuntimeException initException = new RuntimeException("Init failed");
        RuntimeException errorHandlingException = new RuntimeException("Error handling failed");
        when(fetcherEngine.getRequest()).thenReturn(request);
        when(fetcherEngine.getFetcherContextMap()).thenReturn(new HashMap<>());
        when(fetcherEngine.getDag()).thenReturn(dag);
        when(fetcherDefinition.getFetcherName()).thenReturn("testFetcher");
        doThrow(initException).when(testContext).doInitContext(any());
        doThrow(errorHandlingException).when(testContext).doInitContextExceptionally(any(), any());
        // act
        FetcherContextInitResult result = testContext.initContext(fetcherDefinition, fetcherEngine);
        // assert
        assertNotNull(result);
        assertEquals(testContext, result.getSelf());
        verify(testContext).doInitContext(fetcherEngine);
        verify(testContext).doInitContextExceptionally(fetcherEngine, initException);
    }

    // Test implementation of BaseFetcherContext
    private static class TestFetcherContext extends BaseFetcherContext<FetcherReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) throws Exception {
            return new FetcherContextInitResult(this);
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return new FetcherContextInitResult(this);
        }
    }

    @Test
    public void testGetDependencyResultWhenSuccessWithNonNullValue() {
        // arrange
        TestReturnValueDTO expectedResult = new TestReturnValueDTO();
        FetcherResponse<FetcherReturnValueDTO> response = new FetcherResponse<>();
        response.setSuccess(true);
        response.setReturnValue(expectedResult);
        doReturn(response).when(baseFetcherContext).getDependencyResponse(any());
        // act
        Optional<TestReturnValueDTO> result = baseFetcherContext.getDependencyResult(TestDependencyFetcherContext.class, TestReturnValueDTO.class);
        // assert
        assertTrue(result.isPresent());
        assertEquals(expectedResult, result.get());
    }

    @Test
    public void testGetDependencyResultWhenSuccessWithNullValue() {
        // arrange
        FetcherResponse<FetcherReturnValueDTO> response = new FetcherResponse<>();
        response.setSuccess(true);
        response.setReturnValue(null);
        doReturn(response).when(baseFetcherContext).getDependencyResponse(any());
        // act
        Optional<TestReturnValueDTO> result = baseFetcherContext.getDependencyResult(TestDependencyFetcherContext.class, TestReturnValueDTO.class);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testGetDependencyResultWhenFetchFails() {
        // arrange
        FetcherResponse<FetcherReturnValueDTO> response = new FetcherResponse<>();
        response.setSuccess(false);
        response.setError(new RuntimeException("Fetch failed"));
        doReturn(response).when(baseFetcherContext).getDependencyResponse(any());
        // act
        Optional<TestReturnValueDTO> result = baseFetcherContext.getDependencyResult(TestDependencyFetcherContext.class, TestReturnValueDTO.class);
        // assert
        assertFalse(result.isPresent());
    }

    private static class TestBaseFetcherContext extends BaseFetcherContext<TestReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    private static class TestDependencyFetcherContext extends BaseFetcherContext<TestReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    private static class TestReturnValueDTO extends FetcherReturnValueDTO {
    }

    @Test
    @DisplayName("Should set default failure future when future is null")
    void testCheckFutureAndSetDefaultFuture_WhenFutureIsNull() throws Throwable {
        // arrange
        TestBaseFetcherContextImpl context = new TestBaseFetcherContextImpl();
        assertNull(context.getFuture(), "Future should be null initially");
        // act
        context.checkFutureAndSetDefaultFuture();
        // assert
        CompletableFuture<FetcherResponse<TestFetcherReturnValueDTO>> resultFuture = context.getFuture();
        assertNotNull(resultFuture, "Future should not be null after check");
        assertTrue(resultFuture.isDone(), "Future should be completed");
        FetcherResponse<?> response = resultFuture.get();
        assertNotNull(response, "Response should not be null");
        assertFalse(response.isSuccess(), "Response should indicate failure");
        assertTrue(response.getError() instanceof FetcherFatalException, "Error should be FetcherFatalException");
        assertEquals("Fetcher's future is null", response.getError().getMessage(), "Error message should match");
    }

    @Test
    @DisplayName("Should preserve existing future when future is not null")
    void testCheckFutureAndSetDefaultFuture_WhenFutureExists() throws Throwable {
        // arrange
        TestBaseFetcherContextImpl context = new TestBaseFetcherContextImpl();
        TestFetcherReturnValueDTO returnValue = new TestFetcherReturnValueDTO();
        FetcherResponse<TestFetcherReturnValueDTO> successResponse = FetcherResponse.succeed(returnValue);
        CompletableFuture<FetcherResponse<TestFetcherReturnValueDTO>> existingFuture = CompletableFuture.completedFuture(successResponse);
        context.future = existingFuture;
        // act
        context.checkFutureAndSetDefaultFuture();
        // assert
        assertSame(existingFuture, context.getFuture(), "Future instance should remain the same");
        FetcherResponse<?> response = context.getFuture().get();
        assertTrue(response.isSuccess(), "Response should indicate success");
        assertSame(returnValue, response.getReturnValue(), "Return value should be preserved");
    }

class TestFetcherReturnValueDTO extends FetcherReturnValueDTO {
}

@Fetcher(isStartFetcher = false, timeout = 1000L)
class TestNormalFetcherContext extends NormalFetcherContext<TestFetcherReturnValueDTO> {

    @Override
    protected CompletableFuture<TestFetcherReturnValueDTO> doFetch() throws Exception {
        return null;
    }
}

class TestFetcherDefinition extends FetcherDefinition {

    public TestFetcherDefinition() throws Exception {
        super(TestNormalFetcherContext.class, new FetcherDefinitionInitParam(TestNormalFetcherContext.class, TestNormalFetcherContext.class.getAnnotation(Fetcher.class)));
    }

    @Override
    public FetcherTypeEnum getFetcherType() {
        return FetcherTypeEnum.valueOf("NORMAL");
    }

    @Override
    public Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG() {
        return Collections.emptySet();
    }

    @Override
    public Set<String> getNameOfPreviousLayerDependenciesInDAG() {
        return Collections.emptySet();
    }
}

class TestBaseFetcherContextImpl extends BaseFetcherContext<TestFetcherReturnValueDTO, TestFetcherDefinition> {

    @Override
    protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
        return null;
    }

    @Override
    protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
        return null;
    }

    /**
     * Expose the protected future field for testing
     */
    @Override
    public CompletableFuture<FetcherResponse<TestFetcherReturnValueDTO>> getFuture() {
        return super.getFuture();
    }
}
}
