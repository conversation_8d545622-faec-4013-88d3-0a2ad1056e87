package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.ModuleCoverRule;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:14
 */
public class ModuleCoverHandler implements ConfigMergeHandler<ModuleCoverRule> {

    @Override
    public void merge(final PageConfigDataDTO mainConfigData, final ModuleCoverRule rule) {
        if (rule.getCoverModule() == null) {
            return;
        }
        ModuleGroupConfigDTO moduleGroupConfigDTO = mainConfigData.getModuleGroup(
                rule.getGroupName(), rule.getCoverModule().getModuleKey()
        );
        if (moduleGroupConfigDTO == null) {
            return;
        }
        for (int i = 0; i < moduleGroupConfigDTO.getModuleList().size(); i++) {
            ModuleConfigDTO moduleConfigDTO = moduleGroupConfigDTO.getModuleList().get(i);
            if (moduleConfigDTO.getModuleKey().equals(rule.getCoverModule().getModuleKey())) {
                moduleGroupConfigDTO.getModuleList().set(i, rule.getCoverModule());
            }
        }
    }

    @Override
    @JsonIgnore
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_COVER;
    }

}
