package com.sankuai.dz.product.detail.gateway.spi.response.generic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Unit tests for GenericProductDetailPageResponse.fail() method
 */
@RunWith(MockitoJUnitRunner.class)
public class GenericProductDetailPageResponseTest {

    @Mock
    private List<ABResultDTO> mockAbResultList;

    @Mock
    private Map<String, GenericModuleResponse> mockModuleResponse;

    /**
     * Test normal case with non-empty fail reason
     */
    @Test
    public void testFailWithNonEmptyReason() throws Throwable {
        // arrange
        String failReason = "Service unavailable";
        // act
        GenericProductDetailPageResponse response = GenericProductDetailPageResponse.fail(failReason);
        // assert
        assertNotNull("Response should not be null", response);
        assertEquals("Code should match FAILURE code", PageResponseCodeEnum.FAILURE.getCode(), response.getCode());
        assertEquals("Message should match input reason", failReason, response.getMsg());
        assertEquals("Duration should be 0", 0, response.getDuration());
        assertNull("abResultList should be null", response.getAbResultList());
        assertNull("moduleResponse should be null", response.getModuleResponse());
    }

    /**
     * Test boundary case with empty string as fail reason
     */
    @Test
    public void testFailWithEmptyReason() throws Throwable {
        // arrange
        String failReason = "";
        // act
        GenericProductDetailPageResponse response = GenericProductDetailPageResponse.fail(failReason);
        // assert
        assertNotNull("Response should not be null", response);
        assertEquals("Code should match FAILURE code", PageResponseCodeEnum.FAILURE.getCode(), response.getCode());
        assertEquals("Message should be empty string", failReason, response.getMsg());
        assertEquals("Duration should be 0", 0, response.getDuration());
        assertNull("abResultList should be null", response.getAbResultList());
        assertNull("moduleResponse should be null", response.getModuleResponse());
    }

    /**
     * Test boundary case with null as fail reason
     */
    @Test
    public void testFailWithNullReason() throws Throwable {
        // arrange
        String failReason = null;
        // act
        GenericProductDetailPageResponse response = GenericProductDetailPageResponse.fail(failReason);
        // assert
        assertNotNull("Response should not be null", response);
        assertEquals("Code should match FAILURE code", PageResponseCodeEnum.FAILURE.getCode(), response.getCode());
        assertNull("Message should be null", response.getMsg());
        assertEquals("Duration should be 0", 0, response.getDuration());
        assertNull("abResultList should be null", response.getAbResultList());
        assertNull("moduleResponse should be null", response.getModuleResponse());
    }

    /**
     * Test builder pattern with mocked dependencies
     */
    @Test
    public void testFailWithMockedDependencies() throws Throwable {
        // arrange
        String failReason = "Mocked test";
        GenericProductDetailPageResponse mockResponse = mock(GenericProductDetailPageResponse.class);
        // act
        GenericProductDetailPageResponse response = GenericProductDetailPageResponse.fail(failReason);
        // assert
        assertNotNull("Response should not be null", response);
        assertEquals("Code should match FAILURE code", PageResponseCodeEnum.FAILURE.getCode(), response.getCode());
        assertEquals("Message should match input reason", failReason, response.getMsg());
        assertEquals("Duration should be 0", 0, response.getDuration());
        assertNull("abResultList should be null", response.getAbResultList());
        assertNull("moduleResponse should be null", response.getModuleResponse());
    }
}
