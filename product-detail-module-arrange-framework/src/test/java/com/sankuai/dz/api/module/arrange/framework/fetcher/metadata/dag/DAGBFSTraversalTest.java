package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGGraphCut;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAGBFSUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/1/31 13:20
 */
public class DAGBFSTraversalTest {

    @Test
    public void test() {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A", "E"));
            put("A", Sets.newHashSet("B", "C", "F"));
            put("B", Sets.newHashSet("D"));
            put("C", Sets.newHashSet("D"));
            put("D", Sets.newHashSet("F", "Z"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("Z"));
            put("Z", Sets.newHashSet());
        }};
        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        DAGGraphCut.DAGGraphCutResult DAGGraphCutResult = DAGGraphCut.graphCut("O", childrenGraph, parentGraph);
        System.out.println(JSON.toJSONString(DAGGraphCutResult.getChildrenGraph()));
        System.out.println(JSON.toJSONString(DAGGraphCutResult.getParentGraph()));
        System.out.println(JSON.toJSONString(DAGGraphCutResult.getTopologicalSort()));
        Assert.assertNotNull(DAGGraphCutResult.getChildrenGraph());
    }


}