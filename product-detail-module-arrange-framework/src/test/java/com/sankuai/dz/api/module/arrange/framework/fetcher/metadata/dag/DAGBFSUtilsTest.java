package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/7 16:26
 */
public class DAGBFSUtilsTest {

    @Test
    public void test() {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A", "E"));
            put("A", Sets.newHashSet("B", "C", "F"));
            put("B", Sets.newHashSet("D"));
            put("C", Sets.newHashSet("D"));
            put("D", Sets.newHashSet("F", "Z"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("Z"));
            put("Z", Sets.newHashSet());
        }};

        Map<String, Set<String>> parentGraph = DAGBFSUtils.convertChildrenGraphToParentGraph(childrenGraph);
        Map<String, Set<String>> childrenGraph2 = DAGBFSUtils.convertParentGraphToChildrenGraph(parentGraph);

        System.out.println(JSON.toJSONString(childrenGraph));
        System.out.println(JSON.toJSONString(parentGraph));
        System.out.println(JSON.toJSONString(childrenGraph2));
        System.out.println(JSON.toJSONString(childrenGraph).equals(JSON.toJSONString(childrenGraph2)));
        Assert.assertNotNull(childrenGraph2);
    }

}