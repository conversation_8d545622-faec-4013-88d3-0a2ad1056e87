import com.sankuai.dz.product.detail.page.low.code.engine.DisplayRuleEngineFactory;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.AviatorRuleEngine;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.DetailRuleEngine;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.GroovyRuleEngine;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.RuleEngineBaseTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则引擎使用示例类
 * 展示如何使用规则引擎
 */
public class RuleSimpleTest extends RuleEngineBaseTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleSimpleTest.class);

    /**
     * 使用Aviator规则引擎示例
     */
    @Test
    public void aviatorExample() {
        // 准备上下文数据
        Map<String, Object> context = new HashMap<>();
        context.put("baseTime", 60);
        context.put("extraTime", 30);

        // 方式一：通过工厂类调用
        // 执行简单表达式
        String expression = "baseTime + extraTime";
        Integer result = DisplayRuleEngineFactory.executeAviatorRule(expression, context, Integer.class);
        LOGGER.info("Aviator expression result: {}", result); // 输出: 90
        // 执行条件表达式
        String conditionExpression = "baseTime > 50 ? 'Long service' : 'Short service'";
        String conditionResult = DisplayRuleEngineFactory.executeAviatorRule(conditionExpression, context, String.class);
        LOGGER.info("Aviator condition result: {}", conditionResult); // 输出: Long service

        // 数值类型转换示例
        String numericExpression = "baseTime * 1.5";
        // 转换为不同的数值类型
        Integer intResult = AviatorRuleEngine.executeRule(numericExpression, context, Integer.class);
        Long longResult = AviatorRuleEngine.executeRule(numericExpression, context, Long.class);
        Double doubleResult = AviatorRuleEngine.executeRule(numericExpression, context, Double.class);
        Float floatResult = AviatorRuleEngine.executeRule(numericExpression, context, Float.class);

        LOGGER.info("Numeric conversion examples:");
        LOGGER.info("Integer result: {} ({})", intResult, intResult.getClass().getSimpleName());
        LOGGER.info("Long result: {} ({})", longResult, longResult.getClass().getSimpleName());
        LOGGER.info("Double result: {} ({})", doubleResult, doubleResult.getClass().getSimpleName());
        LOGGER.info("Float result: {} ({})", floatResult, floatResult.getClass().getSimpleName());
    }

    /**
     * 使用Groovy规则引擎示例
     */
    @Test
    public void groovyExample() {
        // 准备上下文数据
        Map<String, Object> context = new HashMap<>();
        context.put("product", new ProductInfo("美容服务", 60, 30, true));

        // 方式一：通过工厂类调用
        // 执行简单脚本
        String script = "return product.baseTime + product.extraTime";
        Integer result = DisplayRuleEngineFactory.executeGroovyRule(script, context, Integer.class);
        LOGGER.info("Groovy script result: {}", result); // 输出: 90

        // 方式二：直接调用GroovyRuleEngine
        // 执行复杂脚本
        String complexScript =
                "def steps = []\n" +
                        "if (product.hasExtraService) {\n" +
                        "    steps.add(['name': '前置服务', 'time': 15])\n" +
                        "}\n" +
                        "steps.add(['name': '主服务', 'time': product.baseTime])\n" +
                        "steps.add(['name': '后续服务', 'time': product.extraTime])\n" +
                        "return steps";
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> complexResult = GroovyRuleEngine.executeRule(complexScript, context, List.class);
        LOGGER.info("Groovy complex result: {}", complexResult);
        // 输出类似: [
        //   {name=前置服务, time=15},
        //   {name=主服务, time=60},
        //   {name=后续服务, time=30}
        // ]

        // 数值类型转换示例
        String numericScript = "return product.baseTime * 1.5";
        // 转换为不同的数值类型
        Integer intResult = GroovyRuleEngine.executeRule(numericScript, context, Integer.class);
        Long longResult = GroovyRuleEngine.executeRule(numericScript, context, Long.class);
        Double doubleResult = GroovyRuleEngine.executeRule(numericScript, context, Double.class);
        Float floatResult = GroovyRuleEngine.executeRule(numericScript, context, Float.class);

        LOGGER.info("Numeric conversion examples:");
        LOGGER.info("Integer result: {} ({})", intResult, intResult.getClass().getSimpleName());
        LOGGER.info("Long result: {} ({})", longResult, longResult.getClass().getSimpleName());
        LOGGER.info("Double result: {} ({})", doubleResult, doubleResult.getClass().getSimpleName());
        LOGGER.info("Float result: {} ({})", floatResult, floatResult.getClass().getSimpleName());
    }

    /**
     * 缓存统计示例
     */
    @Test
    public void cacheStatsExample() {
        // 执行一些表达式，填充缓存
        Map<String, Object> context = new HashMap<>();
        context.put("value", 100);

        for (int i = 0; i < 10; i++) {
            AviatorRuleEngine.executeRule("value * " + i, context, Integer.class);
            GroovyRuleEngine.executeRule("return value * " + i, context, Integer.class);
        }

        // 获取缓存统计信息
        LOGGER.info("Aviator cache stats: {}", AviatorRuleEngine.getCacheStats());
    }

    /**
     * 主方法，运行示例
     */

    @Test
    public void dynamicEngineExampleTest() {

        // 运行动态引擎示例
        Map<String, Object> context = new HashMap<>();
        context.put("value", 100);
        dynamicEngineExample(RuleEngineType.AVIATOR.getCode(), "value * 2", context);
        dynamicEngineExample(RuleEngineType.GROOVY.getCode(), "return value * 3", context);
    }

    /**
     * 根据类型选择规则引擎示例
     */
    public static void dynamicEngineExample(String engineType, String expression, Map<String, Object> context) {
        // 根据类型获取规则引擎
        DetailRuleEngine engine = DisplayRuleEngineFactory.getEngine(engineType);

        // 执行表达式
        Object result = engine.execute(expression, context);
        LOGGER.info("Dynamic engine result: {}", result);
    }

    /**
     * 产品信息示例类
     */
    public static class ProductInfo {
        private String name;
        private int baseTime;
        private int extraTime;
        private boolean hasExtraService;

        public ProductInfo(String name, int baseTime, int extraTime, boolean hasExtraService) {
            this.name = name;
            this.baseTime = baseTime;
            this.extraTime = extraTime;
            this.hasExtraService = hasExtraService;
        }

        public String getName() {
            return name;
        }

        public int getBaseTime() {
            return baseTime;
        }

        public int getExtraTime() {
            return extraTime;
        }

        public boolean isHasExtraService() {
            return hasExtraService;
        }
    }
}