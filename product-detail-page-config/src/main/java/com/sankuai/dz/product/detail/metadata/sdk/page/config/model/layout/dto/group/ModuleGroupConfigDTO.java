package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/16 11:11
 */
@Data
@TypeDoc(description = "模块组配置")
public class ModuleGroupConfigDTO implements Serializable {

    @FieldDoc(description = "模块组名称", requiredness = Requiredness.REQUIRED)
    private String groupName;

    @FieldDoc(description = "模块列表", requiredness = Requiredness.REQUIRED)
    private List<ModuleConfigDTO> moduleList = new ArrayList<>();

}
