package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.extend;

import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAFieldInfoBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.field.BaseTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.field.FieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.field.PojoTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.engine.DisplayRuleEngineFactory;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeProcessFatalException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/6/5 20:26
 */
@Getter
public class StructComponentDisplayConfigBO extends BizComponentDisplayConfigBO {

    /**
     * 组件数据模型，内部递归实现Pojo嵌套，其元数据来源于代码定义（被@BizComponent注解标记的类）
     */
    private final List<FieldDisplayConfigBO> fieldConfigs;

    public StructComponentDisplayConfigBO(final String componentKey,
                                          final Map<String, DataSourceConfigBO> dataSource,
                                          final List<FieldDisplayConfigBO> fieldConfigs) {
        super(componentKey, dataSource);
        this.fieldConfigs = Collections.unmodifiableList(fieldConfigs);
    }

    @Override
    public List<RuleConfigDTO> getAllRuleConfigs() {
        return getAllBaseTypeFieldDisplayConfigs(this.getComponentKey(), this.getFieldConfigs(), 0);
    }

    /**
     * 递归获取所有基本类型字段配置
     * 业务组件BO的Class定义嵌套层数不能大于X层
     * X参考com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant#BIZ_COMPONENT_MAX_RECURSION_DEPTH
     */
    private static List<RuleConfigDTO> getAllBaseTypeFieldDisplayConfigs(final String componentKey,
                                                                         final List<FieldDisplayConfigBO> fieldConfigs,
                                                                         final int depth) {
        if (depth >= ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new LowCodeProcessFatalException(String.format(
                    "Fatal Error!!!类定义(componentKey=%s)嵌套层数过深，最多%s层",
                    componentKey, ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH
            ));
        }
        List<RuleConfigDTO> result = new ArrayList<>();
        for (FieldDisplayConfigBO fieldConfig : fieldConfigs) {
            if (fieldConfig.isPojo()) {
                result.addAll(
                        getAllBaseTypeFieldDisplayConfigs(
                                componentKey,
                                ((PojoTypeFieldDisplayConfigBO) fieldConfig).getFieldConfigs(),
                                depth + 1
                        )
                );
            } else {
                result.add(((BaseTypeFieldDisplayConfigBO) fieldConfig).getRuleConfig());
            }
        }
        return result;
    }

    /**
     * 遍历组件BO类结构，按照字段维度进行赋值方式，用于M端表单配置，for产运
     * 处理业务组件内部赋值，业务组件内部不能嵌套容器组件和其他业务组件，故只需要递归业务组件BO进行赋值即可
     * 业务组件BO的Class定义嵌套层数不能大于X层
     * X参考com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant#BIZ_COMPONENT_MAX_RECURSION_DEPTH
     */
    @Override
    public BizComponentBO processBizComponent(final ComponentDefinition componentDefinition,
                                              final Map<String, Object> data) {
        return (BizComponentBO) doProcessClass(
                this.getFieldConfigs(), componentDefinition.getClassInfo(), data, 0
        );
    }

    private Object doProcessClass(final List<FieldDisplayConfigBO> fieldConfigs,
                                  final DAClassInfoBO classInfo,
                                  final Map<String, Object> data,
                                  final int depth) {
        if (depth >= ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new LowCodeProcessFatalException(String.format(
                    "Fatal Error!!!类定义(class=%s)嵌套层数过深，最多%s层，理论上不会发生，启动时会校验",
                    classInfo.getTClass().getName(), ComponentConstant.BIZ_COMPONENT_MAX_RECURSION_DEPTH
            ));
        }
        Object classObject = classInfo.createInstance();
        for (final FieldDisplayConfigBO fieldConfig : fieldConfigs) {
            final DAFieldInfoBO daFieldInfoBO = classInfo.getFieldInfoMap().get(fieldConfig.getFieldName());
            if (daFieldInfoBO == null) {
                throw new LowCodeProcessFatalException(String.format(
                        "Fatal Error!!!元数据(class=%s)中无法找到配置中的字段(field=%s)，理论上不会发生，启动时会校验",
                        classInfo.getTClass().getName(), fieldConfig.getFieldName()
                ));
            }
            Object fieldObject = doProcessField(classInfo, daFieldInfoBO, fieldConfig, data, depth);
            if (fieldObject != null) {
                daFieldInfoBO.doSetMethod(classObject, fieldObject);
            }
        }
        return classObject;
    }

    private Object doProcessField(final DAClassInfoBO classInfo,
                                  final DAFieldInfoBO daFieldInfoBO,
                                  final FieldDisplayConfigBO fieldConfig,
                                  final Map<String, Object> data,
                                  final int depth) {
        if (fieldConfig.isPojo()) {
            PojoTypeFieldDisplayConfigBO pojoTypeFieldConfig = (PojoTypeFieldDisplayConfigBO) fieldConfig;
            DAClassInfoBO subClassInfo = DAClassInfoCache.getClassInfo(daFieldInfoBO.getGenericType().getRawType().getTClass());
            if (subClassInfo == null) {
                throw new LowCodeProcessFatalException(String.format(
                        "Fatal Error!!!元数据(class=%s)中无法找到POJO类型字段(field=%s)的元数据，理论上不会发生，启动时会校验",
                        classInfo.getTClass().getName(), fieldConfig.getFieldName()
                ));
            }
            return doProcessClass(
                    pojoTypeFieldConfig.getFieldConfigs(), subClassInfo, data, depth + 1
            );
        } else {
            BaseTypeFieldDisplayConfigBO baseTypeFieldConfig = (BaseTypeFieldDisplayConfigBO) fieldConfig;
            return DisplayRuleEngineFactory.execute(
                    baseTypeFieldConfig.getRuleConfig().getRuleEngineType().getCode(),
                    baseTypeFieldConfig.getRuleConfig().getExpression(),
                    data
            );
        }
    }

}
