package com.sankuai.dz.api.module.arrange.framework.starter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.component.annotation.BizComponent;
import com.sankuai.dz.api.module.arrange.framework.component.storage.ComponentDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.management.rpc.MetadataManagementRpcServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

@ExtendWith(MockitoExtension.class)
class ModuleArrangeFrameworkStarterTest {

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private ModuleArrangeFrameworkStarter starter;


    /**
     * Test when applicationContext is null
     */
    @Test
    public void testAfterPropertiesSetNullApplicationContext() throws Throwable {
        // arrange
        ModuleArrangeFrameworkStarter starter = new ModuleArrangeFrameworkStarter();
        // act & assert
        Exception exception = assertThrows(NullPointerException.class, () -> starter.afterPropertiesSet());
        assertNotNull(exception);
    }
}
