package com.sankuai.dz.product.detail.gateway.domain.page.config.handler;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.page.config.PageConfigBaseHandler;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/4/24 11:40
 */
@Component
public class PageConfigOldDealHandler extends PageConfigBaseHandler {

    @Override
    public Set<PageRegionEnum> getSupportedPageRegion() {
        return Sets.newHashSet(PageRegionEnum.OLD_DETAIL_PAGE_COMPATIBLE);
    }

    @Override
    protected void checkPageConfig(final PageConfigDataDTO pageConfig,
                                   final Map<String, BizModuleConfigDTO> bizModuleMap) throws SPIConfigFatalException {
        //老团详兼容不需要检查页面配置，如果没有配置则不需要走到新团详模块
    }

    @Override
    protected PageConfigSceneEnum getPageConfigSceneEnum() {
        return PageConfigSceneEnum.OldProductDetail;
    }

    @Override
    protected boolean isNeed(UnifiedPageRequest unifiedPageRequest, ProductDetailPageRequest detailPageRequest, BizModuleConfigDTO bizModuleConfigDTO) {
        return true;//老团详没有什么定制逻辑，配了什么模块就返回什么，也不支持指定模块
    }

}
