package com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.ModuleLevelEnum;

import java.lang.annotation.*;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/3/7 13:55
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ModuleLevel {

    /**
     * 模块等级，用于降级策略，也可以计算出依赖Fetcher的重要性
     */
    ModuleLevelEnum moduleLevel() default ModuleLevelEnum.COMMON_DISPLAY_MODULE;

}
