package com.sankuai.dz.product.detail.page.low.code.test.config;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.struct.field.PojoTypeFieldDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.version.ProductVersionConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.utils.json.JacksonUtils;
import com.sankuai.dz.product.detail.page.low.code.engine.dto.RuleConfigDTO;
import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.HashSet;

/**
 * @Author: guangyujie
 * @Date: 2025/6/10 21:59
 */

public class 桑拿洗浴汗蒸_ProductDetail_SecondCategory_1_304_Groovy extends BaseConfigTester {

    @Test
    public void testConfig() {
        ModuleDisplayConfigBO moduleDisplayConfigBO = buildStructuredDetailConfig(
                new ProductVersionConfigBO(20408612L, 76L, null),
//                buildCardContainer(
//                        buildStructBizComponent(
//                                "Description",
//                                Lists.newArrayList(
//                                        buildBaseTypeField("description", "'测试固定文案返回'")
//                                ),
//                                new DataSourceConfigBO("ProductAttrFetcher", Sets.newHashSet("aaa", "bbb", "ccc")),
//                                new DataSourceConfigBO("ProductOriginDetailFetcher", new HashSet<>())
//                        )
//                ),
//                buildStructBizComponent(
//                        "Service_Project_MN",
//                        Lists.newArrayList(
//                                buildBaseTypeField("titleWithPrefixDot", "'浴资票'"),
//                                buildBaseTypeFieldByGroovy("optionalCount", "2"),
//                                new PojoTypeFieldDisplayConfigBO(
//                                        "serviceProjectItems",
//                                        true,
//                                        new RuleConfigDTO(RuleEngineType.GROOVY, ""),
//                                        Lists.newArrayList(
//                                                buildBaseTypeField("itemName", "'浴资票itemName'"),
//                                                buildBaseTypeField("itemDesc", "'浴资票itemDesc'"),
//                                                buildBaseTypeField("itemExtra", "'浴资票itemExtra'")
//                                        )
//                                )
//                        ),
//                        new DataSourceConfigBO("ProductStandardServiceProjectFetcher", new HashSet<>())
//                ),
                buildAICodingComponentGroovy(
                        "Service_Project_MN_List",
                        "import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.builder.newstructureddetail.component.biz.text.ServiceProjectMNListComponentBO\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.common.BathCommonComponent0\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathThirdCategoryEnum\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory\n" +
                                "import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject\n" +
                                "import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO\n" +
                                "\n" +
                                "import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.BATH_TICKET_1\n" +
                                "import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.BathServiceProjectEnum.BATH_TICKET_2\n" +
                                "\n" +
                                "ProductCategory productCategory = ProductCategoryFetcher;\n" +
                                "ProductStandardServiceProject standardServiceProject = ProductStandardServiceProjectFetcher;\n" +
                                "def ticket = buildBathTicket(productCategory, standardServiceProject)\n" +
                                "if (ticket.isPresent()) {\n" +
                                "    return ticket.get();\n" +
                                "}\n" +
                                "return null;\n" +
                                "\n" +
                                "static Optional<ServiceProjectMNListComponentBO> buildBathTicket(ProductCategory productCategory, ProductStandardServiceProject standardServiceProject) {\n" +
                                "    if (productCategory == null || standardServiceProject == null) {\n" +
                                "        return Optional.empty();\n" +
                                "    }\n" +
                                "    int thirdCategoryId = productCategory.getProductThirdCategoryId();\n" +
                                "    if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET.getCategoryId()) {\n" +
                                "        return BathCommonComponent0.buildServiceProject(\n" +
                                "                standardServiceProject,\n" +
                                "                new BathCommonComponent0.ServiceProjectBuilder() {\n" +
                                "                    @Override\n" +
                                "                    Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {\n" +
                                "                        return BathCommonComponent0.buildServiceProjectItem(serviceProjectItem, projectType)\n" +
                                "                    }\n" +
                                "                },\n" +
                                "                BATH_TICKET_1\n" +
                                "        );\n" +
                                "    } else if (thirdCategoryId == BathThirdCategoryEnum.BATH_TICKET_AND_IN_STORE_SERVICE.getCategoryId()) {\n" +
                                "        return BathCommonComponent0.buildServiceProject(\n" +
                                "                standardServiceProject,\n" +
                                "                new BathCommonComponent0.ServiceProjectBuilder() {\n" +
                                "                    @Override\n" +
                                "                    Optional<ComponentBO> build(StandardServiceProjectItemDTO serviceProjectItem, BathServiceProjectEnum projectType) {\n" +
                                "                        return BathCommonComponent0.buildServiceProjectItem(serviceProjectItem, projectType)\n" +
                                "                    }\n" +
                                "                },\n" +
                                "                BATH_TICKET_2\n" +
                                "        );\n" +
                                "    } else {\n" +
                                "        return Optional.empty();\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "\n",
                        new DataSourceConfigBO("ProductCategoryFetcher", Sets.newHashSet()),
                        new DataSourceConfigBO("ProductStandardServiceProjectFetcher", Sets.newHashSet())
                )
        );
        System.out.println(JacksonUtils.serialize(buildPageConfig(
                moduleDisplayConfigBO
        )));
        assert moduleDisplayConfigBO != null;
    }

}