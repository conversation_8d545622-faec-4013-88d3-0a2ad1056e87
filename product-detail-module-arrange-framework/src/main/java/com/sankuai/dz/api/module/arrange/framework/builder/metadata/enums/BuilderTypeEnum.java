package com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.BuilderFactoryDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.CommonBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.VariableBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/3/6 17:19
 */
@SuppressWarnings("rawtypes")
@Getter
public enum BuilderTypeEnum {

    //Builder支持组合模式，当业务逻辑特别复杂的时候替代继承的模式
    COMPONENT_BUILDER("组件Builder，用于实现组合，代替继承",
            null, null
    ),

    //Builder支持继承多态，通常可以用来实现模板方法等简单的设计模式
    ABSTRACT_BUILDER("builder父类，用于沉淀通用逻辑，一般用于实现模板方法",
            null, null),

    //简单Builder，无设计模式
    COMMON_BUILDER("普通Builder，一个ModuleKey对应一个CommonBuilder",
            CommonBuilderDefinition.class, BaseBuilder.class),

    //泛型Builder，配合设计模式使用，可以理解为一个moduleKey有多个实现，运行时才能确定具体实现的Builder
    VARIABLE_BUILDER("泛型Builder，一个ModuleKey对应多个VariableBuilder",
            VariableBuilderDefinition.class, BaseBuilder.class),

    //设计模式一：工厂方法 + 策略模式
    BUILDER_FACTORY("工厂方法，BuilderFactory负责创建一个VariableBuilder实例",
            BuilderFactoryDefinition.class, BaseBuilderFactory.class),

    //设计模式二：抽象工厂方法 + 多策略模式编排
    ABSTRACT_BUILDER_FACTORY("抽象工厂方法,AbstractBuilderFactory负责创建一组VariableBuilder实例",
            null, null),

    //设计模式三：责任链
    @Deprecated
    BUILDER_CHAIN("暂时不支持，也不建议使用，可读性差", null, null);

    private final String desc;

    private final Class<? extends BuilderDefinition> definitionClass;

    private final Class<? extends AbstractBuilder> builderBaseClass;

    BuilderTypeEnum(final String desc,
                    final Class<? extends BuilderDefinition> definitionClass,
                    final Class<? extends AbstractBuilder> builderBaseClass) {
        this.desc = desc;
        this.definitionClass = definitionClass;
        this.builderBaseClass = builderBaseClass;
    }

}