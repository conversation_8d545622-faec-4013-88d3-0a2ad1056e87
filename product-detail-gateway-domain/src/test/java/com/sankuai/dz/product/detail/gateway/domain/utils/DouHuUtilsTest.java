package com.sankuai.dz.product.detail.gateway.domain.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.client.IDouHuClient;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DouHuUtilsTest {

    @Mock
    private IDouHuClient douHuClient;

    @Before
    public void setUp() throws Exception {
        // Set the mock douHuClient using reflection
        Field field = DouHuUtils.class.getDeclaredField("douHuClient");
        field.setAccessible(true);
        field.set(null, douHuClient);
    }

    /**
     * Test case: douHuClient is null
     * Expected: Return null when douHuClient is null
     */
    @Test
    public void testTryAbDouHuClientIsNull() throws Throwable {
        // Set douHuClient to null
        Field field = DouHuUtils.class.getDeclaredField("douHuClient");
        field.setAccessible(true);
        field.set(null, null);
        String expId = "expId";
        Map<String, Object> env = new HashMap<>();
        DouHuResponse result = DouHuUtils.tryAb(expId, env);
        assertNull(result);
    }

    /**
     * Test case: env parameter is null
     * Expected: Throw NullPointerException when env is null
     */
    @Test(expected = NullPointerException.class)
    public void testTryAbEnvIsNull() throws Throwable {
        String expId = "expId";
        Map<String, Object> env = null;
        DouHuUtils.tryAb(expId, env);
    }

    /**
     * Test case: env is empty
     * Expected: Return DouHuResponse with error code when env is empty
     */
    @Test
    public void testTryAbEnvIsEmpty() throws Throwable {
        String expId = "expId";
        Map<String, Object> env = new HashMap<>();
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode("-9");
        mockResponse.setMsg("实验配置不存在");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        DouHuResponse result = DouHuUtils.tryAb(expId, env);
        assertNotNull(result);
        assertEquals("-9", result.getCode());
        assertEquals("实验配置不存在", result.getMsg());
    }

    /**
     * Test case: env is missing required parameters
     * Expected: Return DouHuResponse with error code when required parameters are missing
     */
    @Test
    public void testTryAbEnvMissingParams() throws Throwable {
        String expId = "expId";
        Map<String, Object> env = new HashMap<>();
        env.put("key", "value");
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode("-9");
        mockResponse.setMsg("实验配置不存在");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        DouHuResponse result = DouHuUtils.tryAb(expId, env);
        assertNotNull(result);
        assertEquals("-9", result.getCode());
        assertEquals("实验配置不存在", result.getMsg());
    }

    /**
     * Test case: env contains all required parameters
     * Expected: Successfully call douHuClient.tryAb and return response
     */
    @Test
    public void testTryAbEnvContainsAllParams() throws Throwable {
        String expId = "expId";
        Map<String, Object> env = new HashMap<>();
        env.put("unionId", "unionId");
        env.put("deviceId", "deviceId");
        env.put("userId", "userId");
        env.put("uuid", "uuid");
        env.put("cityId", "cityId");
        env.put("dpId", "dpId");
        env.put("platform", "platform");
        DouHuResponse mockResponse = new DouHuResponse();
        mockResponse.setCode("0");
        mockResponse.setMsg("success");
        when(douHuClient.tryAb(any(DouHuRequest.class))).thenReturn(mockResponse);
        DouHuResponse result = DouHuUtils.tryAb(expId, env);
        assertNotNull(result);
        assertEquals("0", result.getCode());
        assertEquals("success", result.getMsg());
        verify(douHuClient, times(1)).tryAb(any(DouHuRequest.class));
    }
}
