package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.extend.AggregateFetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/7 10:24
 */
@Slf4j
@Getter
@SuppressWarnings("rawtypes")
public abstract class AggregateFetcherContext<
        REQUEST,//component和aggregate共用的request类型
        AGGREGATE_RESULT extends FetcherReturnValueDTO//component和aggregate共用的returnValue类型
        > extends BaseFetcherContext<AGGREGATE_RESULT, AggregateFetcherDefinition> {

    private REQUEST aggregateRequest;

    /**
     * 初始化REQUEST，提供给Component赋值
     */
    protected abstract REQUEST initRequest();

    /**
     * 初始化future，提供给Component编排
     */
    protected abstract CompletableFuture<FetcherResponse<AGGREGATE_RESULT>> initFuture(final REQUEST request);

    @Override
    @SuppressWarnings("unchecked")
    protected FetcherContextInitResult doInitContext(final FetcherEngine fetcherEngine) {
        //找到本次请求涉及的ComponentFetcher
        Set<String> targetFetcherNames = fetcherEngine.getTargetFetcherNames();
        Set<String> targetComponentFetcherNames = this.fetcherDefinition.getComponentFetcherNames().stream()
                .filter(targetFetcherNames::contains).collect(Collectors.toSet());
        //初始化request
        this.aggregateRequest = initRequest();
        //初始化本次请求涉及的ComponentFetcher，并填充aggregateRequest
        List<ComponentFetcherContext> componentFetcherContexts = new ArrayList<>();
        for (String targetComponentFetcherName : targetComponentFetcherNames) {
            ComponentFetcherContext baseFetcherContext = preInitComponentFetcherContext(
                    fetcherEngine, targetComponentFetcherName, this.aggregateRequest
            );
            if (baseFetcherContext != null) {//发生异常不影响其他ComponentFetcher执行
                componentFetcherContexts.add(baseFetcherContext);
            }
        }
        //初始化future
        this.future = initFuture(this.aggregateRequest);
        for (ComponentFetcherContext componentFetcherContext : componentFetcherContexts) {
            componentFetcherContext.buildFuture(this.future);//每个ComponentFetcher把自己编排到aggregateFuture上
        }
        //返回AggregateFetcher和所有ComponentFetcher，一并添加到FetcherEngine，这样ComponentFetcher就不会重复init并直接执行fetch
        return new FetcherContextInitResult(
                this,
                componentFetcherContexts.stream()
                        .map(context -> (BaseFetcherContext) context)
                        .collect(Collectors.toList())
        );
    }

    @SuppressWarnings("unchecked")
    private ComponentFetcherContext preInitComponentFetcherContext(final FetcherEngine fetcherEngine,
                                                                   final String targetComponentFetcherName,
                                                                   final REQUEST request) {
        try {
            //这些方法不会抛出异常，都是启动时校验过的
            FetcherDefinition componentFetcherDefinition = FetcherDefinitionStorage.getFetcherDefinition(targetComponentFetcherName);
            BaseFetcherContext componentFetcherContext = FetcherDefinitionStorage.getFetcherContext(componentFetcherDefinition);
            FetcherContextInitResult fetcherContextInitResult = componentFetcherContext.initContext(componentFetcherDefinition, fetcherEngine);
            ComponentFetcherContext self = (ComponentFetcherContext) fetcherContextInitResult.getSelf();
            //填充request，如果抛出异常则跳过该ComponentFetcher
            self.fulfillRequest(request);
            return self;
        } catch (Throwable throwable) {
            //一般不会走到这里，preInitInAggregateFetcher内部会有try-catch，其他方法如果失败就是严重的预期外异常了
            log.error("预期外异常导致提前初始化ComponentFetcher({})失败", targetComponentFetcherName, new FetcherFatalException(throwable));
            return null;
        }
    }

    /**
     * 初始化异常兜底，为了不打断整个流程，一定要对初始化异常的情况做Fetcher维度的降级，不能抛出异常
     */
    @Override
    protected FetcherContextInitResult doInitContextExceptionally(final FetcherEngine fetcherEngine,
                                                                  final Throwable throwable) {
        this.future = CompletableFuture.completedFuture(FetcherResponse.fail(throwable));
        return new FetcherContextInitResult(this);
    }

}
