package com.sankuai.dz.api.module.arrange.framework.management.utils;

import com.google.common.collect.Sets;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.HashSet;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * @Author: guangyujie
 * @Date: 2025/2/25 14:04
 */
@ExtendWith(MockitoExtension.class)
public class DagToDotConverterTest {

    public static void main(String[] args) {
        /**************** 示例1：简单DAG ****************/
        Map<String, Set<String>> childrenGraph = new HashMap<String, Set<String>>() {{
            put("O", Sets.newHashSet("A", "W"));
            put("A", Sets.newHashSet("B", "C", "D", "E", "F"));
            put("B", Sets.newHashSet("C", "D", "E", "F"));
            put("C", Sets.newHashSet("D", "E", "F"));
            put("D", Sets.newHashSet("E", "F"));
            put("E", Sets.newHashSet("F"));
            put("F", Sets.newHashSet("Z"));
            put("Z", Sets.newHashSet());
            put("W", Sets.newHashSet("Z"));
            put("M", Sets.newHashSet("N"));
            put("N", Sets.newHashSet("L"));
            put("L", Sets.newHashSet());
        }};
        String s = DagToDotConverter.generateDot(childrenGraph);
        System.out.println(s);
    }


    private String normalizeDotString(String dot) {
        return dot.trim().replaceAll("\\s+", " ");
    }

    @Test
    void testGenerateDot_EmptyGraph() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_SingleNode() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        adjacencyMatrix.put("A", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_SimpleDag() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> aEdges = new HashSet<>();
        aEdges.add("B");
        aEdges.add("C");
        adjacencyMatrix.put("A", aEdges);
        adjacencyMatrix.put("B", new HashSet<>());
        adjacencyMatrix.put("C", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"A\" -> \"B\" \"A\" -> \"C\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_ComplexDag() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> aEdges = new HashSet<>();
        aEdges.add("B");
        aEdges.add("C");
        adjacencyMatrix.put("A", aEdges);
        Set<String> bEdges = new HashSet<>();
        bEdges.add("D");
        bEdges.add("E");
        adjacencyMatrix.put("B", bEdges);
        Set<String> cEdges = new HashSet<>();
        cEdges.add("E");
        cEdges.add("F");
        adjacencyMatrix.put("C", cEdges);
        adjacencyMatrix.put("D", new HashSet<>());
        adjacencyMatrix.put("E", new HashSet<>());
        adjacencyMatrix.put("F", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"A\" -> \"B\" \"A\" -> \"C\" \"B\" -> \"D\" \"B\" -> \"E\" \"C\" -> \"E\" \"C\" -> \"F\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_DuplicateEdges() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> aEdges = new HashSet<>();
        aEdges.add("B");
        // 重复边
        aEdges.add("B");
        adjacencyMatrix.put("A", aEdges);
        adjacencyMatrix.put("B", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"A\" -> \"B\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_EmptyNodeName() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> edges = new HashSet<>();
        edges.add("");
        adjacencyMatrix.put("", edges);
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"\" -> \"\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_SpecialCharacters() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> edges = new HashSet<>();
        edges.add("B@1");
        adjacencyMatrix.put("A#1", edges);
        adjacencyMatrix.put("B@1", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"A#1\" -> \"B@1\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_NullInput() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            DagToDotConverter.generateDot(adjacencyMatrix);
        });
    }

    @Test
    void testGenerateDot_ExampleComplexDag() throws Throwable {
        // arrange
        Map<String, Set<String>> childrenGraph = new HashMap<>();
        Set<String> oEdges = new HashSet<>();
        oEdges.add("A");
        oEdges.add("W");
        childrenGraph.put("O", oEdges);
        Set<String> aEdges = new HashSet<>();
        aEdges.add("B");
        aEdges.add("C");
        aEdges.add("D");
        aEdges.add("E");
        aEdges.add("F");
        childrenGraph.put("A", aEdges);
        Set<String> bEdges = new HashSet<>();
        bEdges.add("C");
        bEdges.add("D");
        bEdges.add("E");
        bEdges.add("F");
        childrenGraph.put("B", bEdges);
        Set<String> cEdges = new HashSet<>();
        cEdges.add("D");
        cEdges.add("E");
        cEdges.add("F");
        childrenGraph.put("C", cEdges);
        Set<String> dEdges = new HashSet<>();
        dEdges.add("E");
        dEdges.add("F");
        childrenGraph.put("D", dEdges);
        Set<String> eEdges = new HashSet<>();
        eEdges.add("F");
        childrenGraph.put("E", eEdges);
        Set<String> fEdges = new HashSet<>();
        fEdges.add("Z");
        childrenGraph.put("F", fEdges);
        childrenGraph.put("Z", new HashSet<>());
        Set<String> wEdges = new HashSet<>();
        wEdges.add("Z");
        childrenGraph.put("W", wEdges);
        Set<String> mEdges = new HashSet<>();
        mEdges.add("N");
        childrenGraph.put("M", mEdges);
        Set<String> nEdges = new HashSet<>();
        nEdges.add("L");
        childrenGraph.put("N", nEdges);
        childrenGraph.put("L", new HashSet<>());
        // act
        String result = DagToDotConverter.generateDot(childrenGraph);
        // assert
        String expected = "digraph G { \"A\" -> \"B\" \"A\" -> \"C\" \"A\" -> \"D\" \"A\" -> \"E\" \"A\" -> \"F\" " + "\"B\" -> \"C\" \"B\" -> \"D\" \"B\" -> \"E\" \"B\" -> \"F\" \"C\" -> \"D\" \"C\" -> \"E\" \"C\" -> \"F\" " + "\"D\" -> \"E\" \"D\" -> \"F\" \"E\" -> \"F\" \"F\" -> \"Z\" \"M\" -> \"N\" \"N\" -> \"L\" \"O\" -> \"A\" " + "\"O\" -> \"W\" \"W\" -> \"Z\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }

    @Test
    void testGenerateDot_SelfLoop() throws Throwable {
        // arrange
        Map<String, Set<String>> adjacencyMatrix = new HashMap<>();
        Set<String> aEdges = new HashSet<>();
        // 自环边
        aEdges.add("A");
        adjacencyMatrix.put("A", aEdges);
        // act
        String result = DagToDotConverter.generateDot(adjacencyMatrix);
        // assert
        String expected = "digraph G { \"A\" -> \"A\" }";
        assertEquals(normalizeDotString(expected), normalizeDotString(result));
    }
}
