package com.sankuai.dz.product.detail.page.config.model.layout.lion.config;

import com.dianping.pigeon.util.CollectionUtils;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.page.config.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.page.config.utils.json.JacksonUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * @Author: guangyujie
 * @Date: 2025/4/28 17:40
 */
public class OldProductDetail_SecondCategory_1_318 extends BaseConfigTool {

    @Test
    public void testConfig() {
        PageConfigTemplateDTO pageConfigTemplateDTO = new PageConfigTemplateDTO();
        PageConfigDataDTO mainConfigData = new PageConfigDataDTO();
        pageConfigTemplateDTO.setMainConfigData(mainConfigData);
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "商品基础信息模块组",
                buildNormalModuleConfigDTO(
                        "module_detail_reminder_info_tag", "GuideCommon", 1,
                        buildOverlayModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 1)
                )//须知条和须知浮层
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "Tab栏",
                buildTabModuleConfigDTO(
                        "module_detail_tab", "GuideCommon", 1,
                        buildTabModuleItemDTO(
                                "deal_detail",
                                "团购详情",
                                buildModuleGroupConfigDTO(
                                        "结构化详情",
                                        buildNormalModuleConfigDTO("module_detail_structured_detail", "GuideCommon", 1)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "purchase_note",
                                "购买须知",
                                buildModuleGroupConfigDTO(
                                        "结构化购买须知",
                                        buildNormalModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 0)
                                )
                        )
                )
        ));

        System.out.println(JacksonUtils.serialize(pageConfigTemplateDTO));
        Assert.assertTrue(pageConfigTemplateDTO.getMainConfigData() != null && !CollectionUtils.isEmpty(pageConfigTemplateDTO.getMainConfigData().getModuleGroupConfigs()));
    }

}
