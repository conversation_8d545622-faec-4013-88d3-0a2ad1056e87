package com.sankuai.dz.api.module.arrange.framework.fetcher.engine;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.lowcode.LowCodeContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FetcherEngineRunTest {

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private DAG dag;

    @Mock
    private BaseFetcherContext fetcherContext;

    @Mock
    private LowCodeContext lowCodeContext;

    @Mock
    private FetcherDefinition fetcherDefinition;

    @Mock
    private FetcherReturnValueDTO returnValueDTO;

    private Set<String> targetFetcherNames;

    private ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap;

    private FetcherEngine fetcherEngine;

    private static final String TEST_FETCHER_NAME = "testFetcher";

    @BeforeEach
    void setUp() {
        targetFetcherNames = new HashSet<>();
        fetcherContextMap = new ConcurrentHashMap<>();
        fetcherEngine = new FetcherEngine(request, dag, targetFetcherNames, fetcherContextMap, lowCodeContext);
    }

    /**
     * Test empty target fetchers scenario
     */
    @Test
    void testRunWithEmptyTargetFetchers() throws Throwable {
        // Arrange
        List<String> topologicalSort = Arrays.asList("fetcher1", "fetcher2");
        when(dag.getTopologicalSort()).thenReturn(topologicalSort);
        // Act
        fetcherEngine.run();
        // Assert
        verify(dag).getTopologicalSort();
        assertTrue(fetcherContextMap.isEmpty());
    }
}
