<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %t %-5level %logger{30}.%method - %msg%n"/>
<!--            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %t %-5level %msg%n"/>-->
        </Console>
        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>
        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M" rolloverMax="30">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <!--日志远程上报-->
        <CatAppender name="catAppender"/>
        <MDPTraceAppender name="mdpTrace"/>
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <LcLayout/>
        </AsyncScribe>
    </appenders>
    <loggers>
        <!--远程日志-->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender"/>
        </logger>
        <root level="info">
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="Console"/>
            <appender-ref ref="catAppender"/>
            <appender-ref ref="mdpTrace"/>
            <appender-ref ref="ScribeAsyncAppender"/>
        </root>
        <!--过滤掉graphql框架打印的warn信息-->
        <logger name="graphql.execution" level="error" additivity="false">
            <AppenderRef ref="errorAppender"/>
        </logger>
        <logger name="com.dianping.pigeon" level="warn"/>
        <logger name="com.meituan.kafka" level="warn"/>
        <logger name="com.meituan.mafka.client" level="warn"/>
        <logger name="com.dianping.lion" level="warn"/>
        <logger name="com.dianping.appkit" level="warn"/>
        <logger name="com.meituan.mtrace" level="warn"/>
        <!--看下来都是无效实验号的日志，意义不大 -->
        <logger name="com.sankuai.douhu.absdk.client.DouHuClient" level="off"/>
    </loggers>
</configuration>
