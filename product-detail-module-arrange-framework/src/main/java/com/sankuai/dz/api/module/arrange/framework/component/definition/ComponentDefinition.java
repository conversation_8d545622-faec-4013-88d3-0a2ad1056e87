package com.sankuai.dz.api.module.arrange.framework.component.definition;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.component.annotation.BizComponent;
import com.sankuai.dz.api.module.arrange.framework.component.spi.BaseComponent;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import lombok.Getter;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/4/15 17:17
 */
@Getter
@SuppressWarnings("rawtypes")
public class ComponentDefinition {

    private final Class<? extends BaseComponent> componetClass;

    private final BizComponent annotation;

    /**
     * 依赖DAG的起始fetcher
     */
    private final FetcherDefinition startFetcherDefinition;

    /**
     * 依赖的所有fetcher名称
     */
    private final Set<String> dependentFetcherNames;//已转换为只读

    public ComponentDefinition(final Class<? extends BaseComponent> componentClass) {
        if (componentClass == null) {
            throw new BuilderFatalError("初始化Component的入参为空");
        }
        BizComponent annotation = componentClass.getAnnotation(BizComponent.class);
        if (annotation == null) {
            throw new BuilderFatalError(String.format("非法Component定义!!!该Component(%s)没有标注注解", componentClass.getName()));
        }
        this.componetClass = componentClass;
        this.annotation = annotation;
        this.startFetcherDefinition = buildStartFetcherName();
        this.dependentFetcherNames = buildDependentFetchers();
    }

    private FetcherDefinition buildStartFetcherName() {
        Class<? extends BaseFetcherContext> startFetcherClass = this.annotation.startFetcher();
        if (startFetcherClass == null) {
            throw new BuilderFatalError("builder没有标注startFetcher");
        }
        return FetcherDefinitionStorage.getFetcherDefinition(startFetcherClass);
    }

    private Set<String> buildDependentFetchers() {
        final Set<String> dependentFetchers = new HashSet<>();
        //先解析builder自身
        DAG dag = FetcherDAGStorage.getDAG(this.startFetcherDefinition.getFetcherName());
        for (Class<? extends BaseFetcherContext> fetcherClass : this.annotation.dependentFetchers()) {
            addDependentFetchers(FetcherDefinition.getFetcherName(fetcherClass), dag, dependentFetchers);
        }
        //循环解析父类
        Class<?> currentClass = componetClass.getSuperclass();
        while (currentClass != null) {
            if (!BaseComponent.class.isAssignableFrom(currentClass)) {
                break;
            }
            BizComponent parentAnnotation = currentClass.getAnnotation(BizComponent.class);
            if (parentAnnotation != null) {
                if (this.annotation.startFetcher() != parentAnnotation.startFetcher()) {
                    throw new BuilderFatalError(String.format("该Component的父类(%s)的startFetcher与子类不相同!!!", currentClass.getName()));
                }
                for (Class<? extends BaseFetcherContext> fetcherClass : parentAnnotation.dependentFetchers()) {
                    addDependentFetchers(FetcherDefinition.getFetcherName(fetcherClass), dag, dependentFetchers);
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        //转化为不可编辑的集合返回
        return Collections.unmodifiableSet(dependentFetchers);
    }

    private void addDependentFetchers(final String fetcherName, final DAG dag, final Set<String> dependentFetchers) {
        if (!dag.contains(fetcherName)) {
            throw new BuilderFatalError(String.format("该Component(%s)依赖的Fetcher(%s)不在该Component所属的DAG中", componetClass.getSimpleName(), fetcherName));
        }
        dependentFetchers.add(fetcherName);
    }

}
