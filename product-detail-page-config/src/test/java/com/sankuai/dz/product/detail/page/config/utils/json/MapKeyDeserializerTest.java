package com.sankuai.dz.product.detail.page.config.utils.json;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import com.fasterxml.jackson.databind.DeserializationContext;

import java.io.IOException;
import org.junit.jupiter.api.Test;

class MapKeyDeserializerTest {

    private final MapKeyDeserializer deserializer = new MapKeyDeserializer();

    private final DeserializationContext context = mock(DeserializationContext.class);

    /**
     * 测试Integer类型反序列化
     */
    @Test
    public void testDeserializeKeyIntegerType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Integer\",\"value\":\"123\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Integer);
        assertEquals(123, result);
    }

    /**
     * 测试Boolean类型反序列化
     */
    @Test
    public void testDeserializeKeyBooleanType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Boolean\",\"value\":\"true\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Boolean);
        assertEquals(true, result);
    }

    /**
     * 测试Long类型反序列化
     */
    @Test
    public void testDeserializeKeyLongType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Long\",\"value\":\"123456789\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Long);
        assertEquals(123456789L, result);
    }

    /**
     * 测试Double类型反序列化
     */
    @Test
    public void testDeserializeKeyDoubleType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Double\",\"value\":\"123.456\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Double);
        assertEquals(123.456, result);
    }

    /**
     * 测试Float类型反序列化
     */
    @Test
    public void testDeserializeKeyFloatType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Float\",\"value\":\"123.45\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Float);
        assertEquals(123.45f, result);
    }

    /**
     * 测试Short类型反序列化
     */
    @Test
    public void testDeserializeKeyShortType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Short\",\"value\":\"123\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Short);
        assertEquals((short) 123, result);
    }

    /**
     * 测试Byte类型反序列化
     */
    @Test
    public void testDeserializeKeyByteType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.Byte\",\"value\":\"123\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Byte);
        assertEquals((byte) 123, result);
    }

    /**
     * 测试String类型反序列化
     */
    @Test
    public void testDeserializeKeyStringType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.lang.String\",\"value\":\"test\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof String);
        assertEquals("test", result);
    }

    /**
     * 测试Enum类型反序列化
     */
    @Test
    public void testDeserializeKeyEnumType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"java.time.DayOfWeek\",\"value\":\"MONDAY\"}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result instanceof Enum);
        assertEquals("MONDAY", result.toString());
    }

    /**
     * 测试Array类型反序列化
     */
    @Test
    public void testDeserializeKeyArrayType() throws Throwable {
        // arrange
        String key = "{\"@class\":\"[Ljava.lang.String;\",\"value\":[\"a\",\"b\"]}";
        // act
        Object result = deserializer.deserializeKey(key, context);
        // assert
        assertTrue(result.getClass().isArray());
        assertArrayEquals(new String[] { "a", "b" }, (String[]) result);
    }

    /**
     * 测试无效class名称抛出IOException
     */
    @Test
    public void testDeserializeKeyInvalidClassThrowsException() throws Throwable {
        // arrange
        String key = "{\"@class\":\"invalid.Class\",\"value\":\"test\"}";
        // act & assert
        IOException exception = assertThrows(IOException.class, () -> deserializer.deserializeKey(key, context));
        assertTrue(exception.getCause() instanceof ClassNotFoundException);
    }

    /**
     * 测试null输入抛出IllegalArgumentException
     */
    @Test
    public void testDeserializeKeyNullInputThrowsException() throws Throwable {
        // arrange
        String key = null;
        // act & assert
        assertThrows(IllegalArgumentException.class, () -> deserializer.deserializeKey(key, context));
    }

    /**
     * 测试缺少class节点抛出NullPointerException
     */
    @Test
    public void testDeserializeKeyMissingClassNodeThrowsException() throws Throwable {
        // arrange
        String key = "{\"value\":\"test\"}";
        // act & assert
        assertThrows(NullPointerException.class, () -> deserializer.deserializeKey(key, context));
    }
}
