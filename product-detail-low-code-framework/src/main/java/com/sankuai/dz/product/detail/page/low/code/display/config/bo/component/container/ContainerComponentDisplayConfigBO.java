package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.dto.ContainerShowRuleConfig;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.entity.constant.ComponentConstant;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.container.ContainerComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeConfigFatalException;
import com.sankuai.dz.product.detail.page.low.code.exception.LowCodeProcessFatalException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/5/30 17:06
 */
@Getter
public abstract class ContainerComponentDisplayConfigBO extends ComponentDisplayConfigBO {

    private final ContainerShowRuleConfig showRuleConfig;

    protected ContainerComponentDisplayConfigBO(final String componentKey,
                                                final ContainerShowRuleConfig showRuleConfig) {
        super(componentKey);
        this.showRuleConfig = showRuleConfig;
    }

    /**
     * 仅获取容器第一层子组件，不是递归获取所有组件!!!
     */
    public abstract List<? extends ComponentDisplayConfigBO> getSubComponentDisplayConfigs();

    /**
     * 递归获取所有业务组件
     */
    public List<BizComponentDisplayConfigBO> getAllBizComponentDisplayConfigsRecursively() {
        return getAllBizComponentDisplayConfigs(this, 0);
    }

    private static List<BizComponentDisplayConfigBO> getAllBizComponentDisplayConfigs(final ComponentDisplayConfigBO config,
                                                                                      final int depth) {
        if (depth >= ComponentConstant.CONTAINER_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new LowCodeConfigFatalException(String.format(
                    "Fatal Error!!!容器组件(key=%s)嵌套层数过深，最多%s层",
                    config.getComponentKey(), ComponentConstant.CONTAINER_COMPONENT_MAX_RECURSION_DEPTH
            ));
        }
        if (config.getComponentGroup() == ComponentGroupEnum.Business) {
            return Lists.newArrayList((BizComponentDisplayConfigBO) config);
        } else if (config.getComponentGroup() == ComponentGroupEnum.Container) {
            List<? extends ComponentDisplayConfigBO> subComponentDisplayConfigs = ((ContainerComponentDisplayConfigBO) config).getSubComponentDisplayConfigs();
            List<BizComponentDisplayConfigBO> allBizComponentDisplayConfigs = new ArrayList<>();
            for (ComponentDisplayConfigBO subComponentDisplayConfig : subComponentDisplayConfigs) {
                allBizComponentDisplayConfigs.addAll(getAllBizComponentDisplayConfigs(subComponentDisplayConfig, depth + 1));
            }
            return allBizComponentDisplayConfigs;
        } else {
            throw new LowCodeConfigFatalException("暂时不支持该组件类型:" + config.getComponentGroup());
        }
    }

    /**
     * 构建ComponentBO
     * 处理容器组件内部赋值，容器组件可以嵌套容器组件和业务组件。
     * 如果是容器组件则递归遍历，递归最大深度不能超过3层。
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public ComponentBO buildComponentBO(final Map<String, Object> data,
                                        final int depth) {
        if (depth >= ComponentConstant.CONTAINER_COMPONENT_MAX_RECURSION_DEPTH) {
            throw new LowCodeProcessFatalException(String.format(
                    "Fatal Error!!!容器组件(key=%s)嵌套层数过深，最多%s层，理论上不会发生，启动时会校验",
                    this.getComponentKey(), ComponentConstant.CONTAINER_COMPONENT_MAX_RECURSION_DEPTH
            ));
        }
        ComponentDefinition componentDefinition = ComponentDefinitionStorage.getComponentMetadata(this.getComponentKey());
        ContainerComponentBO containerComponent = (ContainerComponentBO) componentDefinition.getClassInfo().createInstance();
        final List<ComponentBO> subComponentBOList = new ArrayList<>();
        for (ComponentDisplayConfigBO subComponentConfig : this.getSubComponentDisplayConfigs()) {
            subComponentBOList.add(
                    subComponentConfig.buildComponentBO(data, depth + 1)
            );
        }
        containerComponent.build(this, subComponentBOList);
        return containerComponent;
    }

}
