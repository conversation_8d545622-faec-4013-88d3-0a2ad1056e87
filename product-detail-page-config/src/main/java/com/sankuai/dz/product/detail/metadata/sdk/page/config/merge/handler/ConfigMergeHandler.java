package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.AbstractMergeRule;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:08
 */
public interface ConfigMergeHandler<T extends AbstractMergeRule> {

    void merge(final PageConfigDataDTO mainConfigData, final T rule);

    ConfigMergeTypeEnum getMergeType();

}
