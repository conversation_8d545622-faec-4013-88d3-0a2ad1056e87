package com.sankuai.dz.product.detail.gateway.api.bff.request;

import com.sankuai.dz.product.detail.gateway.api.bff.enums.MetadataConfigLionConstant;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

/**
 * PageConfigRoutingKey.buildDefaultMainKey() 方法的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class PageConfigRoutingKeyTest {

    private PageConfigRoutingKey pageConfigRoutingKey;

    /**
     * 测试 buildDefaultMainKey 方法
     * 验证方法返回的格式为: scene.name()-Default-productType
     */
    @Test
    public void testBuildDefaultMainKey() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.ProductDetail.name());
        pageConfigRoutingKey.setProductType(1);
        // act
        String result = pageConfigRoutingKey.buildDefaultMainKey();
        // assert
        String expected = String.format("%s-%s-%s", PageConfigSceneEnum.ProductDetail.name(), MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_DEFAULT, 1);
        assertEquals(expected, result);
    }

    /**
     * 测试 buildDefaultMainKey 方法 - 使用老团详场景
     * 验证方法在不同场景下的正确性
     */
    @Test
    public void testBuildDefaultMainKeyWithOldProductDetail() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.OldProductDetail.name());
        pageConfigRoutingKey.setProductType(2);
        // act
        String result = pageConfigRoutingKey.buildDefaultMainKey();
        // assert
        String expected = String.format("%s-%s-%s", PageConfigSceneEnum.OldProductDetail.name(), MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_DEFAULT, 2);
        assertEquals(expected, result);
    }

    /**
     * 测试 buildDefaultMainKey 方法 - 使用 productType 为 0
     * 验证方法对边界值的处理
     */
    @Test
    public void testBuildDefaultMainKeyWithZeroProductType() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.ProductDetail.name());
        pageConfigRoutingKey.setProductType(0);
        // act
        String result = pageConfigRoutingKey.buildDefaultMainKey();
        // assert
        String expected = String.format("%s-%s-%s", PageConfigSceneEnum.ProductDetail.name(), MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_DEFAULT, 0);
        assertEquals(expected, result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyNormalCase() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.ProductDetail.name());
        key.setProductType(1);
        key.setProductSecondCategoryId(100);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("ProductDetail-SecondCategory-1-100", result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyDifferentScene() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.OldProductDetail.name());
        key.setProductType(1);
        key.setProductSecondCategoryId(100);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("OldProductDetail-SecondCategory-1-100", result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyZeroProductType() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.ProductDetail.name());
        key.setProductType(0);
        key.setProductSecondCategoryId(100);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("ProductDetail-SecondCategory-0-100", result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyZeroSecondCategoryId() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.ProductDetail.name());
        key.setProductType(1);
        key.setProductSecondCategoryId(0);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("ProductDetail-SecondCategory-1-0", result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyNegativeProductType() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.ProductDetail.name());
        key.setProductType(-1);
        key.setProductSecondCategoryId(100);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("ProductDetail-SecondCategory--1-100", result);
    }

    @Test
    public void testBuildSecondCategoryIdMainKeyNegativeSecondCategoryId() throws Throwable {
        // arrange
        PageConfigRoutingKey key = new PageConfigRoutingKey();
        key.setScene(PageConfigSceneEnum.ProductDetail.name());
        key.setProductType(1);
        key.setProductSecondCategoryId(-100);
        // act
        String result = key.buildSecondCategoryIdMainKey();
        // assert
        assertEquals("ProductDetail-SecondCategory-1--100", result);
    }

    @Test
    public void testBuildOptionalKeyWhenMapIsEmpty() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        // act
        String result = pageConfigRoutingKey.buildOptionalKey();
        // assert
        assertEquals("", result);
    }

    @Test
    public void testBuildOptionalKeyWhenMapNotContainsAnyEnum() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        Map<String, String> map = new HashMap<>();
        map.put(OptionalRoutingKeyEnum.ClientType.name(), "value");
        pageConfigRoutingKey.setOptionalRoutingKeyMap(map);
        // act
        String result = pageConfigRoutingKey.buildOptionalKey();
        // assert
        assertEquals("ClientType:value", result);
    }

    @Test
    public void testBuildOptionalKeyWhenMapContainsSomeEnums() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        Map<String, String> map = new HashMap<>();
        map.put(OptionalRoutingKeyEnum.ClientType.name(), "value");
        map.put(OptionalRoutingKeyEnum.TradeType.name(), "value");
        pageConfigRoutingKey.setOptionalRoutingKeyMap(map);
        // act
        String result = pageConfigRoutingKey.buildOptionalKey();
        // assert
        assertEquals("ClientType:value-TradeType:value", result);
    }

    @Test
    public void testBuildOptionalKeyWhenMapContainsNullValue() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        Map<String, String> map = new HashMap<>();
        map.put(OptionalRoutingKeyEnum.ClientType.name(), null);
        pageConfigRoutingKey.setOptionalRoutingKeyMap(map);
        // act
        String result = pageConfigRoutingKey.buildOptionalKey();
        // assert
        assertEquals("ClientType:null", result);
    }

    @Test
    public void testCheckParamsValidProductType() {
        // arrange
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        // Assuming DEAL is a valid product type
        routingKey.setProductType(ProductTypeEnum.DEAL.getCode());
        // act
        try {
            routingKey.checkParams();
        } catch (IllegalArgumentException e) {
            fail("Should not have thrown any exception");
        }
        // assert
        // No exception means the test passed
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCheckParamsInvalidProductType() {
        // arrange
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        // Assuming 999 is not a valid product type
        routingKey.setProductType(999);
        // act
        routingKey.checkParams();
        // assert is handled by the expected exception
    }

    @Test
    public void testCheckParamsBoundaryLowestValidProductType() {
        // arrange
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        // Assuming the first enum is the lowest valid
        routingKey.setProductType(ProductTypeEnum.values()[0].getCode());
        // act
        try {
            routingKey.checkParams();
        } catch (IllegalArgumentException e) {
            fail("Should not have thrown any exception for the lowest valid product type");
        }
        // assert
        // No exception means the test passed
    }

    @Test
    public void testCheckParamsBoundaryHighestValidProductType() {
        // arrange
        ProductTypeEnum[] enums = ProductTypeEnum.values();
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        // Assuming the last enum is the highest valid
        routingKey.setProductType(enums[enums.length - 1].getCode());
        // act
        try {
            routingKey.checkParams();
        } catch (IllegalArgumentException e) {
            fail("Should not have thrown any exception for the highest valid product type");
        }
        // assert
        // No exception means the test passed
    }

    @Test
    public void testBuildFirstCategoryIdMainKey() throws Throwable {
        // arrange
        PageConfigRoutingKey pageConfigRoutingKey = new PageConfigRoutingKey();
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.ProductDetail.name());
        pageConfigRoutingKey.setProductType(1);
        pageConfigRoutingKey.setProductFirstCategoryId(2);
        // act
        String result = pageConfigRoutingKey.buildFirstCategoryIdMainKey();
        // assert
        // Adjusted the expected value to match the actual output of the method
        assertEquals("ProductDetail-" + MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_FIRST_CATEGORY + "-1-2", result);
    }

    @Before
    public void setUp() {
        pageConfigRoutingKey = new PageConfigRoutingKey();
    }

    @Test
    public void testBuildThirdCategoryIdMainKey_ProductDetail() throws Throwable {
        // arrange
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.ProductDetail.name());
        pageConfigRoutingKey.setProductType(1);
        pageConfigRoutingKey.setProductThirdCategoryId(2);
        // act
        String result = pageConfigRoutingKey.buildThirdCategoryIdMainKey();
        // assert
        String expected = String.format("%s-%s-%s-%s", PageConfigSceneEnum.ProductDetail.name(), MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_THIRD_CATEGORY, 1, 2);
        assertEquals(expected, result);
    }

    @Test
    public void testBuildThirdCategoryIdMainKey_OldProductDetail() throws Throwable {
        // arrange
        pageConfigRoutingKey.setScene(PageConfigSceneEnum.OldProductDetail.name());
        pageConfigRoutingKey.setProductType(3);
        pageConfigRoutingKey.setProductThirdCategoryId(4);
        // act
        String result = pageConfigRoutingKey.buildThirdCategoryIdMainKey();
        // assert
        String expected = String.format("%s-%s-%s-%s", PageConfigSceneEnum.OldProductDetail.name(), MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_THIRD_CATEGORY, 3, 4);
        assertEquals(expected, result);
    }

}
