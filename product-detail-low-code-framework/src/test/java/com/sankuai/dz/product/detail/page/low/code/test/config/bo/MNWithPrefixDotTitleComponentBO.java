package com.sankuai.dz.product.detail.page.low.code.test.config.bo;

import com.google.common.collect.Lists;
import com.sankuai.athena.digital.arch.annotations.DigitalArchClass;
import com.sankuai.athena.digital.arch.annotations.DigitalArchField;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentMetadata;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.biz.BizComponentBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BaseViewComponent;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.vo.biz.BizComponentVO;
import com.sankuai.dz.product.detail.page.low.code.test.config.vo.DetailType11ViewComponent;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * @Author: caisiyuan03
 * @Date: 2025/6/9 19:52
 * @Description: M选N服务项目
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@DigitalArchClass
@ComponentMetadata(
        componentKey = DetailComponentKeyConstant.Service_Project_MN,
        moduleKey = ModuleKeyConstants.STRUCTURED_DEAL_DETAILS,
        desc = "服务项目（M选N）",
        componentGroup = ComponentGroupEnum.Business,
        componentType = ComponentTypeEnum.Text_Biz
)
public class MNWithPrefixDotTitleComponentBO extends BizComponentBO {

    // TODO: 业务复用性设计, 支持类似TitleWithPrefixDotComponentBO的复用
    // TODO: 实现相同接口, 支持buildBaseViewComponent方法
    @DigitalArchField(desc = "服务项目标题")
    private String titleWithPrefixDot;
    @DigitalArchField(desc = "可选数量N", required = false)
    private Long optionalCount;
    @DigitalArchField(desc = "服务项目列表")
    private List<ServiceProjectItemComponentBO> serviceProjectItems;

    @Override
    public BizComponentVO boBuildBizComponentVO() {
        if (StringUtils.isBlank(titleWithPrefixDot) || CollectionUtils.isEmpty(serviceProjectItems)) {
            throw new IllegalArgumentException("业务组件参数：titleWithPrefixDot和serviceProjectItems不能为空！");
        }
        List<BaseViewComponent> result = Lists.newArrayList();

        Integer total = serviceProjectItems.size();
        StringBuilder sb = new StringBuilder(titleWithPrefixDot);
        if (optionalCount != null && !total.equals(optionalCount)) {
            sb.append(total).append("选").append(optionalCount);
        }

        BaseViewComponent titleWithPrefixDotViewComponent = DetailType11ViewComponent.builder()
                .title(sb.toString())
                .build();
        result.add(titleWithPrefixDotViewComponent);

        List<BaseViewComponent> serviceProjectItemViewComponents = Lists.newArrayList();
        for (ServiceProjectItemComponentBO serviceProjectItem : serviceProjectItems) {
            List<BaseViewComponent> dealDetails = Optional.ofNullable(serviceProjectItem)
                    .map(ServiceProjectItemComponentBO::boBuildBizComponentVO)
                    .map(BizComponentVO::getDealDetails)
                    .orElse(Lists.newArrayList());
            serviceProjectItemViewComponents.addAll(dealDetails);
        }
        result.addAll(serviceProjectItemViewComponents);
        return new BizComponentVO(result);
    }
}
