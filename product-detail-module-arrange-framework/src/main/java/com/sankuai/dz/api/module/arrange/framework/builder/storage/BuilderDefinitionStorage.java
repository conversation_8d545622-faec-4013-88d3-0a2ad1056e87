package com.sankuai.dz.api.module.arrange.framework.builder.storage;

import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.BuilderFactoryDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.CommonBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.VariableBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.api.module.arrange.framework.utils.Storage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/3 16:25
 */
@SuppressWarnings("rawtypes")
@Slf4j
public class BuilderDefinitionStorage {

    private static ApplicationContext applicationContext;

    @SuppressWarnings("unchecked")
    public static <T extends AbstractBuilder> T getBuilderBean(final BuilderDefinition builderDefinition) {
        try {
            return (T) applicationContext.getBean(builderDefinition.getBuilderClass());
        } catch (Throwable throwable) {
            String builderClass = Optional.ofNullable(builderDefinition)
                    .map(BuilderDefinition::getBuilderClass)
                    .map(Class::getName).orElse(null);
            log.error("BuilderDefinitionStorage.getBuilderBean,class:{}", builderClass,
                    new BuilderFatalError("运行时Fatal Error!!!getBuilderBean失败:" + builderClass, throwable));
            return null;
        }
    }

    public static final Storage<Class<? extends AbstractBuilder>, BuilderDefinition> builderStorageOfModuleClass = new Storage<>(
            new HashMap<>(), "ModuleClass", "CommonBuilderDefinition"
    );

    public static final Storage<String, BuilderDefinition> builderStorageOfModuleKey = new Storage<>(
            new HashMap<>(), "ModuleKey", "CommonBuilderDefinition"
    );

    public static synchronized void initBuilderDefinitionStorage(final ApplicationContext applicationContext) {
        BuilderDefinitionStorage.applicationContext = applicationContext;
        String[] beanNamesForAnnotation = applicationContext.getBeanNamesForAnnotation(Builder.class);
        for (String builderBeanName : beanNamesForAnnotation) {
            final Class<?> builderClass = applicationContext.getType(builderBeanName);
            if (builderClass == null) {
                throw new BuilderFatalError("无法找到该Builder的beanName对应的Class:" + builderBeanName);
            }
            final Builder annotation = builderClass.getAnnotation(Builder.class);
            if (annotation == null) {
                throw new BuilderFatalError("无法找到该Builder的注解:" + builderBeanName);
            }
            switch (annotation.builderType()) {
                case COMMON_BUILDER:
                    saveBuilderDefinition(
                            buildCommonBuilderDefinition(builderClass, builderBeanName)
                    );
                    break;
                case VARIABLE_BUILDER://泛型Builder会在工厂中处理
                    break;
                case BUILDER_FACTORY:
                    saveBuilderDefinition(
                            buildBuilderFactory(builderClass, builderBeanName, annotation)
                    );
                    break;
                case ABSTRACT_BUILDER_FACTORY:
                    throw new UnsupportedOperationException("暂不支持抽象工厂Builder");
                default:
                    throw new BuilderFatalError("未知BuilderType:" + annotation.builderType().name());
            }
        }
    }

    @SuppressWarnings("unchecked")
    private static CommonBuilderDefinition buildCommonBuilderDefinition(final Class<?> builderClass,
                                                                        final String builderBeanName) {
        if (!BaseBuilder.class.isAssignableFrom(builderClass)) {
            throw new BuilderFatalError(String.format(
                    "CommonBuilder(%s)没有继承BaseBuilder", builderClass.getName()
            ));
        }
        if (!applicationContext.isPrototype(builderBeanName)) {
            throw new BuilderFatalError(String.format(
                    "CommonBuilder(%s)不是多例的(prototype)", builderClass.getName()
            ));
        }
        return new CommonBuilderDefinition((Class<? extends BaseBuilder>) builderClass);
    }

    @SuppressWarnings("unchecked")
    private static VariableBuilderDefinition buildVariableBuilderDefinition(final Class<?> builderClass,
                                                                            final String builderBeanName) {
        if (!BaseVariableBuilder.class.isAssignableFrom(builderClass)) {
            throw new BuilderFatalError(String.format(
                    "VariableBuilder(%s)没有继承BaseVariableBuilder", builderClass.getName()
            ));
        }
        if (!applicationContext.isPrototype(builderBeanName)) {
            throw new BuilderFatalError(String.format(
                    "VariableBuilder(%s)不是多例的(prototype)", builderClass.getName()
            ));
        }
        return new VariableBuilderDefinition((Class<? extends BaseVariableBuilder>) builderClass);
    }

    @SuppressWarnings("unchecked")
    private static BuilderFactoryDefinition buildBuilderFactory(final Class<?> builderFactoryClass,
                                                                final String builderFactoryBeanName,
                                                                final Builder builderFactoryAnnotation) {
        if (!BaseBuilderFactory.class.isAssignableFrom(builderFactoryClass)) {
            throw new BuilderFatalError(String.format(
                    "BuilderFactory(%s)没有继承BaseBuilderFactory", builderFactoryClass.getName()
            ));
        }
        if (!applicationContext.isPrototype(builderFactoryBeanName)) {
            throw new BuilderFatalError(String.format(
                    "BuilderFactory(%s)不是多例的(prototype)", builderFactoryClass.getName()
            ));
        }
        //构建BuilderFactory关联的variableBuilder
        List<VariableBuilderDefinition> variableBuilderDefinitions = new ArrayList<>();
        String[] beanNamesForAnnotation = applicationContext.getBeanNamesForAnnotation(Builder.class);
        for (String builderBeanName : beanNamesForAnnotation) {
            final Class<?> builderClass = applicationContext.getType(builderBeanName);
            if (builderClass == null) {
                throw new BuilderFatalError("无法找到该Builder的beanName对应的Class:" + builderBeanName);
            }
            final Builder builderAnnotation = builderClass.getAnnotation(Builder.class);
            if (builderAnnotation == null) {
                throw new BuilderFatalError("无法找到该Builder的注解:" + builderBeanName);
            }
            if (builderAnnotation.moduleKey().equals(builderFactoryAnnotation.moduleKey())
                    && builderAnnotation.builderType() == BuilderTypeEnum.VARIABLE_BUILDER) {
                VariableBuilderDefinition builderDefinition = buildVariableBuilderDefinition(
                        builderClass, builderBeanName
                );
                //由于一个moduleKey对应多个variableBuilder且moduleKey的Builder类型是工厂，所以只存储在classMap中
                saveBuilderDefinitionByClass(builderDefinition);
                variableBuilderDefinitions.add(builderDefinition);
            }
        }
        //构建BuilderFactory
        return new BuilderFactoryDefinition((Class<? extends BaseBuilderFactory>) builderFactoryClass, variableBuilderDefinitions);
    }

    private static void saveBuilderDefinition(final BuilderDefinition builderDefinition) {
        saveBuilderDefinitionByModuleKey(builderDefinition);
        saveBuilderDefinitionByClass(builderDefinition);
    }

    private static void saveBuilderDefinitionByModuleKey(BuilderDefinition builderDefinition) {
        if (builderStorageOfModuleKey.containsKey(builderDefinition.getModuleKey())) {
            throw new IllegalArgumentException(String.format(
                    "出现重复定义moduleKey的Builder(class=%s)", builderDefinition.getBuilderClass().getName()
            ));
        }
        builderStorageOfModuleKey.put(builderDefinition.getModuleKey(), builderDefinition);
    }

    private static void saveBuilderDefinitionByClass(BuilderDefinition builderDefinition) {
        if (builderStorageOfModuleClass.containsKey(builderDefinition.getBuilderClass())) {
            throw new IllegalArgumentException(String.format(
                    "出现重复的Builder(class=%s)", builderDefinition.getBuilderClass().getName()
            ));
        }
        builderStorageOfModuleClass.put(builderDefinition.getBuilderClass(), builderDefinition);
    }

}
