package com.sankuai.dz.api.module.arrange.framework.starter;

import com.sankuai.dz.product.detail.page.low.code.starter.LowCodeBeanConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: guangyujie
 * @Date: 2025/6/9 11:59
 */
@Configuration
@AutoConfigureAfter(LowCodeBeanConfiguration.class)
public class ModuleArrangeFrameworkConfiguration {

    @Bean
    public ModuleArrangeFrameworkStarter ModuleArrangeFrameworkStarter() {
        return new ModuleArrangeFrameworkStarter();
    }

}