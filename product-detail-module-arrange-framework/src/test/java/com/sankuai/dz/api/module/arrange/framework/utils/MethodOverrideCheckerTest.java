package com.sankuai.dz.api.module.arrange.framework.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for MethodOverrideChecker
 */
@ExtendWith(MockitoExtension.class)
public class MethodOverrideCheckerTest {

    // Test classes for method override scenarios
    private static class Parent {

        public void overriddenMethod() {
        }

        public void nonOverriddenMethod() {
        }

        public void parameterizedMethod(String param1, int param2) {
        }
    }

    private static class Child extends Parent {

        @Override
        public void overriddenMethod() {
        }

        public void parameterizedMethod(String param1, int param2) {
        }
    }

    /**
     * Test when method is overridden in child class
     */
    @Test
    public void testIsMethodOverridden_WhenMethodIsOverridden_ReturnsTrue() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        String methodName = "overriddenMethod";
        // act
        boolean result = MethodOverrideChecker.isMethodOverridden(parentClass, childClass, methodName);
        // assert
        assertTrue(result, "Should return true when method is overridden in child class");
    }

    /**
     * Test when method is not overridden in child class
     */
    @Test
    public void testIsMethodOverridden_WhenMethodNotOverridden_ReturnsFalse() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        String methodName = "nonOverriddenMethod";
        // act
        boolean result = MethodOverrideChecker.isMethodOverridden(parentClass, childClass, methodName);
        // assert
        assertFalse(result, "Should return false when method is not overridden in child class");
    }

    /**
     * Test when method has parameters
     */
    @Test
    public void testIsMethodOverridden_WhenMethodHasParameters_ChecksCorrectly() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        String methodName = "parameterizedMethod";
        Class<?>[] parameterTypes = { String.class, int.class };
        // act
        boolean result = MethodOverrideChecker.isMethodOverridden(parentClass, childClass, methodName, parameterTypes);
        // assert
        assertTrue(result, "Should correctly identify overridden method with parameters");
    }

    /**
     * Test when method doesn't exist
     */
    @Test
    public void testIsMethodOverridden_WhenMethodDoesNotExist_ReturnsFalse() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        String methodName = "nonExistentMethod";
        // act
        boolean result = MethodOverrideChecker.isMethodOverridden(parentClass, childClass, methodName);
        // assert
        assertFalse(result, "Should return false when method doesn't exist");
    }

    /**
     * Test when parent class is null
     */
    @Test
    public void testIsMethodOverridden_WhenParentClassIsNull_ReturnsFalse() throws Throwable {
        // arrange
        Class<?> childClass = Child.class;
        String methodName = "overriddenMethod";
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            MethodOverrideChecker.isMethodOverridden(null, childClass, methodName);
        }, "Should throw NullPointerException when parent class is null");
    }

    /**
     * Test when child class is null
     */
    @Test
    public void testIsMethodOverridden_WhenChildClassIsNull_ReturnsFalse() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        String methodName = "overriddenMethod";
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            MethodOverrideChecker.isMethodOverridden(parentClass, null, methodName);
        }, "Should throw NullPointerException when child class is null");
    }

    /**
     * Test when method name is null
     */
    @Test
    public void testIsMethodOverridden_WhenMethodNameIsNull_ReturnsFalse() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            MethodOverrideChecker.isMethodOverridden(parentClass, childClass, null);
        }, "Should throw NullPointerException when method name is null");
    }

    /**
     * Test when parameter types array is empty
     */
    @Test
    public void testIsMethodOverridden_WhenEmptyParameterTypes_ChecksCorrectly() throws Throwable {
        // arrange
        Class<?> parentClass = Parent.class;
        Class<?> childClass = Child.class;
        String methodName = "overriddenMethod";
        Class<?>[] parameterTypes = new Class<?>[0];
        // act
        boolean result = MethodOverrideChecker.isMethodOverridden(parentClass, childClass, methodName, parameterTypes);
        // assert
        assertTrue(result, "Should correctly identify overridden method with no parameters");
    }
}
