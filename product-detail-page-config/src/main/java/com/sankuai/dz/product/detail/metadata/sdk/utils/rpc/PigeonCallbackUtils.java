package com.sankuai.dz.product.detail.metadata.sdk.utils.rpc;

import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.meituan.mtrace.context.TransmissibleContext;
import com.meituan.mtrace.thread.TraceContextHandler;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/13 22:10
 */
public class PigeonCallbackUtils implements InvocationCallback {

    public static <T> CompletableFuture<T> setPigeonCallback(Class<T> tClass) {
        CompletableFuture<T> completableFuture = new CompletableFuture<>();
        PigeonCallbackUtils pigeonCallbackUtils = new PigeonCallbackUtils(completableFuture);
        InvokerHelper.setCallback(pigeonCallbackUtils);
        return completableFuture;
    }

    private CompletableFuture completableFuture;
    private TransmissibleContext transmissibleContext;
    private boolean isDone = false;

    public PigeonCallbackUtils(CompletableFuture completableFuture) {
        this.completableFuture = completableFuture;
        this.transmissibleContext = TraceContextHandler.getTransContext();
    }

    public boolean isDone() {
        return isDone;
    }

    @Override
    public void onSuccess(Object result) {
        TransmissibleContext backup = TraceContextHandler.backupAndSetTransContext(transmissibleContext);
        try {
            this.completableFuture.complete(result);
            isDone = true;
        } finally {
            TraceContextHandler.restoreBackupTransContext(backup);
        }
    }

    @Override
    public void onFailure(Throwable throwable) {
        TransmissibleContext backup = TraceContextHandler.backupAndSetTransContext(transmissibleContext);
        try {
            this.completableFuture.completeExceptionally(throwable);
            isDone = true;
        } finally {
            TraceContextHandler.restoreBackupTransContext(backup);
        }
    }
}