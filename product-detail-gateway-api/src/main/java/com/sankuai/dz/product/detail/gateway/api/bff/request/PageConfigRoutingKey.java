package com.sankuai.dz.product.detail.gateway.api.bff.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.MetadataConfigLionConstant;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.OptionalRoutingKeyEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 14:24
 */
@Data
@TypeDoc(description = "商品详情页路由规则key")
public class PageConfigRoutingKey implements Serializable {

    @FieldDoc(description = "场景，最大分类", requiredness = Requiredness.REQUIRED,
            type = PageConfigSceneEnum.class)
    private String scene;

    //mainKey维度不推荐增加，会大幅增加配置成本和管理成本，建议就按照商品类型+商品一、二、三级类目进行配置，新增维度可以加在optionalRoutingKey中
    @FieldDoc(description = "商品类型，具体看枚举", requiredness = Requiredness.REQUIRED,
            type = ProductTypeEnum.class)
    private int productType;

    @FieldDoc(description = "商品一级分类", rule = "如果未命中配置，降级到productType", requiredness = Requiredness.OPTIONAL)
    private int productFirstCategoryId;

    @FieldDoc(description = "商品二级分类", rule = "如果未命中配置，降级到productFirstCategoryId", requiredness = Requiredness.OPTIONAL)
    private int productSecondCategoryId;

    @FieldDoc(description = "商品三级分类", rule = "如果未命中配置，降级到productSecondCategoryId", requiredness = Requiredness.OPTIONAL)
    private int productThirdCategoryId;

    @FieldDoc(description = "可选路由key，key参考OptionalRoutingKeyEnum.name", rule = "如果未命中配置，则不生效",
            requiredness = Requiredness.OPTIONAL, type = OptionalRoutingKeyEnum.class)
    private Map<String, String> optionalRoutingKeyMap = new HashMap<>();

    public void checkParams() {
        if (!ProductTypeEnum.containsCode(productType)) {
            throw new IllegalArgumentException("非法productType:" + productType);
        }
    }

    public String buildThirdCategoryIdMainKey() {
        return String.format("%s-%s-%s-%s", scene, MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_THIRD_CATEGORY, productType, productThirdCategoryId);
    }

    public String buildSecondCategoryIdMainKey() {
        return String.format("%s-%s-%s-%s", scene, MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_SECOND_CATEGORY, productType, productSecondCategoryId);
    }

    public String buildFirstCategoryIdMainKey() {
        return String.format("%s-%s-%s-%s", scene, MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_FIRST_CATEGORY, productType, productFirstCategoryId);
    }

    public String buildDefaultMainKey() {
        return String.format("%s-%s-%s", scene, MetadataConfigLionConstant.PAGE_CONFIG_KEY_HEADER_DEFAULT, productType);
    }

    public String buildOptionalKey() {
        return Arrays.stream(OptionalRoutingKeyEnum.values())
                .map(OptionalRoutingKeyEnum::name)
                .filter(key -> optionalRoutingKeyMap.containsKey(key))
                .map(key -> {
                    String value = optionalRoutingKeyMap.get(key);
                    return String.format("%s:%s", key, value);
                }).collect(Collectors.joining("-"));
    }

    public String buildSelectedOptionalKey(Set<OptionalRoutingKeyEnum> selectedRoutingKey) {
        return Arrays.stream(OptionalRoutingKeyEnum.values())
                .filter(selectedRoutingKey::contains)
                .map(OptionalRoutingKeyEnum::name)
                .filter(key -> optionalRoutingKeyMap.containsKey(key))
                .map(key -> {
                    String value = optionalRoutingKeyMap.get(key);
                    return String.format("%s:%s", key, value);
                }).collect(Collectors.joining("-"));
    }

}
