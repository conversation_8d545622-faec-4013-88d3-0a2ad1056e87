package com.sankuai.dz.product.detail.gateway.domain.pagetype;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.product.detail.gateway.api.page.type.request.PageTypeRequest;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserAgent;
import com.sankuai.dz.product.detail.gateway.domain.biz.DeviceInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.biz.ProductCategoryHandler;
import com.sankuai.dz.product.detail.gateway.domain.biz.UserInfoResolver;
import com.sankuai.dz.product.detail.gateway.domain.context.PageTypeContext;
import com.sankuai.dz.product.detail.gateway.domain.utils.DealLinkUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.HeaderUtil;
import com.sankuai.dz.product.detail.gateway.domain.utils.UserAgentUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.collections.MapUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PageTypeServiceTest {

    @Mock
    private ProductCategoryHandler productCategoryHandler;

    @Mock
    private UserInfoResolver userInfoResolver;

    @Mock
    private DeviceInfoResolver deviceInfoResolver;

    @InjectMocks
    private PageTypeService pageTypeService;

    private PageTypeRequest validRequest;

    private Map<String, String> validHeaders;

    @Before
    public void setUp() {
        validRequest = new PageTypeRequest();
        validRequest.setProductId(12345L);
        validHeaders = new HashMap<>();
        validHeaders.put("User-Agent", "mapi 1.0 (com.dianping.v1 1.0; iOS)");
        validHeaders.put("pragma-dpid", "device123");
        validHeaders.put("pragma-unionid", "union123");
        validRequest.setHeaderMap(validHeaders);
    }

    /**
     * Test when input context is invalid (null request)
     */
    @Test
    public void testProcessInvalidContext() throws Throwable {
        // arrange
        PageTypeContext context = new PageTypeContext(null);
        // act
        String result = pageTypeService.process(context);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when request has invalid product ID
     */
    @Test
    public void testProcessInvalidProductId() throws Throwable {
        // arrange
        PageTypeRequest request = new PageTypeRequest();
        request.setProductId(0);
        request.setHeaderMap(validHeaders);
        PageTypeContext context = new PageTypeContext(request);
        // act
        String result = pageTypeService.process(context);
        // assert
        assertEquals("", result);
    }

    /**
     * Test successful DP platform flow with user ID required
     */
    @Test
    public void testProcessDpPlatformWithUserIdRequired() throws Throwable {
        // arrange
        ProductCategory category = new ProductCategory();
        category.setSecondId(100);
        when(productCategoryHandler.getCategoryAsync(any())).thenReturn(CompletableFuture.completedFuture(category));
        when(userInfoResolver.getUserId(anyBoolean(), anyMap())).thenReturn(CompletableFuture.completedFuture(12345L));
        PageTypeContext context = new PageTypeContext(validRequest);
        // act
        String result = pageTypeService.process(context);
        // assert
        assertNotNull(result);
        assertNotNull(context.getProductCategory());
        assertEquals(12345L, context.getUserId());
    }

    /**
     * Test MT platform flow without user ID required
     */
    @Test
    public void testProcessMtPlatformWithoutUserIdRequired() throws Throwable {
        // arrange
        validHeaders.put("User-Agent", "mapi 1.0 (com.sankuai.meituan 1.0; Android)");
        ProductCategory category = new ProductCategory();
        category.setSecondId(200);
        when(productCategoryHandler.getCategoryAsync(any())).thenReturn(CompletableFuture.completedFuture(category));
        when(userInfoResolver.getUserId(anyBoolean(), anyMap())).thenReturn(CompletableFuture.completedFuture(0L));
        PageTypeContext context = new PageTypeContext(validRequest);
        // act
        String result = pageTypeService.process(context);
        // assert
        assertNotNull(result);
        assertNotNull(context.getProductCategory());
        assertEquals(0L, context.getUserId());
    }

    /**
     * Test when async category fetch fails
     */
    @Test
    public void testProcessAsyncCategoryFetchFails() throws Throwable {
        // arrange
        when(productCategoryHandler.getCategoryAsync(any())).thenReturn(CompletableFuture.completedFuture(null));
        when(userInfoResolver.getUserId(anyBoolean(), anyMap())).thenReturn(CompletableFuture.completedFuture(0L));
        PageTypeContext context = new PageTypeContext(validRequest);
        // act
        String result = pageTypeService.process(context);
        // assert
        assertNotNull(result);
        assertNull(context.getProductCategory());
        assertEquals(0L, context.getUserId());
    }
}
