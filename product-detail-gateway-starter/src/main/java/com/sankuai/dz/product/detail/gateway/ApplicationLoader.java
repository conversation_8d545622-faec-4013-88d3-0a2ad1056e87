package com.sankuai.dz.product.detail.gateway;

import com.dianping.rhino.spring.RhinoConfiguration;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = {"com.sankuai.dz.product.detail.metadata.sdk", "com.sankuai.dz.product.detail.gateway.application", "com.sankuai.dz.product.detail.gateway.domain"}
)
@RhinoConfiguration
public class ApplicationLoader {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }
}


