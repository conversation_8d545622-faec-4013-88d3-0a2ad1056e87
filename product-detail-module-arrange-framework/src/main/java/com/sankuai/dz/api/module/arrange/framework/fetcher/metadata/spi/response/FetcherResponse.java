package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 11:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FetcherResponse<T extends FetcherReturnValueDTO> extends BaseFetcherResponse {

    private T returnValue;

    public static <T extends FetcherReturnValueDTO> FetcherResponse<T> succeed(T returnValue) {
        FetcherResponse<T> fetcherResponse = new FetcherResponse<>();
        fetcherResponse.setSuccess(true);
        fetcherResponse.setReturnValue(returnValue);
        return fetcherResponse;
    }

    public static <T extends FetcherReturnValueDTO> FetcherResponse<T> fail(Throwable error) {
        FetcherResponse<T> fetcherResponse = new FetcherResponse<>();
        fetcherResponse.setSuccess(false);
        fetcherResponse.setError(error);
        return fetcherResponse;
    }

}
