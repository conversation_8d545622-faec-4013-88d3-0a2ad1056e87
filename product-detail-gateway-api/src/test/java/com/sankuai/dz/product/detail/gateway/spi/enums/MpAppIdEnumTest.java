package com.sankuai.dz.product.detail.gateway.spi.enums;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MpAppIdEnumTest {

    /**
     * 测试 codeOf 方法能正确返回 MT_KUAISHOU_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode1_ThenReturnMTKuaishouMiniProgram() {
        // arrange
        int code = 1;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.MT_KUAISHOU_MINIPROGRAM, result);
        assertEquals("ks652177521456999275", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 MT_WEIXIN_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode2_ThenReturnMTWeixinMiniProgram() {
        // arrange
        int code = 2;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.MT_WEIXIN_MINIPROGRAM, result);
        assertEquals("wxde8ac0a21135c07d", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 DP_WEIXIN_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode3_ThenReturnDPWeixinMiniProgram() {
        // arrange
        int code = 3;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.DP_WEIXIN_MINIPROGRAM, result);
        assertEquals("wx734c1ad7b3562129", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 XIUYU_WANWU_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode4_ThenReturnXiuyuWanwuMiniProgram() {
        // arrange
        int code = 4;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.XIUYU_WANWU_MINIPROGRAM, result);
        assertEquals("wx46d3ba1216c12af4", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 DP_BAIDUMAP_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode5_ThenReturnDPBaiduMapMiniProgram() {
        // arrange
        int code = 5;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.DP_BAIDUMAP_MINIPROGRAM, result);
        assertEquals("11553825", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 MT_LIVE_WEIXIN_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode6_ThenReturnMTLiveWeixinMiniProgram() {
        // arrange
        int code = 6;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.MT_LIVE_WEIXIN_MINIPROGRAM, result);
        assertEquals("wxe955ef83bdcc9f82", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法能正确返回 MT_LIVE_ORDER_WEIXIN_MINIPROGRAM 枚举
     */
    @Test
    public void testCodeOf_WhenCode7_ThenReturnMTLiveOrderWeixinMiniProgram() {
        // arrange
        int code = 7;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(code);
        // assert
        assertEquals(MpAppIdEnum.MT_LIVE_ORDER_WEIXIN_MINIPROGRAM, result);
        assertEquals("wx33c85a6366be4fc8", result.getMpAppId());
    }

    /**
     * 测试 codeOf 方法在传入不存在的 code 时抛出 UnsupportedOperationException
     */
    @Test(expected = UnsupportedOperationException.class)
    public void testCodeOf_WhenInvalidCode_ThenThrowException() {
        // arrange
        int invalidCode = 999;
        // act
        MpAppIdEnum.codeOf(invalidCode);
        // assert - 期望抛出异常
    }

    /**
     * 测试 codeOf 方法在传入最小边界值(1)时能正确返回对应枚举
     */
    @Test
    public void testCodeOf_WhenMinBoundaryCode_ThenReturnCorrectEnum() {
        // arrange
        int minCode = 1;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(minCode);
        // assert
        assertEquals(MpAppIdEnum.MT_KUAISHOU_MINIPROGRAM, result);
    }

    /**
     * 测试 codeOf 方法在传入最大边界值(7)时能正确返回对应枚举
     */
    @Test
    public void testCodeOf_WhenMaxBoundaryCode_ThenReturnCorrectEnum() {
        // arrange
        int maxCode = 7;
        // act
        MpAppIdEnum result = MpAppIdEnum.codeOf(maxCode);
        // assert
        assertEquals(MpAppIdEnum.MT_LIVE_ORDER_WEIXIN_MINIPROGRAM, result);
    }

    /**
     * 测试 codeOf 方法在传入超过最大边界的 code 时抛出异常
     */
    @Test(expected = UnsupportedOperationException.class)
    public void testCodeOf_WhenCodeExceedsMaxBoundary_ThenThrowException() {
        // arrange
        int exceedMaxCode = 8;
        // act
        MpAppIdEnum.codeOf(exceedMaxCode);
        // assert - 期望抛出异常
    }

    /**
     * 测试 codeOf 方法在传入低于最小边界的 code 时抛出异常
     */
    @Test(expected = UnsupportedOperationException.class)
    public void testCodeOf_WhenCodeBelowMinBoundary_ThenThrowException() {
        // arrange
        int belowMinCode = 0;
        // act
        MpAppIdEnum.codeOf(belowMinCode);
        // assert - 期望抛出异常
    }
}
