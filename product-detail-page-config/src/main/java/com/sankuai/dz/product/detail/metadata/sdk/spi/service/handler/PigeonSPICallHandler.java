package com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericProductDetailPageResponse;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.utils.rpc.PigeonCallbackUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 21:59
 */
@Component
@Slf4j
public class PigeonSPICallHandler implements SPICallHandler<GenericService> {

    private Map<String, GenericService> spiMap = new HashMap<>();

    @Override
    public GenericService getSpiService(final SPIConfigDTO spiConfigDTO) {
        InvokerConfig<GenericService> invokerConfig = new InvokerConfig<>(
                spiConfigDTO.getInterfaceUrl(),
                GenericService.class
        );
        invokerConfig.setTimeout(spiConfigDTO.getTimeout());
        invokerConfig.setGeneric(GenericType.JSON_COMMON.getName());
        invokerConfig.setCallType(CallMethod.CALLBACK.getName());
        GenericService service = ServiceFactory.getService(invokerConfig);
        if (service == null) {
            throw new SPIConfigFatalException("获取spi服务失败:" + spiConfigDTO.getInterfaceUrl());
        }
        return service;
    }

    @Override
    public void refreshSPIMap(Map<String, GenericService> spiMap) {
        if (MapUtils.isEmpty(spiMap)) {
            throw new SPIConfigFatalException(getInterfaceType().name() + "类型的SPI服务集合为空!");
        }
        this.spiMap = spiMap;
    }

    @Override
    public CompletableFuture<GenericProductDetailPageResponse> call(final SPIConfigDTO spiConfigDTO,
                                                                    final ProductDetailPageRequest request) {
        final String interfaceAlias = spiConfigDTO.getInterfaceAlias();
        GenericService service = spiMap.get(interfaceAlias);
        if (service == null) {
            throw new SPIConfigFatalException("查不到该接口别名对应的rpc服务:" + interfaceAlias);
        }
        final long startTime = System.currentTimeMillis();
        CompletableFuture<String> future = PigeonCallbackUtils.setPigeonCallback(String.class);
        try {
            service.$invoke(
                    "query",
                    Collections.singletonList(ProductDetailPageRequest.class.getName()),
                    Collections.singletonList(SafeJacksonUtils.serialize(request))
            );
        } catch (Throwable throwable) {
            log.error("PigeonSPICallHandler.GenericService.call error", throwable);
            future.completeExceptionally(throwable);
        }
        return future.thenApply(json -> {
            GenericProductDetailPageResponse response = JSONObject.parseObject(json, GenericProductDetailPageResponse.class);
            response.setDuration(System.currentTimeMillis() - startTime);
            return response;
        }).exceptionally(throwable -> {
            log.error("SPICallMainFunction.singleSPI.call,alias={}", interfaceAlias, throwable);
            GenericProductDetailPageResponse response = GenericProductDetailPageResponse.fail(
                    String.format("%s:%s", throwable.getClass().getName(), throwable.getMessage())
            );
            response.setDuration(System.currentTimeMillis() - startTime);
            return response;
        });
    }

    @Override
    public InterfaceTypeEnum getInterfaceType() {
        return InterfaceTypeEnum.PIGEON;
    }

}
