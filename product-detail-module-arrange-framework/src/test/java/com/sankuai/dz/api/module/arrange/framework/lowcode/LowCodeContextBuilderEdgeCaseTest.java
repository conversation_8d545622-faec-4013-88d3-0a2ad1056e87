package com.sankuai.dz.api.module.arrange.framework.lowcode;

import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.DisplayConfigStorage;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * LowCodeContextBuilder 边界情况和异常场景测试
 * 专门测试各种边界条件和异常情况，提高代码覆盖率
 * 
 * @Author: guangyujie
 * @Date: 2025/1/16 10:30
 */
@RunWith(MockitoJUnitRunner.class)
public class LowCodeContextBuilderEdgeCaseTest {

    private PageConfigRoutingKey validRoutingKey;
    private ProductDetailPageRequest validPageRequest;

    @Before
    public void setUp() {
        // 创建有效的路由key
        validRoutingKey = new PageConfigRoutingKey();
        validRoutingKey.setScene("detail");
        validRoutingKey.setProductType(1);
        validRoutingKey.setProductFirstCategoryId(100);

        // 创建有效的页面请求
        validPageRequest = new ProductDetailPageRequest();
        validPageRequest.setPageConfigRoutingKey(validRoutingKey);
    }

    /**
     * 测试 build 方法 - 请求参数为null的各种情况
     */
    @Test
    public void testBuildWithNullParameters() {
        // 测试完全null的请求
        LowCodeContext result1 = LowCodeContextBuilder.build(null);
        assertNotNull("null请求应该返回空的LowCodeContext", result1);
        assertTrue("null请求的数据源应该为空", result1.getAllRelatedDataSourceConfig().isEmpty());

        // 测试request为null的情况
        ModuleArrangeRequest requestWithNullPageRequest = new ModuleArrangeRequest(null, new HashSet<>());
        LowCodeContext result2 = LowCodeContextBuilder.build(requestWithNullPageRequest);
        assertNotNull("null页面请求应该返回空的LowCodeContext", result2);

        // 测试moduleKeys为null的情况
        ModuleArrangeRequest requestWithNullModuleKeys = new ModuleArrangeRequest(validPageRequest, null);
        LowCodeContext result3 = LowCodeContextBuilder.build(requestWithNullModuleKeys);
        assertNotNull("null模块keys应该返回空的LowCodeContext", result3);
    }

    /**
     * 测试 build 方法 - 复杂的数据源合并场景
     */
    @Test
    public void testBuildWithComplexDataSourceMerging() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建复杂的数据源合并场景
            PageDisplayConfigBO complexPageConfig = createComplexPageConfigForDataSourceMerging();
            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", complexPageConfig);

            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenReturn(configResponse);

            // 创建包含所有模块的请求
            Set<String> allModuleKeys = new HashSet<>(Arrays.asList("module1", "module2", "module3"));
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, allModuleKeys);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(request);

            // 验证结果
            assertNotNull("结果不应该为空", result);

            // 验证数据源合并结果
            DataSourceConfigBO sharedFetcher = result.getDataSourceConfig("shared-fetcher");
            assertNotNull("共享数据源应该存在", sharedFetcher);
            
            Set<String> mergedFields = sharedFetcher.getOptionalFieldName();
            assertEquals("合并后的字段数量应该正确", 5, mergedFields.size());
            assertTrue("应该包含所有字段", mergedFields.containsAll(
                    Arrays.asList("field1", "field2", "field3", "field4", "field5")));

            // 验证独立数据源
            assertNotNull("独立数据源1应该存在", result.getDataSourceConfig("unique-fetcher-1"));
            assertNotNull("独立数据源2应该存在", result.getDataSourceConfig("unique-fetcher-2"));

            // 验证总的数据源数量
            assertEquals("总数据源数量应该正确", 3, result.getAllDependentFetcherNames().size());
        }
    }

    /**
     * 测试 build 方法 - 空的数据源配置
     */
    @Test
    public void testBuildWithEmptyDataSources() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建没有数据源的模块配置
            ModuleDisplayConfigBO moduleWithoutDataSource = mock(ModuleDisplayConfigBO.class);
            when(moduleWithoutDataSource.getAllDataSource()).thenReturn(new HashMap<>());

            PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
            Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
            matchedConfigs.put("module1", moduleWithoutDataSource);
            
            when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                    .thenReturn(matchedConfigs);

            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", pageConfig);
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenReturn(configResponse);

            Set<String> moduleKeys = new HashSet<>(Arrays.asList("module1"));
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, moduleKeys);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(request);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNotNull("模块配置应该存在", result.getModuleDisplayConfig("module1"));
            assertTrue("数据源配置应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
            assertTrue("依赖的fetcher名称应该为空", result.getAllDependentFetcherNames().isEmpty());
        }
    }

    /**
     * 测试 build 方法 - 大量模块和数据源
     */
    @Test
    public void testBuildWithLargeNumberOfModulesAndDataSources() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建大量模块和数据源的配置
            PageDisplayConfigBO largePageConfig = createLargePageConfig();
            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", largePageConfig);

            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenReturn(configResponse);

            // 创建包含大量模块的请求
            Set<String> largeModuleKeys = new HashSet<>();
            for (int i = 1; i <= 50; i++) {
                largeModuleKeys.add("module" + i);
            }
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, largeModuleKeys);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(request);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            
            // 验证部分模块配置
            assertNotNull("模块1应该存在", result.getModuleDisplayConfig("module1"));
            assertNotNull("模块25应该存在", result.getModuleDisplayConfig("module25"));
            assertNotNull("模块50应该存在", result.getModuleDisplayConfig("module50"));

            // 验证数据源数量
            assertFalse("数据源配置不应该为空", result.getAllRelatedDataSourceConfig().isEmpty());
            assertTrue("数据源数量应该合理", result.getAllDependentFetcherNames().size() <= 50);
        }
    }

    /**
     * 测试 build 方法 - 异常恢复机制
     */
    @Test
    public void testBuildWithExceptionRecovery() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 模拟第一次调用抛出异常，第二次调用成功
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenThrow(new RuntimeException("第一次调用失败"))
                    .thenReturn(new ConfigQueryResponse("test-key", null));

            Set<String> moduleKeys = new HashSet<>(Arrays.asList("module1"));
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, moduleKeys);

            // 执行第一次测试 - 应该捕获异常并返回空的LowCodeContext
            LowCodeContext result1 = LowCodeContextBuilder.build(request);
            assertNotNull("第一次调用应该返回空的LowCodeContext", result1);
            assertTrue("第一次调用的数据源应该为空", result1.getAllRelatedDataSourceConfig().isEmpty());

            // 执行第二次测试 - 应该正常处理
            LowCodeContext result2 = LowCodeContextBuilder.build(request);
            assertNotNull("第二次调用应该返回空的LowCodeContext", result2);
            assertTrue("第二次调用的数据源应该为空", result2.getAllRelatedDataSourceConfig().isEmpty());
        }
    }

    /**
     * 测试 build 方法 - 特殊字符和边界值
     */
    @Test
    public void testBuildWithSpecialCharactersAndBoundaryValues() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建包含特殊字符的模块配置
            ModuleDisplayConfigBO specialModule = mock(ModuleDisplayConfigBO.class);
            Map<String, DataSourceConfigBO> specialDataSources = new HashMap<>();
            
            // 创建包含特殊字符的数据源
            Set<String> specialFields = new HashSet<>(Arrays.asList("field-with-hyphen", "field_with_underscore", "field.with.dot"));
            DataSourceConfigBO specialDataSource = new DataSourceConfigBO("fetcher-特殊字符_123", specialFields);
            specialDataSources.put("fetcher-特殊字符_123", specialDataSource);
            
            when(specialModule.getAllDataSource()).thenReturn(specialDataSources);

            PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
            Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
            matchedConfigs.put("module-特殊字符_123", specialModule);
            
            when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                    .thenReturn(matchedConfigs);

            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", pageConfig);
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenReturn(configResponse);

            Set<String> moduleKeys = new HashSet<>(Arrays.asList("module-特殊字符_123"));
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, moduleKeys);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(request);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNotNull("特殊字符模块应该存在", result.getModuleDisplayConfig("module-特殊字符_123"));
            assertNotNull("特殊字符数据源应该存在", result.getDataSourceConfig("fetcher-特殊字符_123"));
            
            DataSourceConfigBO retrievedDataSource = result.getDataSourceConfig("fetcher-特殊字符_123");
            assertEquals("特殊字符字段数量应该正确", 3, retrievedDataSource.getOptionalFieldName().size());
        }
    }

    /**
     * 测试 build 方法 - 数据源字段为空的情况
     */
    @Test
    public void testBuildWithEmptyDataSourceFields() {
        try (MockedStatic<DisplayConfigStorage> displayConfigMock = mockStatic(DisplayConfigStorage.class)) {
            // 创建包含空字段的数据源
            ModuleDisplayConfigBO moduleWithEmptyFields = mock(ModuleDisplayConfigBO.class);
            Map<String, DataSourceConfigBO> dataSourcesWithEmptyFields = new HashMap<>();
            
            DataSourceConfigBO emptyFieldsDataSource = new DataSourceConfigBO("empty-fields-fetcher", new HashSet<>());
            dataSourcesWithEmptyFields.put("empty-fields-fetcher", emptyFieldsDataSource);
            
            when(moduleWithEmptyFields.getAllDataSource()).thenReturn(dataSourcesWithEmptyFields);

            PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
            Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
            matchedConfigs.put("module1", moduleWithEmptyFields);
            
            when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                    .thenReturn(matchedConfigs);

            ConfigQueryResponse configResponse = new ConfigQueryResponse("test-key", pageConfig);
            displayConfigMock.when(() -> DisplayConfigStorage.getConfig(any(PageConfigRoutingKey.class)))
                    .thenReturn(configResponse);

            Set<String> moduleKeys = new HashSet<>(Arrays.asList("module1"));
            ModuleArrangeRequest request = new ModuleArrangeRequest(validPageRequest, moduleKeys);

            // 执行测试
            LowCodeContext result = LowCodeContextBuilder.build(request);

            // 验证结果
            assertNotNull("结果不应该为空", result);
            assertNotNull("模块配置应该存在", result.getModuleDisplayConfig("module1"));
            
            DataSourceConfigBO emptyFieldsResult = result.getDataSourceConfig("empty-fields-fetcher");
            assertNotNull("空字段数据源应该存在", emptyFieldsResult);
            assertTrue("数据源字段应该为空", emptyFieldsResult.getOptionalFieldName().isEmpty());
        }
    }

    /**
     * 创建复杂的页面配置用于数据源合并测试
     */
    private PageDisplayConfigBO createComplexPageConfigForDataSourceMerging() {
        // 创建三个模块，其中两个共享一个数据源，每个模块还有独立的数据源
        ModuleDisplayConfigBO module1 = mock(ModuleDisplayConfigBO.class);
        ModuleDisplayConfigBO module2 = mock(ModuleDisplayConfigBO.class);
        ModuleDisplayConfigBO module3 = mock(ModuleDisplayConfigBO.class);

        // module1 的数据源：shared-fetcher(field1,field2) + unique-fetcher-1(field6)
        Map<String, DataSourceConfigBO> module1DataSources = new HashMap<>();
        module1DataSources.put("shared-fetcher", new DataSourceConfigBO("shared-fetcher", 
                new HashSet<>(Arrays.asList("field1", "field2"))));
        module1DataSources.put("unique-fetcher-1", new DataSourceConfigBO("unique-fetcher-1", 
                new HashSet<>(Arrays.asList("field6"))));
        when(module1.getAllDataSource()).thenReturn(module1DataSources);

        // module2 的数据源：shared-fetcher(field2,field3,field4) + unique-fetcher-2(field7)
        Map<String, DataSourceConfigBO> module2DataSources = new HashMap<>();
        module2DataSources.put("shared-fetcher", new DataSourceConfigBO("shared-fetcher", 
                new HashSet<>(Arrays.asList("field2", "field3", "field4"))));
        module2DataSources.put("unique-fetcher-2", new DataSourceConfigBO("unique-fetcher-2", 
                new HashSet<>(Arrays.asList("field7"))));
        when(module2.getAllDataSource()).thenReturn(module2DataSources);

        // module3 的数据源：shared-fetcher(field4,field5)
        Map<String, DataSourceConfigBO> module3DataSources = new HashMap<>();
        module3DataSources.put("shared-fetcher", new DataSourceConfigBO("shared-fetcher", 
                new HashSet<>(Arrays.asList("field4", "field5"))));
        when(module3.getAllDataSource()).thenReturn(module3DataSources);

        PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
        Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
        matchedConfigs.put("module1", module1);
        matchedConfigs.put("module2", module2);
        matchedConfigs.put("module3", module3);
        
        when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                .thenReturn(matchedConfigs);
        
        return pageConfig;
    }

    /**
     * 创建大量模块和数据源的页面配置
     */
    private PageDisplayConfigBO createLargePageConfig() {
        PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
        Map<String, ModuleDisplayConfigBO> matchedConfigs = new HashMap<>();
        
        for (int i = 1; i <= 50; i++) {
            ModuleDisplayConfigBO module = mock(ModuleDisplayConfigBO.class);
            Map<String, DataSourceConfigBO> dataSources = new HashMap<>();
            
            // 每个模块有一个独立的数据源
            String fetcherName = "fetcher" + i;
            Set<String> fields = new HashSet<>(Arrays.asList("field" + i + "_1", "field" + i + "_2"));
            dataSources.put(fetcherName, new DataSourceConfigBO(fetcherName, fields));
            
            when(module.getAllDataSource()).thenReturn(dataSources);
            matchedConfigs.put("module" + i, module);
        }
        
        when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                .thenReturn(matchedConfigs);
        
        return pageConfig;
    }
}
