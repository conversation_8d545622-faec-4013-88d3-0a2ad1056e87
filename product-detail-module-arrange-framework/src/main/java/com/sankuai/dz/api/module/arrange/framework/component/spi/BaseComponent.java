package com.sankuai.dz.api.module.arrange.framework.component.spi;

import com.sankuai.dz.api.module.arrange.framework.component.definition.ComponentDefinition;
import com.sankuai.dz.api.module.arrange.framework.component.exception.ComponentFatalException;
import com.sankuai.dz.api.module.arrange.framework.component.storage.ComponentDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContext;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContextAware;

/**
 * @Author: guangyujie
 * @Date: 2025/4/15 17:15
 */
public abstract class BaseComponent extends RunnerContextAware {

    /**
     * BuilderComponent的定义
     */
    private ComponentDefinition componentDefinition;

    @Override
    protected void doInit(final RunnerContext runnerContext) {
        componentDefinition = ComponentDefinitionStorage.componentStorageOfModuleClass.get(this.getClass());
        if (componentDefinition == null) {
            throw new ComponentFatalException(String.format("获取不到componentDefinition,class=%s", this.getClass().getName()));
        }
    }

    @Override
    protected boolean containsDependencyFetcher(String dependencyFetcherName) {
        return this.componentDefinition.getDependentFetcherNames().contains(dependencyFetcherName);
    }

}
