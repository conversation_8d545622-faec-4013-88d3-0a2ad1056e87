package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import lombok.Data;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/2/9 14:42
 */
@Data
@SuppressWarnings("rawtypes")
public class FetcherContextInitResult {

    private BaseFetcherContext self;

    private List<BaseFetcherContext> others;

    public FetcherContextInitResult(BaseFetcherContext self) {
        this.self = self;
    }

    public FetcherContextInitResult(BaseFetcherContext self, List<BaseFetcherContext> others) {
        this.self = self;
        this.others = others;
    }

}
