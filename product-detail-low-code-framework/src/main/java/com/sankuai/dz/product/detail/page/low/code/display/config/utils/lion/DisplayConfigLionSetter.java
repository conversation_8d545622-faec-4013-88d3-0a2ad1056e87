package com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion;

import com.dianping.lion.Environment;
import com.dianping.lion.client.util.LionEnvHttpServersUtil;
import com.dianping.lion.client.util.http.HttpClient;
import com.dianping.lion.client.util.http.HttpRequest;
import com.dianping.lion.client.util.http.HttpResponse;
import com.sankuai.dz.product.detail.page.low.code.display.config.utils.json.JacksonUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion.DisplayConfigLionConstant.APP_KEY;
import static com.sankuai.dz.product.detail.page.low.code.display.config.utils.lion.DisplayConfigLionConstant.LION_PASSWORD;

/**
 * @Author: guangyujie
 * @Date: 2024/7/10 16:22
 */
public class DisplayConfigLionSetter {

    private static final String FILE_FORMAT = "/fileconfig/env/%s/appkey/%s/group/%s/file/%s";

    public static String buildFilePath(String env, String group, String fileName) {
        return String.format(FILE_FORMAT, env, APP_KEY, group, fileName);
    }

    public static String buildContentPath(String env, String group, String fileName) {
        return String.format(FILE_FORMAT, env, APP_KEY, group, fileName) + "/content";
    }

    private static final String lionAPIServer = LionEnvHttpServersUtil.getLionAPIServer();

    private static final String env = Environment.isProductEnv() ? "prod" : "test";

    private static final String group = "default";

    private static final String SWIMLANE_DEFAULT = "default";

    private static final String SET_DEFAULT = "default";

    public static <T> void setResource(String key, T data, String swimlane, String set) throws Exception {
        if (StringUtils.isEmpty(swimlane)) {
            swimlane = SWIMLANE_DEFAULT;
        }
        if (StringUtils.isBlank(set)) {
            set = SET_DEFAULT;
        }
        LionResponse response = addRecord(key, JacksonUtils.serialize(data), swimlane, set);
        if (response.isHasError()) {
            throw new IllegalStateException("创建Lion文件内容失败,msg:" + response.getMessage());
        }
    }

    public static void deleteFlow(String key, String swimlane, String set) throws Exception {
        if (StringUtils.isEmpty(swimlane)) {
            swimlane = SWIMLANE_DEFAULT;
        }
        if (StringUtils.isBlank(set)) {
            set = SET_DEFAULT;
        }
        LionResponse response = deleteRecord(key, swimlane, set);
        if (response.isHasError()) {
            throw new IllegalStateException("删除Lion文件内容失败,msg:" + response.getMessage());
        }
    }

    public static <T> T loadFlow(String key, String swimlane, String set, Class<T> dataClass) throws Exception {
        if (StringUtils.isEmpty(swimlane)) {
            swimlane = SWIMLANE_DEFAULT;
        }
        if (StringUtils.isBlank(set)) {
            set = SET_DEFAULT;
        }
        LionResponse response = loadRecord(key, swimlane, set);
        if (response.isHasError()) {
            throw new IllegalStateException("读取Lion文件内容失败,msg:" + response.getMessage());
        }
        return JacksonUtils.deserialize(response.getResult(), dataClass);
    }

    private static LionResponse deleteRecord(String fileName, String swimlane, String set) throws Exception {
        String path = buildFilePath(env, group, fileName);
        Map<String, String> params = new HashMap<>();
        params.put("set", set);
        params.put("swimlane", swimlane);

        Map<String, Object> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        HttpRequest delete = createHttpRequest(HttpClient.METHOD_DELETE, null, path, header, params);
        return execute(delete);
    }

    private static LionResponse loadRecord(String fileName, String swimlane, String set) throws Exception {
        String path = buildContentPath(env, group, fileName);
        Map<String, String> params = new HashMap<>();
        params.put("set", set);
        params.put("swimlane", swimlane);

        Map<String, Object> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        HttpRequest get = createHttpRequest(HttpClient.METHOD_GET, null, path, header, params);
        return executeEntity(get);
    }

    private static LionResponse addRecord(String fileName, String fileContent, String swimlane, String set) throws Exception {
        String path = buildFilePath(env, group, fileName);
        Map<String, String> params = new HashMap<>();
        params.put("set", set);
        params.put("swimlane", swimlane);
        Map<String, Object> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        HttpRequest post = createHttpRequest(HttpClient.METHOD_POST, fileContent, path, header, params);
        return execute(post);
    }

    private static LionResponse executeEntity(HttpRequest request) throws Exception {
        HttpResponse execute = HttpClient.execute(request);
        LionResponse response = new LionResponse();
        response.setCode(execute.getStatusCode());
        response.setResult(execute.getEntity());
        if (!execute.isOK() || execute.getStatusCode() != 200 || StringUtils.isBlank(execute.getEntity())) {
            response.setHasError(true);
        }
        return response;
    }

    private static LionResponse execute(HttpRequest request) throws Exception {
        HttpResponse execute = HttpClient.execute(request);
        LionResponse response = JacksonUtils.deserialize(execute.getEntity(), LionResponse.class);
        if (response == null) {
            response = new LionResponse();
        }
        response.setCode(execute.getStatusCode());
        response.setHasError(false);
        if (!execute.isOK() || execute.getStatusCode() != 200 || StringUtils.isBlank(execute.getEntity())) {
            response.setHasError(true);
        }
        return response;
    }

    private static HttpRequest createHttpRequest(String method,
                                                 String text,
                                                 String path,
                                                 Map<String, Object> header,
                                                 Map<String, String> params) {
        return HttpRequest.builder()
                .host(lionAPIServer)
                .clientKey(APP_KEY)
                .secret(LION_PASSWORD)
                .headers(header)
                .params(params)
                .method(method)
                .path(path)
                .jsonEntity(text)
                .build();

    }

    @Data
    private static class LionResponse {

        private int code;

        private String status;

        private String message;

        private String result;

        private boolean hasError;

    }

}
