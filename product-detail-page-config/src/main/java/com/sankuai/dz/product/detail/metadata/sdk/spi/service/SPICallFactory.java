package com.sankuai.dz.product.detail.metadata.sdk.spi.service;

import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler.SPICallHandler;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/2/18 23:05
 */
@Component
public class SPICallFactory implements InitializingBean {

    @Resource
    private List<SPICallHandler> handlerList;
    private final Map<InterfaceTypeEnum, SPICallHandler> handlerMap = new HashMap<>();

    public SPICallHandler getHandler(InterfaceTypeEnum interfaceTypeEnum) {
        return handlerMap.get(interfaceTypeEnum);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (SPICallHandler spiCallHandler : handlerList) {
            handlerMap.put(spiCallHandler.getInterfaceType(), spiCallHandler);
        }
        for (InterfaceTypeEnum interfaceTypeEnum : InterfaceTypeEnum.values()) {
            if (!handlerMap.containsKey(interfaceTypeEnum)) {
                throw new SPIConfigFatalException("没有该接口类型的处理器:" + interfaceTypeEnum.name());
            }
        }
    }

}
