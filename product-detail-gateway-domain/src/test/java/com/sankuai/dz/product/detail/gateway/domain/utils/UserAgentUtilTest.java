package com.sankuai.dz.product.detail.gateway.domain.utils;

import static org.junit.Assert.*;
import com.sankuai.dz.product.detail.gateway.domain.bean.UserAgent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UserAgentUtilTest {

    @Test
    public void testParseUserAgentWhenUserAgentStrIsEmptyOrStartsWithMapiOrMozilla() throws Throwable {
        String userAgentStr = "";
        UserAgent result = UserAgentUtil.parseUserAgent(userAgentStr);
        assertNotNull(result);
        assertNull(result.getPlatform());
        assertNull(result.getClient());
        assertNull(result.getVersion());
    }

    @Test
    public void testParseUserAgentWhenUserAgentStrDoesNotStartWithMapiOrMozillaButMatchesPattern() throws Throwable {
        // Adjusted the userAgentStr to match the expected pattern for DP platform and iPhone client
        String userAgentStr = "mapi 1.0 (dpscope 1.0;)";
        UserAgent result = UserAgentUtil.parseUserAgent(userAgentStr);
        assertNotNull(result);
        assertEquals("dp", result.getPlatform());
        assertEquals("ios", result.getClient());
        assertEquals("1.0", result.getVersion());
    }

    @Test
    public void testParseUserAgentWhenUserAgentStrDoesNotStartWithMapiOrMozillaAndDoesNotMatchPattern() throws Throwable {
        String userAgentStr = "not mapi or mozilla";
        UserAgent result = UserAgentUtil.parseUserAgent(userAgentStr);
        assertNotNull(result);
        assertNull(result.getPlatform());
        assertNull(result.getClient());
        assertNull(result.getVersion());
    }
}
