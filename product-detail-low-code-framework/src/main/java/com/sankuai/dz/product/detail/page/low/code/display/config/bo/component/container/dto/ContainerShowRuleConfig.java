package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.dto;

import com.sankuai.dz.product.detail.page.low.code.engine.enums.RuleEngineType;
import lombok.Getter;

/**
 * @Author: guang<PERSON><PERSON><PERSON>
 * @Date: 2025/5/30 17:18
 */
@Getter
public class ContainerShowRuleConfig {

    /**
     * 规则引擎类型
     */
    private final RuleEngineType ruleEngineType;

    /**
     * DSL
     */
    private final String showExpression;

    public ContainerShowRuleConfig(final RuleEngineType ruleEngineType,
                                   final String showExpression) {
        this.ruleEngineType = ruleEngineType;
        this.showExpression = showExpression;
    }

}
