package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDefinitionException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto.FetcherDefinitionInitParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.enums.FetcherTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FetcherDefinitionCheckPreviousLayerDependenciesInDAGTest {

    @Mock
    private Fetcher fetcherAnnotation;

    /**
     * Test case when fetcher is not start fetcher and has no dependencies
     * Should throw FetcherDefinitionException
     */
    @Test
    public void testCheckPreviousLayerDependenciesInDAG_NotStartFetcherNoDependencies() throws Throwable {
        // arrange
        when(fetcherAnnotation.isStartFetcher()).thenReturn(false);
        when(fetcherAnnotation.timeout()).thenReturn(1000L);
        TestFetcherDefinition definition = new TestFetcherDefinition(TestFetcherContext.class, new FetcherDefinitionInitParam(TestFetcherContext.class, fetcherAnnotation));
        // act & assert
        assertThrows(FetcherDefinitionException.class, () -> definition.checkPreviousLayerDependenciesInDAG(), "Should throw exception when not start fetcher has no dependencies");
    }

    /**
     * Test case when fetcher is start fetcher but has dependencies
     * Should throw FetcherDefinitionException
     */
    @Test
    public void testCheckPreviousLayerDependenciesInDAG_StartFetcherWithDependencies() throws Throwable {
        // arrange
        when(fetcherAnnotation.isStartFetcher()).thenReturn(true);
        when(fetcherAnnotation.timeout()).thenReturn(1000L);
        TestFetcherDefinition definition = new TestFetcherDefinition(TestFetcherContext.class, new FetcherDefinitionInitParam(TestFetcherContext.class, fetcherAnnotation)) {

            @Override
            public Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG() {
                Set<Class<? extends BaseFetcherContext>> dependencies = new HashSet<>();
                dependencies.add(TestFetcherContext.class);
                return dependencies;
            }
        };
        // act & assert
        assertThrows(FetcherDefinitionException.class, () -> definition.checkPreviousLayerDependenciesInDAG(), "Should throw exception when start fetcher has dependencies");
    }

    /**
     * Test case when fetcher is start fetcher and has no dependencies
     * Should pass without exception
     */
    @Test
    public void testCheckPreviousLayerDependenciesInDAG_StartFetcherNoDependencies() throws Throwable {
        // arrange
        when(fetcherAnnotation.isStartFetcher()).thenReturn(true);
        when(fetcherAnnotation.timeout()).thenReturn(1000L);
        TestFetcherDefinition definition = new TestFetcherDefinition(TestFetcherContext.class, new FetcherDefinitionInitParam(TestFetcherContext.class, fetcherAnnotation));
        // act & assert
        assertDoesNotThrow(() -> definition.checkPreviousLayerDependenciesInDAG(), "Should not throw exception when start fetcher has no dependencies");
    }

    /**
     * Test case when fetcher is not start fetcher and has dependencies
     * Should pass without exception
     */
    @Test
    public void testCheckPreviousLayerDependenciesInDAG_NotStartFetcherWithDependencies() throws Throwable {
        // arrange
        when(fetcherAnnotation.isStartFetcher()).thenReturn(false);
        when(fetcherAnnotation.timeout()).thenReturn(1000L);
        TestFetcherDefinition definition = new TestFetcherDefinition(TestFetcherContext.class, new FetcherDefinitionInitParam(TestFetcherContext.class, fetcherAnnotation)) {

            @Override
            public Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG() {
                Set<Class<? extends BaseFetcherContext>> dependencies = new HashSet<>();
                dependencies.add(TestFetcherContext.class);
                return dependencies;
            }
        };
        // act & assert
        assertDoesNotThrow(() -> definition.checkPreviousLayerDependenciesInDAG(), "Should not throw exception when not start fetcher has dependencies");
    }

    // Helper classes for testing
    @Fetcher(isStartFetcher = false, timeout = 1000L)
    private static class TestFetcherContext extends NormalFetcherContext<TestReturnValueDTO> {

        @Override
        public FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) throws Exception {
            return null;
        }

        @Override
        public FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }

        @Override
        protected CompletableFuture<TestReturnValueDTO> doFetch() throws Exception {
            return null;
        }
    }

    private static class TestReturnValueDTO extends FetcherReturnValueDTO {
    }

    private static class TestFetcherDefinition extends FetcherDefinition {

        public TestFetcherDefinition(Class<? extends BaseFetcherContext> fetcherClass, FetcherDefinitionInitParam initParam) throws Exception {
            super(fetcherClass, initParam);
        }

        @Override
        public FetcherTypeEnum getFetcherType() {
            return null;
        }

        @Override
        public Set<Class<? extends BaseFetcherContext>> getClassOfPreviousLayerDependenciesInDAG() {
            return new HashSet<>();
        }

        @Override
        public Set<String> getNameOfPreviousLayerDependenciesInDAG() {
            return new HashSet<>();
        }
    }
}
