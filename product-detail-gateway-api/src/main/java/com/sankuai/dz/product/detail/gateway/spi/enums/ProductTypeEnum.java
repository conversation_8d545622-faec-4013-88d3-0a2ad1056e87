package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

/**
 * @Author: guangyu<PERSON><PERSON>
 * @Date: 2025/1/8 17:09
 */
@Getter
public enum ProductTypeEnum {

    DEAL(1, "团购", ProductModelTypeEnum.DEAL),
    RESERVE(2, "泛商品预订", ProductModelTypeEnum.FUN_PRODUCT);

    private final int code;

    private final String desc;

    private final ProductModelTypeEnum productModelType;

    ProductTypeEnum(int code, String desc, ProductModelTypeEnum productModelType) {
        this.code = code;
        this.desc = desc;
        this.productModelType = productModelType;
    }

    public static ProductTypeEnum fromCode(int code) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (ProductTypeEnum value : ProductTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}