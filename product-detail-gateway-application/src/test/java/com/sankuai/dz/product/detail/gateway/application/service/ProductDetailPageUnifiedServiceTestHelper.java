package com.sankuai.dz.product.detail.gateway.application.service;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;

/**
 * ProductDetailPageUnifiedService 测试辅助工具类
 * 提供测试数据创建和验证方法
 *
 * @Author: guangyujie
 * @Date: 2025/1/15 17:00
 */
public class ProductDetailPageUnifiedServiceTestHelper {

    /**
     * 创建有效的 MT 请求
     */
    public static UnifiedPageRequest createValidMTRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(12345L);
        request.setProductType(1); // 美团商品类型
        request.setClientType(100); // MT_APP
        request.setPageRegion(1);
        request.setModuleKeys("header,detail,recommend");
        request.setMrnversion("1.0.0");
        return request;
    }

    /**
     * 创建有效的 DP 请求
     */
    public static UnifiedPageRequest createValidDPRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(67890L);
        request.setProductType(2); // 点评商品类型
        request.setClientType(200); // DP_APP
        request.setPageRegion(2);
        request.setModuleKeys("header,detail,review");
        request.setMrnversion("2.0.0");
        return request;
    }

    /**
     * 创建边界值请求 - 最小有效值
     */
    public static UnifiedPageRequest createMinValidRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(1L);
        request.setProductType(1);
        request.setClientType(100);
        request.setMrnversion("1.0");
        return request;
    }

    /**
     * 创建边界值请求 - 最大有效值
     */
    public static UnifiedPageRequest createMaxValidRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(Long.MAX_VALUE);
        request.setProductType(999);
        request.setClientType(999);
        request.setPageRegion(999);
        StringBuilder moduleKeysBuilder = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            moduleKeysBuilder.append("1");
        }
        request.setModuleKeys(moduleKeysBuilder.toString()); // 长字符串
        request.setMrnversion("999.999.999");
        return request;
    }

    /**
     * 创建无效请求 - 产品ID为0
     */
    public static UnifiedPageRequest createInvalidProductIdRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(0L);
        request.setProductType(1);
        request.setClientType(100);
        request.setMrnversion("1.0.0");
        return request;
    }

    /**
     * 创建无效请求 - 负数产品ID
     */
    public static UnifiedPageRequest createNegativeProductIdRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(-1L);
        request.setProductType(1);
        request.setClientType(100);
        request.setMrnversion("1.0.0");
        return request;
    }

    /**
     * 创建成功响应
     */
    public static ProductDetailPageMergeResponse createSuccessResponse() {
        ProductDetailPageMergeResponse response = new ProductDetailPageMergeResponse();
        response.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        response.setMsg("success");
        return response;
    }

    /**
     * 创建失败响应
     */
    public static ProductDetailPageMergeResponse createFailureResponse() {
        ProductDetailPageMergeResponse response = new ProductDetailPageMergeResponse();
        response.setCode(PageResponseCodeEnum.FAILURE.getCode());
        response.setMsg("服务器开小差了，请稍后重试");
        return response;
    }

    /**
     * 创建未授权响应
     */
    public static ProductDetailPageMergeResponse createUnauthorizedResponse() {
        ProductDetailPageMergeResponse response = new ProductDetailPageMergeResponse();
        response.setCode(PageResponseCodeEnum.UNAUTHORIZED.getCode());
        response.setMsg(PageResponseCodeEnum.UNAUTHORIZED.getDesc());
        return response;
    }

    /**
     * 验证响应是否为成功状态
     */
    public static boolean isSuccessResponse(ProductDetailPageMergeResponse response) {
        return response != null && response.getCode() == PageResponseCodeEnum.SUCCESS.getCode();
    }

    /**
     * 验证响应是否为失败状态
     */
    public static boolean isFailureResponse(ProductDetailPageMergeResponse response) {
        return response != null && response.getCode() == PageResponseCodeEnum.FAILURE.getCode();
    }

    /**
     * 验证响应是否为未授权状态
     */
    public static boolean isUnauthorizedResponse(ProductDetailPageMergeResponse response) {
        return response != null && response.getCode() == PageResponseCodeEnum.UNAUTHORIZED.getCode();
    }

    /**
     * 验证响应消息
     */
    public static boolean hasExpectedMessage(ProductDetailPageMergeResponse response, String expectedMessage) {
        return response != null && expectedMessage.equals(response.getMsg());
    }

    /**
     * 创建包含特殊字符的请求
     */
    public static UnifiedPageRequest createSpecialCharacterRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(12345L);
        request.setProductType(1);
        request.setClientType(100);
        request.setModuleKeys("header,detail,特殊字符模块,emoji😀");
        request.setMrnversion("1.0.0-beta+特殊版本");
        return request;
    }

    /**
     * 创建空字符串字段的请求
     */
    public static UnifiedPageRequest createEmptyStringFieldsRequest() {
        UnifiedPageRequest request = new UnifiedPageRequest();
        request.setProductId(12345L);
        request.setProductType(1);
        request.setClientType(100);
        request.setModuleKeys("");
        request.setMrnversion("");
        return request;
    }

    /**
     * 验证请求的基本有效性
     */
    public static boolean isValidRequest(UnifiedPageRequest request) {
        return request != null
                && request.getProductId() > 0
                && request.getProductType() > 0
                && request.getClientType() > 0;
    }

    /**
     * 获取请求的描述信息，用于测试日志
     */
    public static String getRequestDescription(UnifiedPageRequest request) {
        if (request == null) {
            return "null request";
        }
        return String.format("Request[productId=%d, productType=%d, clientType=%d]",
                request.getProductId(), request.getProductType(), request.getClientType());
    }

    /**
     * 获取响应的描述信息，用于测试日志
     */
    public static String getResponseDescription(ProductDetailPageMergeResponse response) {
        if (response == null) {
            return "null response";
        }
        return String.format("Response[code=%d, msg=%s]", response.getCode(), response.getMsg());
    }
}
