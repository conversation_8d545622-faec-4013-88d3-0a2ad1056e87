package com.sankuai.dz.api.module.arrange.framework.component.spi;

import com.sankuai.dz.api.module.arrange.framework.component.definition.ComponentDefinition;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContext;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BaseComponentTest {

    @Mock
    private ComponentDefinition componentDefinition;

    @InjectMocks
    private BaseComponent baseComponent = new BaseComponent() {

        @Override
        protected void doInit(final RunnerContext runnerContext) {
            // Not needed for these tests
        }
    };

    /**
     * Test case for when the dependency fetcher name exists in the set
     */
    @Test
    public void testContainsDependencyFetcherWhenExists() {
        // arrange
        String dependencyFetcherName = "testFetcher";
        Set<String> fetcherNames = new HashSet<>();
        fetcherNames.add(dependencyFetcherName);
        when(componentDefinition.getDependentFetcherNames()).thenReturn(fetcherNames);
        // act
        boolean result = baseComponent.containsDependencyFetcher(dependencyFetcherName);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for when the dependency fetcher name does not exist in the set
     */
    @Test
    public void testContainsDependencyFetcherWhenNotExists() {
        // arrange
        String dependencyFetcherName = "testFetcher";
        Set<String> fetcherNames = new HashSet<>();
        when(componentDefinition.getDependentFetcherNames()).thenReturn(fetcherNames);
        // act
        boolean result = baseComponent.containsDependencyFetcher(dependencyFetcherName);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when the dependency fetcher name is null
     */
    @Test
    public void testContainsDependencyFetcherWithNullName() {
        // arrange
        Set<String> fetcherNames = new HashSet<>();
        fetcherNames.add("someFetcher");
        when(componentDefinition.getDependentFetcherNames()).thenReturn(fetcherNames);
        // act
        boolean result = baseComponent.containsDependencyFetcher(null);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for when component definition returns null for dependent fetcher names
     */
    @Test
    public void testContainsDependencyFetcherWithNullDependentFetcherNames() {
        // arrange
        String dependencyFetcherName = "testFetcher";
        when(componentDefinition.getDependentFetcherNames()).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> baseComponent.containsDependencyFetcher(dependencyFetcherName));
    }
}
