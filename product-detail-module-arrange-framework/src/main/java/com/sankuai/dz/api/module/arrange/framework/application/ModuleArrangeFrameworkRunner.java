package com.sankuai.dz.api.module.arrange.framework.application;

import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.application.constant.FrameworkRunnerConstant;
import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.api.module.arrange.framework.application.response.FrameworkRunnerResult;
import com.sankuai.dz.api.module.arrange.framework.builder.engine.BuilderEngine;
import com.sankuai.dz.api.module.arrange.framework.builder.exception.BuilderFatalError;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.BuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.storage.BuilderDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.context.RunnerContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDAGStorage;
import com.sankuai.dz.api.module.arrange.framework.lowcode.LowCodeContext;
import com.sankuai.dz.api.module.arrange.framework.lowcode.LowCodeContextBuilder;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/4 13:38
 */
@Slf4j
@SuppressWarnings("rawtypes")
public class ModuleArrangeFrameworkRunner {

    public static FrameworkRunnerResult run(final ModuleArrangeRequest moduleArrangeRequest) {
        long startTime = System.currentTimeMillis();
        //初始化商品详情页配置化上下文
        final LowCodeContext lowCodeContext = LowCodeContextBuilder.build(moduleArrangeRequest);
        //Fetcher返回值上下文初始化
        final ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap = new ConcurrentHashMap<>();
        //存储上下文到ThreadLocal中
        RunnerContext.setRunnerContext(new RunnerContext(
                moduleArrangeRequest.getRequest(), fetcherContextMap, lowCodeContext
        ));
        //获取所有builder定义
        final List<BuilderDefinition> builderDefinitions = getBuilderDefinitions(moduleArrangeRequest);
        //获取完整DAG
        final DAG dag = getDag(builderDefinitions);
        //根据builder依赖的Fetcher裁剪DAG，实现按需索取
        final Set<String> targetFetcherNames = getTargetFetcherNames(
                moduleArrangeRequest.getRequest(), builderDefinitions, lowCodeContext, dag
        );
        //统计metadata耗时
        Cat.newCompletedTransactionWithDuration(FrameworkRunnerConstant.FRAMEWORK_RUNNER_CAT_NAME, "metadata", System.currentTimeMillis() - startTime);
        //初始化FetcherEngine
        final FetcherEngine fetcherEngine = new FetcherEngine(
                moduleArrangeRequest.getRequest(), dag, targetFetcherNames, fetcherContextMap, lowCodeContext
        );
        try {
            //执行fetch，执行结果在FetcherEngine中
            fetcherEngine.run();
            //初始化BuilderEngine
            final BuilderEngine builderEngine = new BuilderEngine(builderDefinitions);
            //执行模块Builder
            builderEngine.run();
            //构建返回值
            return new FrameworkRunnerResult(builderDefinitions, targetFetcherNames, fetcherEngine, builderEngine);
        } finally {
            //删除ThreadLocal中的上下文，防止内存溢出
            RunnerContext.clearRunnerContext();
        }
    }

    /**
     * 根据入参获取所有builderDefinitions
     */
    private static List<BuilderDefinition> getBuilderDefinitions(ModuleArrangeRequest request) {
        return request.getModuleKeys().stream()
                .map(moduleKey -> {
                    try {
                        return BuilderDefinitionStorage.builderStorageOfModuleKey.get(moduleKey);
                    } catch (Exception e) {
                        log.error("FATAL ERROR!!!根据入参ModuleKey({})获取Builder失败，忽略该module，请尽快排查问题!!!", moduleKey, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据builder获取startFetcher，从而获取DAG
     */
    private static DAG getDag(final List<BuilderDefinition> builderDefinitions) {
        //获取所有builder的开始fetcher节点
        Set<String> startFetcherNames = builderDefinitions.stream()
                .map(BuilderDefinition::getStartFetcherDefinition)
                .map(FetcherDefinition::getFetcherName)
                .collect(Collectors.toSet());
        //暂时不允许出现一次请求涉及多个开始fetcher节点（即多个DAG）
        if (startFetcherNames.size() > 1) {
            throw new BuilderFatalError("当前请求的moduleKeys包含多个startFetcher，暂不支持这种调用");
        }
        String startFetcherName = startFetcherNames.stream().findFirst().orElse(null);
        if (StringUtils.isBlank(startFetcherName)) {
            throw new BuilderFatalError("当前请求的moduleKeys对应的startFetcher为空");
        }
        //获取DAG
        return FetcherDAGStorage.getDAG(startFetcherName);
    }

    /**
     * 根据builder构建DAG中所有需要的Fetcher，实现按需索取，减少无意义请求
     */
    private static Set<String> getTargetFetcherNames(final ProductDetailPageRequest request,
                                                     final List<BuilderDefinition> builderDefinitions,
                                                     final LowCodeContext lowCodeContext,
                                                     final DAG dag) {
        //获取所有builder依赖的fetcher
        Set<String> dependentFetchers = builderDefinitions.stream()
                .map(builderDefinition -> builderDefinition.getRuntimeDependentFetcherNames(request))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        //merge配置中动态依赖的fetcher
        dependentFetchers.addAll(lowCodeContext.getAllDependentFetcherNames());
        //从DAG中找到所有依赖fetcher的祖先fetcher
        Set<String> allAncestorsOfDependentFetchers = dependentFetchers.stream()
                .map(dag::getAncestors)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        //合并依赖fetcher和依赖fetcher的祖先fetcher，即本次请求涉及的所有fetcher，可做到按需调用
        Set<String> targetFetcherNames = new HashSet<>();
        targetFetcherNames.addAll(dependentFetchers);
        targetFetcherNames.addAll(allAncestorsOfDependentFetchers);
        return targetFetcherNames;
    }

}
