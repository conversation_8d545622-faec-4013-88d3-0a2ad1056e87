package com.sankuai.dz.product.detail.gateway.spi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/1/8 19:19
 */
@Data
@TypeDoc(description = "聚合接口返回值")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDetailPageResponse implements Serializable {

    @FieldDoc(description = "状态码", requiredness = Requiredness.REQUIRED, type = PageResponseCodeEnum.class)
    private int code;

    @FieldDoc(description = "失败原因", rule = "code != MergeResponseCodeEnum.SUCCESS.getCode()才有效", requiredness = Requiredness.OPTIONAL)
    private String msg;

    @FieldDoc(description = "AB结果聚合", requiredness = Requiredness.OPTIONAL)
    private List<ABResultDTO> abResultList;

    @FieldDoc(description = "模块返回值，具体模块是否可用也需要判断", requiredness = Requiredness.REQUIRED)
    private Map<String, ModuleResponse> moduleResponse;

    public static ProductDetailPageResponse buildResponse(final List<ModuleResponse> moduleList,
                                                          final List<ABResultDTO> abResultList) {
        boolean isPartlyFail = false;
        Map<String, ModuleResponse> moduleResponse = new HashMap<>();
        for (ModuleResponse response : moduleList) {
            if (response == null || StringUtils.isBlank(response.getModuleKey())) {
                continue;
            }
            if (!response.isSuccess()) {
                isPartlyFail = true;
            }
            moduleResponse.put(response.getModuleKey(), response);
        }
        return ProductDetailPageResponse.builder()
                .code(isPartlyFail ? PageResponseCodeEnum.PARTLY_FAIL.getCode() : PageResponseCodeEnum.SUCCESS.getCode())
                .moduleResponse(moduleResponse)
                .abResultList(abResultList)
                .build();
    }

    public static ProductDetailPageResponse fail(final String failReason) {
        return ProductDetailPageResponse.builder().code(PageResponseCodeEnum.FAILURE.getCode()).msg(failReason).build();
    }

}
