package com.sankuai.dz.product.detail.page.low.code.entity.component.metadata;

import com.sankuai.athena.digital.arch.annotations.DigitalArchClass;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: guangyujie
 * @Date: 2025/5/28 19:09
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@DigitalArchClass
public @interface ComponentMetadata {

    /**
     * 组件key，模块内唯一
     */
    String componentKey();

    /**
     * 模块key，全局唯一
     */
    String moduleKey() default "";

    /**
     * 组件描述
     */
    String desc() default "";

    /**
     * 组件类别
     */
    ComponentGroupEnum componentGroup();

    /**
     * 组件分类
     */
    ComponentTypeEnum componentType();

}
