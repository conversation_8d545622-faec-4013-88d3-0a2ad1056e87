package com.sankuai.dz.api.module.arrange.framework.fetcher.engine;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dz.api.module.arrange.framework.application.constant.FrameworkRunnerConstant;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.constant.FetcherConstant;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.storage.FetcherDefinitionStorage;
import com.sankuai.dz.api.module.arrange.framework.lowcode.LowCodeContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 16:08
 */
@Slf4j
@Getter
@SuppressWarnings("rawtypes")
public class FetcherEngine {

    /**
     * 公共参数
     * tips：非只读，非全局变量，随意修改只会影响单次请求结果
     */
    private final ProductDetailPageRequest request;

    /**
     * 整个有向无环图元数据
     * 全局变量内部所有属性只读
     */
    private final DAG dag;

    /**
     * 目标Fetcher，用于按需索取，如果为空则无效
     */
    private final Set<String> targetFetcherNames;

    /**
     * 记录所有Fetcher的执行过程数据
     */
    private final Map<String, BaseFetcherContext> fetcherContextMap;

    /**
     * 低代码配置化上下文，实现Fetcher取数的配置化能力
     */
    private final LowCodeContext lowCodeContext;

    public FetcherEngine(final ProductDetailPageRequest request,
                         final DAG dag,
                         final Set<String> targetFetcherNames,
                         final ConcurrentHashMap<String, BaseFetcherContext> fetcherContextMap,
                         final LowCodeContext lowCodeContext) {
        this.request = request;
        this.dag = dag;
        this.targetFetcherNames = Collections.unmodifiableSet(targetFetcherNames);
        this.fetcherContextMap = fetcherContextMap;
        this.lowCodeContext = lowCodeContext;
    }

    @SuppressWarnings("unchecked")
    public void run() {
        long startTime = System.currentTimeMillis();
        final List<String> topologicalSort = this.dag.getTopologicalSort();
        for (final String fetcherName : topologicalSort) {
            if (!targetFetcherNames.contains(fetcherName)) {
                continue;//避免无效调用，按需索取
            }
            final FetcherDefinition fetcherDefinition = FetcherDefinitionStorage.getFetcherDefinition(fetcherName);
            List<BaseFetcherContext> dependencies = getContextsOfDependencies(
                    this.fetcherContextMap, fetcherDefinition
            );
            try {
                CompletableFuture
                        .allOf(dependencies.stream()
                                .map(BaseFetcherContext::getFuture)
                                .filter(Objects::nonNull)//过滤掉有问题的Fetcher，不影响整个链路执行
                                .toArray(CompletableFuture[]::new))
                        .get(1000, TimeUnit.MILLISECONDS);
            } catch (Exception ignored) {
            }
            final BaseFetcherContext self = buildFetcherContext(fetcherDefinition);
            if (self == null) {
                continue;
            }
            self.checkFutureAndSetDefaultFuture();
            CompletableFuture<FetcherResponse<? extends FetcherReturnValueDTO>> future = self.getFuture();
            future.whenComplete((fetcherResponse, throwable) -> {
                        FetcherResponse<? extends FetcherReturnValueDTO> response;
                        if (throwable != null) {
                            response = FetcherResponse.fail(throwable);
                        } else {
                            response = Optional.ofNullable(fetcherResponse).orElse(FetcherResponse.fail(new FetcherFatalException("FetcherResponse为空，理论上不应该出现!!!")));
                        }
                        //打点
                        doLog(self, response);
                    });
        }
        Cat.newCompletedTransactionWithDuration(FrameworkRunnerConstant.FRAMEWORK_RUNNER_CAT_NAME, "fetcher", System.currentTimeMillis() - startTime);
    }

    @SuppressWarnings("unchecked")
    private BaseFetcherContext buildFetcherContext(final FetcherDefinition fetcherDefinition) {
        try {
            BaseFetcherContext self;//当前fetcher的上下文
            if (this.fetcherContextMap.containsKey(fetcherDefinition.getFetcherName())) {
                //如果前置fetcher已经代为初始化了则这里直接返回
                self = this.fetcherContextMap.get(fetcherDefinition.getFetcherName());
            } else {
                //如果前置fetcher没有代为初始化则进行初始化
                BaseFetcherContext fetcherContext = FetcherDefinitionStorage.getFetcherContext(fetcherDefinition);
                FetcherContextInitResult fetcherContextInitResult = fetcherContext.initContext(fetcherDefinition, this);
                //把当前fetcher的上下文存入引擎
                self = fetcherContextInitResult.getSelf();
                this.fetcherContextMap.put(self.getFetcherDefinition().getFetcherName(), self);
                //把当前fetcher代为初始化的fetcher的上下文存入引擎
                if (CollectionUtils.isNotEmpty(fetcherContextInitResult.getOthers())) {
                    for (BaseFetcherContext other : fetcherContextInitResult.getOthers()) {
                        if (other == null) {
                            continue;//不影响其他ComponentFetcher执行
                        }
                        this.fetcherContextMap.put(other.getFetcherDefinition().getFetcherName(), other);
                    }
                }
            }
            if (self == null) {
                throw new FetcherFatalException(String.format(
                        "Fatal Error!!!Fetcher(%s)构建的上下文为null!!!请立刻排查问题",
                        fetcherDefinition.getFetcherName()
                ));
            }
            return self;
        } catch (Throwable throwable) {
            log.error("Fatal Error!!!({}).buildFetcherContext失败，理论上不可能，尽快排查问题", fetcherDefinition.getFetcherName(), throwable);
            return null;
        }
    }

    private List<BaseFetcherContext> getContextsOfDependencies(final Map<String, BaseFetcherContext> fetcherContextMap,
                                                               final FetcherDefinition fetcherDefinition) {
        Collection<String> dependencies = CollectionUtils.intersection(
                fetcherDefinition.getNameOfPreviousLayerDependenciesInDAG(), this.targetFetcherNames
        );
        return dependencies.stream().map(fetcherContextMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static void doLog(final BaseFetcherContext fetcherContext,
                              final FetcherResponse<? extends FetcherReturnValueDTO> response) {
        long processTime = System.currentTimeMillis() - fetcherContext.getStartTime();
        String fetcherName = fetcherContext.getFetcherDefinition().getFetcherName();
        if (response.isSuccess()) {
            //成功打点
            Cat.newCompletedTransactionWithDuration(FetcherConstant.FETCHER_CAT_NAME, fetcherName, processTime);
        } else {
            Throwable optionalThrowable = Optional.ofNullable(response.getError()).orElse(new FetcherFatalException("未知异常"));
            //统一失败日志
            log.error("{} fail", fetcherName, optionalThrowable);
            //失败打点
            Transaction transaction = Cat.newTransactionWithDuration(FetcherConstant.FETCHER_CAT_NAME, fetcherName, processTime);
            transaction.setStatus(optionalThrowable);
            transaction.complete();
        }
    }

}
