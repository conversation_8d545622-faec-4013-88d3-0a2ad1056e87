package com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto;

import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/4/25 11:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigQueryResponse implements Serializable {

    private String routingKey;

    private PageDisplayConfigBO config;

}