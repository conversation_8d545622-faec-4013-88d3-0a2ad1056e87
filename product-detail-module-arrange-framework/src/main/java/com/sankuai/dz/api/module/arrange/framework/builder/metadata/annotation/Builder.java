package com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.ModuleLevelEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * @Author: guangyujie
 * @Date: 2025/1/26 10:26
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
@Scope("prototype")
@SuppressWarnings("rawtypes")
public @interface Builder {

    /**
     * 是否是可变Builder
     */
    BuilderTypeEnum builderType() default BuilderTypeEnum.COMMON_BUILDER;

    /**
     * 模块key，全局唯一
     * 参考https://km.sankuai.com/collabpage/2695513940，暂时用文档维护，后续会用系统维护
     */
    String moduleKey();

    /**
     * 依赖的Fetcher开始节点
     */
    Class<? extends BaseFetcherContext> startFetcher();

    /**
     * startFetcher的DAG中所有依赖的Fetcher
     */
    Class<? extends BaseFetcherContext>[] dependentFetchers();

}
