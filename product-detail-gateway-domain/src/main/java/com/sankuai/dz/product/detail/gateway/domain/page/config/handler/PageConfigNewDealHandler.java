package com.sankuai.dz.product.detail.gateway.domain.page.config.handler;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.api.bff.enums.PageConfigSceneEnum;
import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.domain.page.config.PageConfigBaseHandler;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ScreenFlagEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.metadata.sdk.error.SPIConfigFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/2/18 18:21
 */
@Component
public class PageConfigNewDealHandler extends PageConfigBaseHandler {

    @Override
    public Set<PageRegionEnum> getSupportedPageRegion() {
        return Sets.newHashSet(
                PageRegionEnum.ALL_SCREEN,
                PageRegionEnum.FIRST_SCREEN,
                PageRegionEnum.NOT_FIRST_SCREEN,
                PageRegionEnum.SELECTED_MODULE
        );
    }

    @Override
    protected void checkPageConfig(final PageConfigDataDTO pageConfig,
                                   final Map<String, BizModuleConfigDTO> bizModuleMap) throws SPIConfigFatalException {
        if (MapUtils.isEmpty(bizModuleMap)) {
            throw new SPIConfigFatalException("空模块配置");
        }
    }

    @Override
    protected PageConfigSceneEnum getPageConfigSceneEnum() {
        return PageConfigSceneEnum.ProductDetail;
    }

    @Override
    protected boolean isNeed(final UnifiedPageRequest unifiedPageRequest,
                             final ProductDetailPageRequest detailPageRequest,
                             final BizModuleConfigDTO bizModuleConfigDTO) {
        Set<String> moduleWhiteList = detailPageRequest.getModuleKeys();
        int pageRegion = unifiedPageRequest.getPageRegion();
        // 指定模块优先级最高
        if (CollectionUtils.isNotEmpty(moduleWhiteList)) {
            return moduleWhiteList.contains(bizModuleConfigDTO.getModuleKey());
        }
        // 指定首屏
        if (pageRegion == PageRegionEnum.FIRST_SCREEN.getCode()) {
            return bizModuleConfigDTO.getFirstScreenFlag() == ScreenFlagEnum.FIRST_SCREEN.getCode();
        }
        // 指定非首屏
        if (pageRegion == PageRegionEnum.NOT_FIRST_SCREEN.getCode()) {
            return bizModuleConfigDTO.getFirstScreenFlag() == ScreenFlagEnum.NON_FIRST_SCREEN.getCode();
        }
        // 默认需要
        return true;
    }

}
