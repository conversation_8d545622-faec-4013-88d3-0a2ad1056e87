package com.sankuai.dz.product.detail.page.low.code.entity.spi.vo;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/5/22 15:30
 */
@Data
public abstract class ComponentVO implements Serializable {

    private String componentKey;

    /**
     * 获取组件类型
     */
    public abstract ComponentGroupEnum getComponentGroup();

}
