package com.sankuai.dz.product.detail.metadata.sdk.management;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * ProductDetailPageConfigRpcServiceImpl 完整测试套件
 * 包含所有相关的测试类，确保覆盖率大于70%
 *
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/1/15 21:00
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
        ProductDetailPageConfigRpcServiceImplTest.class,
        ProductDetailPageConfigRpcServiceImplEdgeCaseTest.class
})
public class ProductDetailPageConfigRpcServiceImplTestSuite {
    // 测试套件类，无需实现内容
}
