package com.sankuai.dz.product.detail.gateway.domain.page.config.handler;

import static org.junit.Assert.*;

import com.sankuai.dz.product.detail.gateway.api.bff.request.UnifiedPageRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageRegionEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ScreenFlagEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PageConfigNewDealHandlerTest {

    @InjectMocks
    private PageConfigNewDealHandler handler;

    @Test
    public void testIsNeed_WhenModuleWhiteListContainsKey_ShouldReturnTrue() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        Set<String> moduleWhiteList = new HashSet<>();
        moduleWhiteList.add("testModule");
        detailPageRequest.setModuleKeys(moduleWhiteList);
        bizModuleConfigDTO.setModuleKey("testModule");
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertTrue(result);
    }

    @Test
    public void testIsNeed_WhenModuleWhiteListNotContainsKey_ShouldReturnFalse() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        Set<String> moduleWhiteList = new HashSet<>();
        moduleWhiteList.add("otherModule");
        detailPageRequest.setModuleKeys(moduleWhiteList);
        bizModuleConfigDTO.setModuleKey("testModule");
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertFalse(result);
    }

    @Test
    public void testIsNeed_WhenFirstScreenAndModuleIsFirstScreen_ShouldReturnTrue() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setPageRegion(PageRegionEnum.FIRST_SCREEN.getCode());
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        bizModuleConfigDTO.setFirstScreenFlag(ScreenFlagEnum.FIRST_SCREEN.getCode());
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertTrue(result);
    }

    @Test
    public void testIsNeed_WhenFirstScreenButModuleNotFirstScreen_ShouldReturnFalse() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setPageRegion(PageRegionEnum.FIRST_SCREEN.getCode());
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        bizModuleConfigDTO.setFirstScreenFlag(ScreenFlagEnum.NON_FIRST_SCREEN.getCode());
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertFalse(result);
    }

    @Test
    public void testIsNeed_WhenNotFirstScreenAndModuleNotFirstScreen_ShouldReturnTrue() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setPageRegion(PageRegionEnum.NOT_FIRST_SCREEN.getCode());
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        bizModuleConfigDTO.setFirstScreenFlag(ScreenFlagEnum.NON_FIRST_SCREEN.getCode());
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertTrue(result);
    }

    @Test
    public void testIsNeed_WhenNotFirstScreenButModuleIsFirstScreen_ShouldReturnFalse() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setPageRegion(PageRegionEnum.NOT_FIRST_SCREEN.getCode());
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        bizModuleConfigDTO.setFirstScreenFlag(ScreenFlagEnum.FIRST_SCREEN.getCode());
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertFalse(result);
    }

    @Test
    public void testIsNeed_WhenDefaultCase_ShouldReturnTrue() throws Throwable {
        UnifiedPageRequest unifiedPageRequest = new UnifiedPageRequest();
        unifiedPageRequest.setPageRegion(PageRegionEnum.ALL_SCREEN.getCode());
        ProductDetailPageRequest detailPageRequest = new ProductDetailPageRequest();
        BizModuleConfigDTO bizModuleConfigDTO = new BizModuleConfigDTO() {

            @Override
            public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
                return null;
            }

            @Override
            public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
                return new ArrayList<>();
            }

            @Override
            public ModuleTypeEnum getModuleType() {
                return null;
            }
        };
        boolean result = handler.isNeed(unifiedPageRequest, detailPageRequest, bizModuleConfigDTO);
        assertTrue(result);
    }
}
