package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ComponentFetcherContextTest {

    @Mock
    private FetcherEngine fetcherEngine;

    /**
     * Test doInitContextExceptionally with a RuntimeException
     */
    @Test
    public void testDoInitContextExceptionallyWithRuntimeException() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("Test exception");
        TestComponentFetcherContext<TestAggregateResult> context = new TestComponentFetcherContext<>();
        // act
        FetcherContextInitResult result = context.doInitContextExceptionally(fetcherEngine, expectedException);
        // assert
        assertNotNull(result);
        assertSame(context, result.getSelf());
        CompletableFuture<FetcherResponse<TestAggregateResult>> future = context.getFuture();
        assertNotNull(future);
        assertTrue(future.isDone());
        FetcherResponse<?> response = future.get();
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(expectedException, response.getError());
    }

    /**
     * Test doInitContextExceptionally with null throwable
     */
    @Test
    public void testDoInitContextExceptionallyWithNullThrowable() throws Throwable {
        // arrange
        TestComponentFetcherContext<TestAggregateResult> context = new TestComponentFetcherContext<>();
        // act
        FetcherContextInitResult result = context.doInitContextExceptionally(fetcherEngine, null);
        // assert
        assertNotNull(result);
        assertSame(context, result.getSelf());
        CompletableFuture<FetcherResponse<TestAggregateResult>> future = context.getFuture();
        assertNotNull(future);
        assertTrue(future.isDone());
        FetcherResponse<?> response = future.get();
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getError());
    }

    /**
     * Test doInitContextExceptionally with checked exception
     */
    @Test
    public void testDoInitContextExceptionallyWithCheckedException() throws Throwable {
        // arrange
        Exception expectedException = new Exception("Test checked exception");
        TestComponentFetcherContext<TestAggregateResult> context = new TestComponentFetcherContext<>();
        // act
        FetcherContextInitResult result = context.doInitContextExceptionally(fetcherEngine, expectedException);
        // assert
        assertNotNull(result);
        assertSame(context, result.getSelf());
        CompletableFuture<FetcherResponse<TestAggregateResult>> future = context.getFuture();
        assertNotNull(future);
        assertTrue(future.isDone());
        FetcherResponse<?> response = future.get();
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals(expectedException, response.getError());
    }

    // Test implementation classes
    private static class TestComponentFetcherContext<T extends FetcherReturnValueDTO> extends ComponentFetcherContext<Object, T, T> {

        @Override
        public void fulfillRequest(Object request) {
        }

        @Override
        protected FetcherResponse<T> mapResult(FetcherResponse<T> aggregateResult) {
            return null;
        }

        @Override
        public FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }
    }

    private static class TestAggregateResult extends FetcherReturnValueDTO {
    }
}
