package com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.card;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.biz.BizComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.ContainerComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.container.dto.ContainerShowRuleConfig;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentGroupEnum;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.enums.ComponentTypeEnum;
import lombok.Getter;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/5/29 17:18
 */
@Getter
public class CardContainerComponentDisplayConfigBO extends ContainerComponentDisplayConfigBO {

    private final List<BizComponentDisplayConfigBO> bizComponentDisplayConfigBOList;

    @Override
    public List<? extends ComponentDisplayConfigBO> getSubComponentDisplayConfigs() {
        return bizComponentDisplayConfigBOList;
    }

    public CardContainerComponentDisplayConfigBO(final String componentKey,
                                                 final ContainerShowRuleConfig showRuleConfig,
                                                 final List<BizComponentDisplayConfigBO> bizComponentDisplayConfigBOList) {
        super(componentKey, showRuleConfig);
        this.bizComponentDisplayConfigBOList = Collections.unmodifiableList(
                bizComponentDisplayConfigBOList
        );
    }

    @Override
    public List<DataSourceConfigBO> getDataSourceConfigs() {
        return bizComponentDisplayConfigBOList
                .stream()
                .map(BizComponentDisplayConfigBO::getDataSourceConfigs)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Override
    public ComponentGroupEnum getComponentGroup() {
        return ComponentGroupEnum.Container;
    }

    @Override
    public Set<ComponentTypeEnum> getSupportedComponentType() {
        return Sets.newHashSet(
                ComponentTypeEnum.Static_Card_Container,
                ComponentTypeEnum.Unroll_Container
        );
    }

}
