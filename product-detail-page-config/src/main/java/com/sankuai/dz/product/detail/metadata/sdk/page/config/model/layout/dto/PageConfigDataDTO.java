package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.mtrace.util.StringUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 18:54
 */
@Data
@TypeDoc(description = "页面配置")
public class PageConfigDataDTO implements Serializable {

    @FieldDoc(description = "模块组列表", requiredness = Requiredness.REQUIRED)
    private List<ModuleGroupConfigDTO> moduleGroupConfigs = new ArrayList<>();

    public List<List<ModuleConfigVO>> buildVO(final BuildModuleVORequest request) {
        return moduleGroupConfigs.stream()
                .map(ModuleGroupConfigDTO::getModuleList)
                .map(moduleList -> moduleList.stream()
                        .map(module -> module.buildVO(request, 0))
                        .collect(Collectors.toList())
                ).collect(Collectors.toList());
    }

    @JsonIgnore
    public Map<String, ModuleConfigDTO> getAllModule() {
        return moduleGroupConfigs.stream()
                .flatMap(group -> group.getModuleList().stream())
                .flatMap(module -> module.getAllModuleConfigs().stream())
                .collect(Collectors.toMap(
                        ModuleConfigDTO::getModuleKey,
                        v -> v,
                        (v1, v2) -> v1
                ));
    }

    @JsonIgnore
    public Map<String, BizModuleConfigDTO> getAllBizModule() {
        return moduleGroupConfigs.stream()
                .flatMap(group -> group.getModuleList().stream())
                .flatMap(module -> module.getAllModuleConfigs().stream())
                .filter(ModuleConfigDTO::isBizModule)
                .map(module -> (BizModuleConfigDTO) module)
                .collect(Collectors.toMap(
                        BizModuleConfigDTO::getModuleKey,
                        v -> v,
                        (v1, v2) -> v1
                ));
    }

    /**
     * 匹配模块
     *
     * @param groupName  如果不为空，则按照groupName筛选group
     * @param moduleKey  模块类型
     * @return 适配的group组
     */
    @JsonIgnore
    public ModuleGroupConfigDTO getModuleGroup(String groupName, String moduleKey) {
        for (ModuleGroupConfigDTO moduleGroupConfig : moduleGroupConfigs) {
            if (StringUtils.isNotBlank(groupName) && !groupName.equals(moduleGroupConfig.getGroupName())) {
                continue;
            }
            if (moduleGroupConfig.getModuleList().stream().anyMatch(module -> module.getModuleKey().equals(moduleKey))) {
                return moduleGroupConfig;
            }
        }
        return null;
    }

}
