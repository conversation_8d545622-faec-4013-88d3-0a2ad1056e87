package com.sankuai.dz.product.detail.page.config.utils.json;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class JsonClassRemoverTest {

    /**
     * 测试处理普通JSON字符串，不包含@class和moduleResponse
     */
    @Test
    public void testProcessNormalJson() throws Throwable {
        // arrange
        String originalJson = "{\"name\":\"test\",\"value\":123}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        assertEquals("test", resultNode.get("name").asText());
        assertEquals(123, resultNode.get("value").asInt());
    }

    /**
     * 测试处理包含@class字段的JSON
     */
    @Test
    public void testProcessJsonWithClassField() throws Throwable {
        // arrange
        String originalJson = "{\"@class\":\"com.example.Test\",\"name\":\"test\"}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        assertNull(resultNode.get("@class"));
        assertEquals("test", resultNode.get("name").asText());
    }

    /**
     * 测试处理包含moduleResponse字段的JSON
     */
    @Test
    public void testProcessJsonWithModuleResponse() throws Throwable {
        // arrange
        String originalJson = "{\"moduleResponse\":{\"{\\\"value\\\":\\\"key1\\\"}\":\"value1\"}}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        JsonNode moduleResponse = resultNode.get("moduleResponse");
        assertNotNull(moduleResponse);
        assertEquals("value1", moduleResponse.get("key1").asText());
    }

    /**
     * 测试处理JSON解析异常
     */
    @Test
    public void testProcessJsonParsingException() throws Throwable {
        // arrange
        String invalidJson = "{invalid json}";
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> JsonClassRemover.process(invalidJson));
        assertTrue(exception.getCause() instanceof com.fasterxml.jackson.core.JsonParseException);
    }

    /**
     * 测试处理moduleResponse键转换异常
     */
    @Test
    public void testProcessModuleResponseKeyConversionException() throws Throwable {
        // arrange
        String originalJson = "{\"moduleResponse\":{\"invalidKey\":\"value\"}}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        JsonNode moduleResponse = resultNode.get("moduleResponse");
        assertNotNull(moduleResponse);
        assertEquals("value", moduleResponse.get("invalidKey").asText());
    }

    /**
     * 测试处理空JSON字符串
     */
    @Test
    public void testProcessEmptyJson() throws Throwable {
        // arrange
        String originalJson = "{}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        assertTrue(resultNode.isEmpty());
    }

    /**
     * 测试处理null输入
     */
    @Test
    public void testProcessNullInput() throws Throwable {
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> JsonClassRemover.process(null));
        assertTrue(exception.getCause() instanceof IllegalArgumentException);
    }

    /**
     * 测试处理复杂嵌套结构JSON
     */
    @Test
    public void testProcessComplexNestedJson() throws Throwable {
        // arrange
        String originalJson = "{\"@class\":\"com.example.Root\",\"moduleResponse\":{\"{\\\"value\\\":\\\"key1\\\"}\":{\"nested\":{\"@class\":\"com.example.Nested\"}}},\"array\":[{\"@class\":\"com.example.ArrayItem\"}]}";
        // act
        String result = JsonClassRemover.process(originalJson);
        // assert
        assertNotNull(result);
        JsonNode resultNode = new ObjectMapper().readTree(result);
        assertNull(resultNode.get("@class"));
        JsonNode moduleResponse = resultNode.get("moduleResponse");
        assertNotNull(moduleResponse);
        JsonNode key1Node = moduleResponse.get("key1");
        assertNotNull(key1Node);
        assertNull(key1Node.get("nested").get("@class"));
        assertNull(resultNode.get("array").get(0).get("@class"));
    }
}
