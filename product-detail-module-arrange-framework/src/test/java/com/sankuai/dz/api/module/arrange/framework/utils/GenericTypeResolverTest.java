package com.sankuai.dz.api.module.arrange.framework.utils;

import org.junit.Assert;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/2/10 00:58
 */
public class GenericTypeResolverTest {

    @Test
    public void test() {
        // 定义多层继承结构
        class Grandparent<T, A> {
        }
        class Parent<U, T> extends Grandparent<T, String> {
        }
        class Child extends Parent<String, Integer> {
        }

        // 获取 Grandparent 的第 0 个泛型实际类型
        Class<?> actualType = GenericTypeResolver.getGenericActualType(Child.class, Parent.class, 1);
        System.out.println("泛型实际类型: " + actualType.getName());
        Assert.assertEquals(actualType.getName(),"java.lang.Integer");
    }

}