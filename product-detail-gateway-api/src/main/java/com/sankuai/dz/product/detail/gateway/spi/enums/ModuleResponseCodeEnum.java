package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

/**
 * @Author: guangyu<PERSON><PERSON>
 * @Date: 2025/1/23 11:28
 */
@Getter
public enum ModuleResponseCodeEnum {

    SUCCESS(200, "成功"),
    FAILURE(500, "失败");

    private final int code;

    private final String desc;

    ModuleResponseCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ModuleResponseCodeEnum fromCode(int code) {
        for (ModuleResponseCodeEnum value : ModuleResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (ModuleResponseCodeEnum value : ModuleResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}