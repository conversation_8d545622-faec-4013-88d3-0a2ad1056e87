package com.sankuai.dz.product.detail.gateway.spi.enums;

import static org.junit.Assert.*;
import org.junit.Test;
import static org.mockito.Mockito.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ClientTypeEnumTest {

    /**
     * 测试 fromCode 方法，当 code 参数与枚举值的 code 属性相等时，应返回对应的枚举值
     */
    @Test
    public void testFromCode_CodeEqualsEnumCode() {
        // arrange
        int code = 100;
        // act
        ClientTypeEnum result = ClientTypeEnum.fromCode(code);
        // assert
        assertEquals(ClientTypeEnum.DP_APP, result);
    }

    /**
     * 测试 fromCode 方法，当 code 参数与所有枚举值的 code 属性都不相等时，应返回 UNKNOWN
     */
    @Test
    public void testFromCode_CodeNotEqualsAnyEnumCode() {
        // arrange
        int code = 999;
        // act
        ClientTypeEnum result = ClientTypeEnum.fromCode(code);
        // assert
        assertEquals(ClientTypeEnum.UNKNOWN, result);
    }

    @Test
    public void testContainsCode_WhenCodeExistsMinimumValue_ShouldReturnTrue() {
        // arrange
        final int existingCode = ClientTypeEnum.UNKNOWN.getCode();
        // act
        boolean result = ClientTypeEnum.containsCode(existingCode);
        // assert
        assertTrue("Should return true for existing minimum code value", result);
    }

    @Test
    public void testContainsCode_WhenCodeExistsMaximumValue_ShouldReturnTrue() {
        // arrange
        // MT_WAN_WU_XCX code
        final int existingCode = 504;
        // act
        boolean result = ClientTypeEnum.containsCode(existingCode);
        // assert
        assertTrue("Should return true for existing maximum code value", result);
    }

    @Test
    public void testContainsCode_WhenCodeExistsMiddleRange_ShouldReturnTrue() {
        // arrange
        final int existingCode = ClientTypeEnum.MT_APP.getCode();
        // act
        boolean result = ClientTypeEnum.containsCode(existingCode);
        // assert
        assertTrue("Should return true for existing middle range code value", result);
    }

    @Test
    public void testContainsCode_WhenCodeNotExistsNegativeValue_ShouldReturnFalse() {
        // arrange
        final int nonExistingCode = -1;
        // act
        boolean result = ClientTypeEnum.containsCode(nonExistingCode);
        // assert
        assertFalse("Should return false for non-existing negative code value", result);
    }

    @Test
    public void testContainsCode_WhenCodeNotExistsBetweenRanges_ShouldReturnFalse() {
        // arrange
        // Between DP_PC(104) and MT_APP(200)
        final int nonExistingCode = 150;
        // act
        boolean result = ClientTypeEnum.containsCode(nonExistingCode);
        // assert
        assertFalse("Should return false for non-existing code between ranges", result);
    }

    @Test
    public void testContainsCode_WhenCodeNotExistsAboveMaximum_ShouldReturnFalse() {
        // arrange
        // Above maximum code (504)
        final int nonExistingCode = 505;
        // act
        boolean result = ClientTypeEnum.containsCode(nonExistingCode);
        // assert
        assertFalse("Should return false for non-existing code above maximum value", result);
    }

    @Test
    public void testContainsCode_WhenCodeExistsDpApp_ShouldReturnTrue() {
        // arrange
        final int existingCode = ClientTypeEnum.DP_APP.getCode();
        // act
        boolean result = ClientTypeEnum.containsCode(existingCode);
        // assert
        assertTrue("Should return true for DP_APP code value", result);
    }

    @Test
    public void testContainsCode_WhenCodeExistsMtMap_ShouldReturnTrue() {
        // arrange
        final int existingCode = ClientTypeEnum.MT_MAP.getCode();
        // act
        boolean result = ClientTypeEnum.containsCode(existingCode);
        // assert
        assertTrue("Should return true for MT_MAP code value", result);
    }
}
