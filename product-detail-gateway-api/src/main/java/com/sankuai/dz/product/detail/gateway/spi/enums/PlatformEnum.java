package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/2/10 10:45
 */
@Getter
public enum PlatformEnum {

    UNKNOWN(0, "未知"),
    DP(1, "点评"),
    MT(2, "美团"),
    KAI_DIAN_BAO(3, "开店宝B端");

    private final int code;

    private final String desc;

    PlatformEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlatformEnum fromCode(int code) {
        for (PlatformEnum value : PlatformEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (PlatformEnum value : PlatformEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(PlatformEnum.values()).collect(Collectors.toMap(
                PlatformEnum::getCode,
                PlatformEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(PlatformEnum.values()).collect(Collectors.toMap(
                PlatformEnum::name,
                PlatformEnum::getDesc
        ));
    }

}