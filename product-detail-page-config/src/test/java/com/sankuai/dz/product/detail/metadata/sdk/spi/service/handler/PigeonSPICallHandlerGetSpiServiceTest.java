package com.sankuai.dz.product.detail.metadata.sdk.spi.service.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.sankuai.dz.product.detail.metadata.sdk.enums.InterfaceTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.spi.model.SPIConfigDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class PigeonSPICallHandlerGetSpiServiceTest {

    @Mock
    private GenericService genericService1;

    /**
     * Test that InvokerConfig is properly configured with values from SPIConfigDTO
     */
    @Test
    public void testGetSpiServiceConfiguresInvokerCorrectly() throws Throwable {
        // arrange
        String testUrl = "test.service.url";
        int testTimeout = 3000;
        SPIConfigDTO configDTO = spy(new SPIConfigDTO("alias", InterfaceTypeEnum.PIGEON, testUrl) {

            @Override
            public int getTimeout() {
                return testTimeout;
            }
        });
        PigeonSPICallHandler handler = new PigeonSPICallHandler() {

            @Override
            public GenericService getSpiService(SPIConfigDTO spiConfigDTO) {
                InvokerConfig<GenericService> config = new InvokerConfig<>(spiConfigDTO.getInterfaceUrl(), GenericService.class);
                config.setTimeout(spiConfigDTO.getTimeout());
                config.setGeneric(GenericType.JSON_COMMON.getName());
                config.setCallType(CallMethod.CALLBACK.getName());
                // Verify configuration
                assertEquals(testUrl, config.getUrl());
                assertEquals(GenericService.class, config.getServiceInterface());
                assertEquals(testTimeout, config.getTimeout());
                assertEquals(GenericType.JSON_COMMON.getName(), config.getGeneric());
                assertEquals(CallMethod.CALLBACK.getName(), config.getCallType());
                return genericService1;
            }
        };
        // act & assert
        GenericService result = handler.getSpiService(configDTO);
        assertNotNull(result);
        assertEquals(genericService1, result);
    }

    /**
     * Test with minimum timeout value (boundary case)
     */
    @Test
    public void testGetSpiServiceWithMinimumTimeout() throws Throwable {
        // arrange
        String testUrl = "test.service.url";
        // minimum valid timeout
        int testTimeout = 1;
        SPIConfigDTO configDTO = spy(new SPIConfigDTO("alias", InterfaceTypeEnum.PIGEON, testUrl) {

            @Override
            public int getTimeout() {
                return testTimeout;
            }
        });
        PigeonSPICallHandler handler = new PigeonSPICallHandler() {

            @Override
            public GenericService getSpiService(SPIConfigDTO spiConfigDTO) {
                InvokerConfig<GenericService> config = new InvokerConfig<>(spiConfigDTO.getInterfaceUrl(), GenericService.class);
                config.setTimeout(spiConfigDTO.getTimeout());
                assertEquals(testTimeout, config.getTimeout());
                return genericService1;
            }
        };
        // act & assert
        GenericService result = handler.getSpiService(configDTO);
        assertNotNull(result);
        assertEquals(genericService1, result);
    }

    /**
     * Test with default timeout when not specified
     */
    @Test
    public void testGetSpiServiceWithDefaultTimeout() throws Throwable {
        // arrange
        String testUrl = "test.service.url";
        SPIConfigDTO configDTO = spy(new SPIConfigDTO("alias", InterfaceTypeEnum.PIGEON, testUrl));
        PigeonSPICallHandler handler = new PigeonSPICallHandler() {

            @Override
            public GenericService getSpiService(SPIConfigDTO spiConfigDTO) {
                InvokerConfig<GenericService> config = new InvokerConfig<>(spiConfigDTO.getInterfaceUrl(), GenericService.class);
                config.setTimeout(spiConfigDTO.getTimeout());
                // Default timeout from SPIConfigDTO
                assertEquals(5000, config.getTimeout());
                return genericService1;
            }
        };
        // act & assert
        GenericService result = handler.getSpiService(configDTO);
        assertNotNull(result);
        assertEquals(genericService1, result);
    }

    /**
     * Test normal case where service is successfully created
     */
    @Test
    public void testGetSpiServiceSuccess() throws Throwable {
        // arrange
        String testUrl = "test.service.url";
        SPIConfigDTO configDTO = new SPIConfigDTO("alias", InterfaceTypeEnum.PIGEON, testUrl);
        PigeonSPICallHandler handler = new PigeonSPICallHandler() {

            @Override
            public GenericService getSpiService(SPIConfigDTO spiConfigDTO) {
                return genericService1;
            }
        };
        // act
        GenericService result = handler.getSpiService(configDTO);
        // assert
        assertNotNull(result);
        assertEquals(genericService1, result);
    }
}
