package com.sankuai.dz.product.detail.metadata.sdk.model.layout.lion.config;

import com.dianping.pigeon.util.CollectionUtils;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigTemplateDTO;
import com.sankuai.dz.product.detail.metadata.sdk.utils.json.JacksonUtils;
import org.junit.Assert;
import org.junit.Test;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 20:45
 * 足疗预订
 */
public class ProductDetail_SecondCategory_2_521 extends BaseConfigTool{

    @Test
    public void testConfig() {
        //pageConfigTemplateDTO
        PageConfigTemplateDTO pageConfigTemplateDTO = new PageConfigTemplateDTO();
        PageConfigDataDTO mainConfigData = new PageConfigDataDTO();
        pageConfigTemplateDTO.setMainConfigData(mainConfigData);

        //mainConfigData
        //足疗预订基础模块
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "头图模块组",
                buildNormalModuleConfigDTO(
                        "module_detail_navigation_bar_normal", "GuideCommon", 1
                ),
                buildNormalModuleConfigDTO(
                        "module_detail_head_pic_banner", "GuideCommon", 1
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "商品基础信息模块组",
                buildExclusiveModuleConfigDTO(
                        "exclusiveModule1",
                        buildNormalModuleConfigDTO(
                                "module_detail_reserve_price_banner", "ReservePrice", 1
                        ),
                        buildNormalModuleConfigDTO(
                                "module_detail_reserve_period_price_banner", "ReservePrice", 1
                        )
                ),//价格条 or 限时优惠
                buildNormalModuleConfigDTO(
                        "module_detail_reserve_promo_banner", "ReservePrice", 1
                ),//优惠栏和优惠浮层
                buildNormalModuleConfigDTO(
                        "module_detail_discountcard", "GuideCommon", 1
                ),//折扣卡
                buildNormalModuleConfigDTO(
                        "module_detail_title", "GuideCommon", 1
                ),//标题
                buildNormalModuleConfigDTO(
                        "module_tags", "GuideCommon", 1
                ),//标签
                buildNormalModuleConfigDTO(
                        "module_detail_highlight", "GuideCommon", 1
                ),//亮点
                buildNormalModuleConfigDTO(
                        "module_detail_reminder_info_tag", "GuideCommon", 1,
                        buildOverlayModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 1)
                ),//须知条和须知浮层
                buildNormalModuleConfigDTO(
                        "module_detail_guarantee_info_tag", "GuideCommon", 1,
                        buildOverlayModuleConfigDTO("module_detail_guarantee_info_tag_popup", "GuideCommon", 1)
                )//保障条和保障浮层
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "Tab栏",
                buildTabModuleConfigDTO(
                        "module_detail_tab", "GuideCommon", 1,
                        buildTabModuleItemDTO(
                                "order_time_duration",
                                "可订时间",
                                buildModuleGroupConfigDTO(
                                        "可订时间",
                                        buildNormalModuleConfigDTO("module_detail_reserve_booking_time", "ReserveTime", 1)
                                ),
                                buildModuleGroupConfigDTO(
                                        "相关技师",
                                        buildNormalModuleConfigDTO("module_detail_reserve_technician_related", "TechnicianBooking", 1)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "deal_detail",
                                "项目详情",
                                buildModuleGroupConfigDTO(
                                        "结构化详情",
                                        buildNormalModuleConfigDTO("module_detail_structured_detail", "GuideCommon", 1)
                                ),
                                buildModuleGroupConfigDTO(
                                        "服务设施",
                                        buildNormalModuleConfigDTO("module_detail_service_facilities", "GuideCommon", 1)
                                ),
                                buildModuleGroupConfigDTO(
                                        "图文详情",
                                        buildNormalModuleConfigDTO("module_detail_image_text", "GuideCommon", 0)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "purchase_note",
                                "购买须知",
                                buildModuleGroupConfigDTO(
                                        "结构化购买须知",
                                        buildNormalModuleConfigDTO("module_detail_booking_instructions", "GuideCommon", 0)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "product_review",
                                "评价",
                                buildModuleGroupConfigDTO(
                                        "评价",
                                        buildNormalModuleConfigDTO("module_detail_mt_review_v1", "GuideCommon", 0)
                                )
                        ),
                        buildTabModuleItemDTO(
                                "product_review",
                                "评价",
                                buildModuleGroupConfigDTO(
                                        "评价",
                                        buildNormalModuleConfigDTO("module_detail_dp_reserve_shop_review", "GuideCommon", 0)
                                )
                        )
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "推荐",
                buildNormalModuleConfigDTO(
                        "module_detail_recommend", "GuideCommon", 1
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "底Bar区域",
                buildNormalModuleConfigDTO(
                "module_detail_reserve_price_bottom_bar", "ReservePrice", 1
                )
        ));
        mainConfigData.getModuleGroupConfigs().add(buildModuleGroupConfigDTO(
                "优惠浮层",
                buildNormalModuleConfigDTO(
                        "module_detail_reserve_promo_float_layer", "ReservePrice", 1
                )
        ));
        System.out.println(JacksonUtils.serialize(pageConfigTemplateDTO));
        Assert.assertTrue( pageConfigTemplateDTO.getMainConfigData() != null && !CollectionUtils.isEmpty(pageConfigTemplateDTO.getMainConfigData().getModuleGroupConfigs()));
    }

}