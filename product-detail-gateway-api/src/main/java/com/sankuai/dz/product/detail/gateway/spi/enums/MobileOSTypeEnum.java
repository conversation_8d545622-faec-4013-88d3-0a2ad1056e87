package com.sankuai.dz.product.detail.gateway.spi.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/10 10:55
 */
@Getter
public enum MobileOSTypeEnum {

    UNKNOWN(""),
    IOS("ios"),
    ANDROID("android");

    private final String code;

    MobileOSTypeEnum(String code) {
        this.code = code;
    }

    public static MobileOSTypeEnum fromCode(String code) {
        for (MobileOSTypeEnum value : MobileOSTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static boolean containsCode(String code) {
        for (MobileOSTypeEnum value : MobileOSTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return true;
            }
        }
        return false;
    }

}