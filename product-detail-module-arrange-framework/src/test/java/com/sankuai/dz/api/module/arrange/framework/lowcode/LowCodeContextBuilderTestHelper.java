package com.sankuai.dz.api.module.arrange.framework.lowcode;

import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.product.detail.gateway.api.bff.request.PageConfigRoutingKey;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.datasource.DataSourceConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.ModuleDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.layout.PageDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.display.config.storage.dto.ConfigQueryResponse;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * LowCodeContextBuilder 测试辅助工具类
 * 提供测试数据创建和验证方法
 * 
 * @Author: guangyujie
 * @Date: 2025/1/16 11:00
 */
public class LowCodeContextBuilderTestHelper {

    /**
     * 创建有效的美团路由key
     */
    public static PageConfigRoutingKey createValidMTRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(1); // 美团商品类型
        routingKey.setProductFirstCategoryId(100);
        routingKey.setProductSecondCategoryId(200);
        routingKey.setProductThirdCategoryId(300);
        
        // 添加可选路由参数
        Map<String, String> optionalMap = new HashMap<>();
        optionalMap.put("CLIENT_TYPE", "MT_APP");
        optionalMap.put("PAGE_REGION", "BEIJING");
        routingKey.setOptionalRoutingKeyMap(optionalMap);
        
        return routingKey;
    }

    /**
     * 创建有效的点评路由key
     */
    public static PageConfigRoutingKey createValidDPRoutingKey() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(2); // 点评商品类型
        routingKey.setProductFirstCategoryId(500);
        routingKey.setProductSecondCategoryId(600);
        routingKey.setProductThirdCategoryId(700);
        
        // 添加可选路由参数
        Map<String, String> optionalMap = new HashMap<>();
        optionalMap.put("CLIENT_TYPE", "DP_APP");
        optionalMap.put("PAGE_REGION", "SHANGHAI");
        routingKey.setOptionalRoutingKeyMap(optionalMap);
        
        return routingKey;
    }

    /**
     * 创建有效的产品详情页面请求
     */
    public static ProductDetailPageRequest createValidPageRequest() {
        ProductDetailPageRequest pageRequest = new ProductDetailPageRequest();
        pageRequest.setPageConfigRoutingKey(createValidMTRoutingKey());
        pageRequest.setProductId(12345L);
        pageRequest.setClientType(100);
        return pageRequest;
    }

    /**
     * 创建有效的模块编排请求
     */
    public static ModuleArrangeRequest createValidModuleArrangeRequest() {
        ProductDetailPageRequest pageRequest = createValidPageRequest();
        Set<String> moduleKeys = new HashSet<>(Arrays.asList("header", "product_info", "recommend"));
        return new ModuleArrangeRequest(pageRequest, moduleKeys);
    }

    /**
     * 创建包含大量模块的编排请求
     */
    public static ModuleArrangeRequest createLargeModuleArrangeRequest(int moduleCount) {
        ProductDetailPageRequest pageRequest = createValidPageRequest();
        Set<String> moduleKeys = new HashSet<>();
        for (int i = 1; i <= moduleCount; i++) {
            moduleKeys.add("module" + i);
        }
        return new ModuleArrangeRequest(pageRequest, moduleKeys);
    }

    /**
     * 创建空模块的编排请求
     */
    public static ModuleArrangeRequest createEmptyModuleArrangeRequest() {
        ProductDetailPageRequest pageRequest = createValidPageRequest();
        Set<String> emptyModuleKeys = new HashSet<>();
        return new ModuleArrangeRequest(pageRequest, emptyModuleKeys);
    }

    /**
     * 创建模拟的数据源配置
     */
    public static DataSourceConfigBO createMockDataSourceConfig(String fetcherName, String... fields) {
        Set<String> fieldSet = new HashSet<>(Arrays.asList(fields));
        return new DataSourceConfigBO(fetcherName, fieldSet);
    }

    /**
     * 创建模拟的模块显示配置
     */
    public static ModuleDisplayConfigBO createMockModuleDisplayConfig(String moduleKey, 
                                                                      Map<String, DataSourceConfigBO> dataSources) {
        ModuleDisplayConfigBO moduleConfig = mock(ModuleDisplayConfigBO.class);
        when(moduleConfig.getAllDataSource()).thenReturn(dataSources);
        return moduleConfig;
    }

    /**
     * 创建模拟的页面显示配置
     */
    public static PageDisplayConfigBO createMockPageDisplayConfig(Map<String, ModuleDisplayConfigBO> moduleConfigs) {
        PageDisplayConfigBO pageConfig = mock(PageDisplayConfigBO.class);
        when(pageConfig.getMatchedModuleDisplayConfigs(any(PageConfigRoutingKey.class)))
                .thenReturn(moduleConfigs);
        return pageConfig;
    }

    /**
     * 创建标准的测试页面配置
     */
    public static PageDisplayConfigBO createStandardTestPageConfig() {
        Map<String, ModuleDisplayConfigBO> moduleConfigs = new HashMap<>();
        
        // 创建 header 模块配置
        Map<String, DataSourceConfigBO> headerDataSources = new HashMap<>();
        headerDataSources.put("user-fetcher", createMockDataSourceConfig("user-fetcher", "userId", "userName"));
        headerDataSources.put("common-fetcher", createMockDataSourceConfig("common-fetcher", "timestamp", "version"));
        moduleConfigs.put("header", createMockModuleDisplayConfig("header", headerDataSources));
        
        // 创建 product_info 模块配置
        Map<String, DataSourceConfigBO> productInfoDataSources = new HashMap<>();
        productInfoDataSources.put("product-fetcher", createMockDataSourceConfig("product-fetcher", "productId", "productName", "price"));
        productInfoDataSources.put("common-fetcher", createMockDataSourceConfig("common-fetcher", "timestamp", "region"));
        moduleConfigs.put("product_info", createMockModuleDisplayConfig("product_info", productInfoDataSources));
        
        // 创建 recommend 模块配置
        Map<String, DataSourceConfigBO> recommendDataSources = new HashMap<>();
        recommendDataSources.put("recommend-fetcher", createMockDataSourceConfig("recommend-fetcher", "recommendList", "algorithm"));
        moduleConfigs.put("recommend", createMockModuleDisplayConfig("recommend", recommendDataSources));
        
        return createMockPageDisplayConfig(moduleConfigs);
    }

    /**
     * 创建包含重复数据源的页面配置
     */
    public static PageDisplayConfigBO createPageConfigWithDuplicateDataSources() {
        Map<String, ModuleDisplayConfigBO> moduleConfigs = new HashMap<>();
        
        // 模块1：使用 shared-fetcher(field1, field2) 和 unique-fetcher-1(field5)
        Map<String, DataSourceConfigBO> module1DataSources = new HashMap<>();
        module1DataSources.put("shared-fetcher", createMockDataSourceConfig("shared-fetcher", "field1", "field2"));
        module1DataSources.put("unique-fetcher-1", createMockDataSourceConfig("unique-fetcher-1", "field5"));
        moduleConfigs.put("module1", createMockModuleDisplayConfig("module1", module1DataSources));
        
        // 模块2：使用 shared-fetcher(field2, field3) 和 unique-fetcher-2(field6)
        Map<String, DataSourceConfigBO> module2DataSources = new HashMap<>();
        module2DataSources.put("shared-fetcher", createMockDataSourceConfig("shared-fetcher", "field2", "field3"));
        module2DataSources.put("unique-fetcher-2", createMockDataSourceConfig("unique-fetcher-2", "field6"));
        moduleConfigs.put("module2", createMockModuleDisplayConfig("module2", module2DataSources));
        
        // 模块3：使用 shared-fetcher(field3, field4)
        Map<String, DataSourceConfigBO> module3DataSources = new HashMap<>();
        module3DataSources.put("shared-fetcher", createMockDataSourceConfig("shared-fetcher", "field3", "field4"));
        moduleConfigs.put("module3", createMockModuleDisplayConfig("module3", module3DataSources));
        
        return createMockPageDisplayConfig(moduleConfigs);
    }

    /**
     * 创建空的页面配置
     */
    public static PageDisplayConfigBO createEmptyPageConfig() {
        return createMockPageDisplayConfig(new HashMap<>());
    }

    /**
     * 创建配置查询响应
     */
    public static ConfigQueryResponse createConfigQueryResponse(String key, PageDisplayConfigBO config) {
        return new ConfigQueryResponse(key, config);
    }

    /**
     * 创建空的配置查询响应
     */
    public static ConfigQueryResponse createEmptyConfigQueryResponse() {
        return new ConfigQueryResponse("empty-key", null);
    }

    /**
     * 验证 LowCodeContext 的有效性
     */
    public static boolean isValidLowCodeContext(LowCodeContext context) {
        return context != null;
    }

    /**
     * 验证 LowCodeContext 是否为空
     */
    public static boolean isEmptyLowCodeContext(LowCodeContext context) {
        return context != null 
                && context.getAllRelatedDataSourceConfig().isEmpty()
                && context.getAllDependentFetcherNames().isEmpty();
    }

    /**
     * 获取 LowCodeContext 的描述信息
     */
    public static String getLowCodeContextDescription(LowCodeContext context) {
        if (context == null) {
            return "null LowCodeContext";
        }
        
        int dataSourceCount = context.getAllRelatedDataSourceConfig().size();
        int fetcherCount = context.getAllDependentFetcherNames().size();
        
        return String.format("LowCodeContext[dataSourceCount=%d, fetcherCount=%d, fetcherNames=%s]",
                dataSourceCount, fetcherCount, context.getAllDependentFetcherNames());
    }

    /**
     * 验证数据源合并的正确性
     */
    public static boolean verifyDataSourceMerging(LowCodeContext context, String fetcherName, String... expectedFields) {
        DataSourceConfigBO dataSource = context.getDataSourceConfig(fetcherName);
        if (dataSource == null) {
            return false;
        }
        
        Set<String> actualFields = dataSource.getOptionalFieldName();
        Set<String> expectedFieldSet = new HashSet<>(Arrays.asList(expectedFields));
        
        return actualFields.equals(expectedFieldSet);
    }

    /**
     * 创建包含特殊字符的测试数据
     */
    public static ModuleArrangeRequest createSpecialCharacterRequest() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail-特殊场景");
        routingKey.setProductType(1);
        routingKey.setProductFirstCategoryId(100);
        
        ProductDetailPageRequest pageRequest = new ProductDetailPageRequest();
        pageRequest.setPageConfigRoutingKey(routingKey);
        
        Set<String> moduleKeys = new HashSet<>(Arrays.asList("module-特殊字符_123", "module-emoji😀"));
        return new ModuleArrangeRequest(pageRequest, moduleKeys);
    }

    /**
     * 创建边界值测试数据
     */
    public static ModuleArrangeRequest createBoundaryValueRequest() {
        PageConfigRoutingKey routingKey = new PageConfigRoutingKey();
        routingKey.setScene("detail");
        routingKey.setProductType(Integer.MAX_VALUE);
        routingKey.setProductFirstCategoryId(Integer.MAX_VALUE);
        routingKey.setProductSecondCategoryId(Integer.MAX_VALUE);
        routingKey.setProductThirdCategoryId(Integer.MAX_VALUE);
        
        ProductDetailPageRequest pageRequest = new ProductDetailPageRequest();
        pageRequest.setPageConfigRoutingKey(routingKey);
        pageRequest.setProductId(Long.MAX_VALUE);
        
        Set<String> moduleKeys = new HashSet<>(Arrays.asList("boundary-module"));
        return new ModuleArrangeRequest(pageRequest, moduleKeys);
    }

    /**
     * 创建性能测试用的大量数据
     */
    public static PageDisplayConfigBO createPerformanceTestPageConfig(int moduleCount, int dataSourcePerModule) {
        Map<String, ModuleDisplayConfigBO> moduleConfigs = new HashMap<>();
        
        for (int i = 1; i <= moduleCount; i++) {
            Map<String, DataSourceConfigBO> dataSources = new HashMap<>();
            
            for (int j = 1; j <= dataSourcePerModule; j++) {
                String fetcherName = "fetcher" + i + "_" + j;
                String[] fields = {"field" + i + "_" + j + "_1", "field" + i + "_" + j + "_2"};
                dataSources.put(fetcherName, createMockDataSourceConfig(fetcherName, fields));
            }
            
            moduleConfigs.put("module" + i, createMockModuleDisplayConfig("module" + i, dataSources));
        }
        
        return createMockPageDisplayConfig(moduleConfigs);
    }

    /**
     * 验证性能测试结果
     */
    public static boolean verifyPerformanceTestResult(LowCodeContext context, int expectedModuleCount, int expectedDataSourceCount) {
        if (context == null) {
            return false;
        }
        
        int actualDataSourceCount = context.getAllRelatedDataSourceConfig().size();
        return actualDataSourceCount <= expectedDataSourceCount; // 可能因为合并而减少
    }

    /**
     * 创建测试用的异常
     */
    public static RuntimeException createTestException(String message) {
        return new RuntimeException("Test Exception: " + message);
    }

    /**
     * 验证异常处理的正确性
     */
    public static boolean verifyExceptionHandling(LowCodeContext context) {
        // 异常情况下应该返回空的 LowCodeContext
        return isEmptyLowCodeContext(context);
    }
}
