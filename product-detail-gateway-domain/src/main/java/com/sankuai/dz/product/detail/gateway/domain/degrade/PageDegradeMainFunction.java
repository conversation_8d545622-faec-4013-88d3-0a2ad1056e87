package com.sankuai.dz.product.detail.gateway.domain.degrade;

import com.dianping.cat.Cat;
import com.sankuai.dz.product.detail.gateway.api.bff.response.ProductDetailPageMergeResponse;
import com.sankuai.dz.product.detail.gateway.domain.bean.ProductCategory;
import com.sankuai.dz.product.detail.gateway.domain.context.GatewayContext;
import com.sankuai.dz.product.detail.gateway.domain.context.InvokerInfo;
import com.sankuai.dz.product.detail.gateway.domain.degrade.exception.PageDegradeException;
import com.sankuai.dz.product.detail.gateway.spi.enums.ModuleResponseCodeEnum;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/27 17:19
 */
@Slf4j
@Component
public class PageDegradeMainFunction {

    public void process(final GatewayContext context) {
        final long startTime = System.currentTimeMillis();
        try {
            final Set<String> allModuleKeys = context.getInvokerInfoList().stream()
                    .map(InvokerInfo::getModuleKeys)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
            final Map<String, String> logMetricDimension = buildLogMetricDimension(context);
            final ProductDetailPageMergeResponse pageResponse = context.getPageResponse();
            final Map<String, GenericModuleResponse> allResponses = pageResponse.getData().getResponse();
            for (String moduleKey : allModuleKeys) {
                GenericModuleResponse response = allResponses.get(moduleKey);
                logMetricDimension.put("moduleKey", moduleKey);
                int responseCode = Optional.ofNullable(response).map(GenericModuleResponse::getCode).orElse(ModuleResponseCodeEnum.FAILURE.getCode());
                Cat.logMetricForCount(
                        String.format("ModuleResponse-%s-%s", context.getUnifiedRequest().getProductType(), responseCode),
                        logMetricDimension
                );
            }
        } catch (Throwable throwable) {
            log.error("PageDegradeMainFunction", new PageDegradeException(throwable));
        }
        //耗时打点
        Cat.newCompletedTransactionWithDuration(
                "ProductUnifiedPageService", "PageDegradeMainFunction",
                System.currentTimeMillis() - startTime
        );
    }

    private Map<String, String> buildLogMetricDimension(final GatewayContext gatewayContext) {
        Map<String, String> map = new HashMap<>();
        String secondCategoryStr = Optional.ofNullable(gatewayContext.getProductCategory())
                .map(ProductCategory::getSecondId)
                .map(String::valueOf)
                .orElse("0");
        map.put("secondCategory", secondCategoryStr);
        return map;
    }

}
