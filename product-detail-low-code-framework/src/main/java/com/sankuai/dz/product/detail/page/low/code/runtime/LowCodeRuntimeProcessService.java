package com.sankuai.dz.product.detail.page.low.code.runtime;

import com.dianping.cat.Cat;
import com.sankuai.dz.product.detail.page.low.code.display.config.bo.component.ComponentDisplayConfigBO;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.dz.product.detail.page.low.code.runtime.request.LowCodeProcessRequest;
import com.sankuai.dz.product.detail.page.low.code.runtime.response.ComponentProcessResult;
import com.sankuai.dz.product.detail.page.low.code.runtime.response.LowCodeProcessResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/5/27 14:05
 */
@Slf4j
public class LowCodeRuntimeProcessService {

    /**
     * 低代码处理流程主函数
     */
    public static LowCodeProcessResponse process(final LowCodeProcessRequest request) {
        final long startTime = System.currentTimeMillis();
        List<ComponentProcessResult> result = new ArrayList<>();
        for (ComponentDisplayConfigBO config : request.getComponentConfigs()) {
            try {
                ComponentBO componentBO = config.buildComponentBO(request.getData(), 0);
                result.add(ComponentProcessResult.succeed(componentBO));
            } catch (Throwable throwable) {
                result.add(ComponentProcessResult.fail(config.getComponentKey(), throwable));
            }
        }
        Cat.newCompletedTransactionWithDuration("LowCodeRuntimeProcessService", "process", System.currentTimeMillis() - startTime);
        return new LowCodeProcessResponse(result);
    }

}
