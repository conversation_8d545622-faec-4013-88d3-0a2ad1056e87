package com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.CommonBuilderDefinition;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;

/**
 * @Author: guangyujie
 * @Date: 2025/2/3 11:55
 */
public abstract class BaseBuilder<T extends AbstractModuleVO> extends AbstractBuilder<T, CommonBuilderDefinition> {

    @Override
    protected boolean isBuilderSupportedForLowCode() {
        return true;
    }

}
