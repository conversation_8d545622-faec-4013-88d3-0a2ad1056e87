package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * @Author: guangyujie
 * @Date: 2025/3/14 11:20
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
@Scope("prototype")
public @interface ComponentFetcher {

    /**
     * 聚合fetcher（父fetcher）
     */
    Class<? extends AggregateFetcherContext> aggregateFetcher();

    /**
     * 填充request的前置依赖，这些依赖会在运行时动态传递到aggregateFetcher的依赖上
     */
    Class<? extends BaseFetcherContext>[] fillRequestDependencies() default {};

    /**
     * 构建返回值的前置依赖
     */
    Class<? extends BaseFetcherContext>[] buildResultDependencies() default {};

}
