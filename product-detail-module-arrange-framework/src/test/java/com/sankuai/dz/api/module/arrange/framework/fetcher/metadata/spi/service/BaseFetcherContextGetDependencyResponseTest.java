package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.FetcherEngine;
import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherFatalException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.dag.DAG;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.FetcherDefinition;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherContextInitResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BaseFetcherContextGetDependencyResponseTest {

    @Mock
    private DAG dag;

    @Mock
    private FetcherDefinition fetcherDefinition;

    // Test implementation of BaseFetcherContext
    private static class TestFetcherContext extends BaseFetcherContext<FetcherReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    // Test dependency fetcher context
    private static class DependencyFetcherContext extends BaseFetcherContext<FetcherReturnValueDTO, FetcherDefinition> {

        @Override
        protected FetcherContextInitResult doInitContext(FetcherEngine fetcherEngine) {
            return null;
        }

        @Override
        protected FetcherContextInitResult doInitContextExceptionally(FetcherEngine fetcherEngine, Throwable throwable) {
            return null;
        }
    }

    /**
     * Test successful dependency response retrieval when dependency exists and is an ancestor
     */
    @Test
    public void testGetDependencyResponse_SuccessfulRetrieval() {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        context.dag = dag;
        context.fetcherDefinition = fetcherDefinition;
        Map<String, BaseFetcherContext> contextMap = new HashMap<>();
        DependencyFetcherContext dependencyContext = new DependencyFetcherContext();
        FetcherResponse<FetcherReturnValueDTO> expectedResponse = FetcherResponse.succeed(mock(FetcherReturnValueDTO.class));
        when(fetcherDefinition.getFetcherName()).thenReturn("TestFetcher");
        when(dag.isAncestor("TestFetcher", "DependencyFetcherContext")).thenReturn(true);
        contextMap.put("DependencyFetcherContext", dependencyContext);
        // Use reflection to set private field
        try {
            java.lang.reflect.Field field = BaseFetcherContext.class.getDeclaredField("fetcherContextMap");
            field.setAccessible(true);
            field.set(context, contextMap);
            field = BaseFetcherContext.class.getDeclaredField("fetchResponseCache");
            field.setAccessible(true);
            field.set(dependencyContext, expectedResponse);
        } catch (Exception e) {
            fail("Test setup failed");
        }
        // act
        FetcherResponse<FetcherReturnValueDTO> result = context.getDependencyResponse(DependencyFetcherContext.class);
        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result);
    }

    /**
     * Test when dependency is not an ancestor
     */
    @Test
    public void testGetDependencyResponse_NotAncestor() {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        context.dag = dag;
        context.fetcherDefinition = fetcherDefinition;
        when(fetcherDefinition.getFetcherName()).thenReturn("TestFetcher");
        when(dag.isAncestor("TestFetcher", "DependencyFetcherContext")).thenReturn(false);
        // act & assert
        FetcherFatalException exception = assertThrows(FetcherFatalException.class, () -> context.getDependencyResponse(DependencyFetcherContext.class));
        assertTrue(exception.getMessage().contains("该Fetcher(TestFetcher)没有依赖"));
    }

    /**
     * Test when dependency context is null
     */
    @Test
    public void testGetDependencyResponse_NullDependencyContext() {
        // arrange
        TestFetcherContext context = new TestFetcherContext();
        context.dag = dag;
        context.fetcherDefinition = fetcherDefinition;
        Map<String, BaseFetcherContext> contextMap = new HashMap<>();
        when(fetcherDefinition.getFetcherName()).thenReturn("TestFetcher");
        when(dag.isAncestor("TestFetcher", "DependencyFetcherContext")).thenReturn(true);
        try {
            java.lang.reflect.Field field = BaseFetcherContext.class.getDeclaredField("fetcherContextMap");
            field.setAccessible(true);
            field.set(context, contextMap);
        } catch (Exception e) {
            fail("Test setup failed");
        }
        // act & assert
        FetcherFatalException exception = assertThrows(FetcherFatalException.class, () -> context.getDependencyResponse(DependencyFetcherContext.class));
        assertEquals("该fetcher的依赖fetcher未执行且不在FetcherContext中", exception.getMessage());
    }
}
