package com.sankuai.dz.product.detail.gateway.domain.data.merge.dto;

import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.generic.GenericModuleResponse;
import com.sankuai.dz.product.detail.metadata.sdk.error.DataMergeFatalException;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/1/20 17:32
 */
@Data
public class DataMergeRequest implements Serializable {

    /**
     * 接口入参
     */
    private ProductDetailPageRequest spiRequest;

    /**
     * 页面配置
     */
    private PageConfigDataDTO pageConfigData;

    /**
     * AB结果聚合
     */
    private List<ABResultDTO> abResultList;

    /**
     * 模块返回值，具体模块是否可用也需要判断
     */
    private Map<String, GenericModuleResponse> moduleResponse;

    public void checkParam() {
        if (spiRequest == null
                || pageConfigData == null
                || abResultList == null
                || moduleResponse == null) {
            throw new DataMergeFatalException("merge方法入参异常");
        }
    }

}
