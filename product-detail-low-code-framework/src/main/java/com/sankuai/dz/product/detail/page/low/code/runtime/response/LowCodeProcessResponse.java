package com.sankuai.dz.product.detail.page.low.code.runtime.response;

import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/5/27 14:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LowCodeProcessResponse {

    private List<ComponentProcessResult> result;

    public List<ComponentBO> getValidComponents() {
        return result.stream()
                .filter(ComponentProcessResult::isSuccess)
                .map(ComponentProcessResult::getComponentBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
