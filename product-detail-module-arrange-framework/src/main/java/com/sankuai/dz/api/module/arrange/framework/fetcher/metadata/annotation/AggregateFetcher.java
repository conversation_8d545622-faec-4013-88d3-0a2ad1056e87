package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation;

import com.sankuai.dz.api.module.arrange.framework.fetcher.engine.constant.FetcherConstant;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/14 11:20
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
@Scope("prototype")
public @interface AggregateFetcher {

    /**
     * 是否是开始节点
     */
    boolean isStartFetcher() default false;

    /**
     * 前一层依赖的Fetcher，只有isStartFetcher=true才能为空
     */
    Class<? extends BaseFetcherContext>[] previousLayerDependencies() default {};

    /**
     * fetcher超时时间，最大不超过FetcherConstant.MAX_TIME_OUT
     * @see FetcherConstant#MAX_TIME_OUT
     */
    long timeout() default FetcherConstant.PROD_MAX_TIME_OUT;

}
