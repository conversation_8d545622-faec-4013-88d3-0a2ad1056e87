package com.sankuai.dz.product.detail.gateway.spi.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ABDetailDTO implements Serializable {
    /**
     * 实验号
     * 示例：exp000001
     */
    private String expId;
    /**
     * 命中策略
     * 示例：exp000001_a
     */
    private String expStrategy;
    /**
     * 命中策略组别
     * 示例：a
     */
    private String expResult;
    /**
     * 实验上报信息
     * https://km.sankuai.com/page/118494674
     * 示例：{"ab_id":"exp000001_a","query_id":"43a79596-7159-4244-80f5-c0d0734b2ce0"}
     */
    private String expBiInfo;
}
