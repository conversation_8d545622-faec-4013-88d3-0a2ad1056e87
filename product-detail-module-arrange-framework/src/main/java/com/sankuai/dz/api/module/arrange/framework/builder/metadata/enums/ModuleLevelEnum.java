package com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/2/6 09:56
 */
@Getter
public enum ModuleLevelEnum {

    COMMON_DISPLAY_MODULE("普通展示模块", "可以降级，小概率会造成资损","收藏、点评、榜单、亮点、图文详情、购买须知、领券/发券等模块" ),
    COMMON_TRADE_MODULE("普通交易模块", "可以降级，大概率会造成资损", "搭售、同店推荐、、团购详情等"),
    CORE_DISPLAY_MODULE("核心展示模块", "不可以降级，肯定会造成资损", "头图、标题、价格、优惠、等"),
    CORE_TRADE_MODULE("核心交易模块", "不可以降级，肯定会造成资损", "底Bar、");

    private final String name;
    private final String desc;
    private final String example;

    ModuleLevelEnum(String name, String desc, String example) {
        this.name = name;
        this.desc = desc;
        this.example = example;
    }

}