package com.sankuai.dz.api.module.arrange.framework.fetcher.engine;


import org.junit.Assert;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/1/31 13:14
 */
public class FetcherEngineTest {

    @Test
    public void test() {
        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> "Task 1");
        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
            throw new RuntimeException("Task 2 failed");
        });
        CompletableFuture<String> future3 = CompletableFuture.supplyAsync(() -> "Task 3");

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3);

        try {
            allFutures.get(); // 等待所有任务完成
        } catch (InterruptedException | ExecutionException e) {
            System.out.println("allOf threw an exception: " + e.getMessage());
        }

        // 检查每个 future 的状态
        System.out.println("future1 isDone: " + future1.isDone());
        System.out.println("future1 isCompletedExceptionally: " + future1.isCompletedExceptionally());
        System.out.println("future2 isDone: " + future2.isDone());
        System.out.println("future2 isCompletedExceptionally: " + future2.isCompletedExceptionally());
        System.out.println("future3 isDone: " + future3.isDone());
        System.out.println("future3 isCompletedExceptionally: " + future3.isCompletedExceptionally());

        // 获取各个任务的结果
        try {
            System.out.println("future1 result: " + future1.join());
        } catch (Exception e) {
            System.out.println("future1 threw an exception: " + e.getMessage());
        }

        try {
            System.out.println("future2 result: " + future2.join());
        } catch (Exception e) {
            System.out.println("future2 threw an exception: " + e.getMessage());
        }

        try {
            System.out.println("future3 result: " + future3.join());
        } catch (Exception e) {
            System.out.println("future3 threw an exception: " + e.getMessage());
        }
        Assert.assertEquals(future1.join(), "Task 1");
    }

}