package com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.overlay;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ModuleTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.BizModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.ModuleConfigVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.layout.module.overlay.OverlayModuleVO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.request.BuildModuleVORequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 23:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "普通模块")
public class OverlayModuleConfigDTO extends BizModuleConfigDTO {

    @Override
    public ModuleTypeEnum getModuleType() {
        return ModuleTypeEnum.overlay;
    }

    @Override
    public List<ModuleConfigDTO> getAllModuleConfigs(int depth) {
        return Lists.newArrayList(this);
    }

    @Override
    public ModuleConfigVO buildVO(BuildModuleVORequest request, int depth) {
        OverlayModuleVO vo = new OverlayModuleVO();
        vo.setFirstScreenFlag(this.getFirstScreenFlag());
        vo.setModuleKey(this.getModuleKey());
        vo.setModuleType(this.getModuleType().name());
        return vo;
    }
}
