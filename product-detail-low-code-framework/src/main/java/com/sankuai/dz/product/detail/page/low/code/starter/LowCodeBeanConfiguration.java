package com.sankuai.dz.product.detail.page.low.code.starter;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Sets;
import com.sankuai.athena.digital.arch.starter.DAStarter;
import com.sankuai.athena.digital.arch.starter.DAStarterItemEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/5/28 16:15
 */
@Configuration
public class LowCodeBeanConfiguration {

    @Bean
    @DependsOn("dAStarter")
    public LowCodeStarter lowCodeStarter() {
        List<String> basePackages = Lion.getList(
                Environment.getAppName(), Environment.getAppName() + ".lowcode.starter.basePackages", String.class, new ArrayList<>()
        );
        LowCodeStarter lowCodeStarter = new LowCodeStarter();
        lowCodeStarter.setBasePackages(basePackages);
        return lowCodeStarter;
    }

    @Bean
    public DAStarter dAStarter() {
        DAStarter daStarter = new DAStarter();
        //仅需要解析类的元数据和数据上报
        daStarter.setStarterItemEnums(Sets.newHashSet(
                DAStarterItemEnum.CLASS_PARSER,
                DAStarterItemEnum.METADATA_REPORT
        ));
        List<String> basePackages = Lion.getList(
                Environment.getAppName(), Environment.getAppName() + ".lowcode.starter.basePackages", String.class, new ArrayList<>()
        );
        daStarter.setBasePackages(basePackages);
        return daStarter;
    }

}
