package com.sankuai.dz.product.detail.gateway.api.bff.response;

import com.sankuai.dz.product.detail.gateway.api.bff.vo.BizDataVO;
import com.sankuai.dz.product.detail.gateway.api.bff.vo.PageConfigDataVO;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABDetailDTO;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.PageResponseCodeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
public class ProductDetailPageMergeResponse implements Serializable {
    /**
     * ab实验结果，参考https://km.sankuai.com/collabpage/2688161252#id-2.1.2%20%E8%BF%94%E5%9B%9E%E5%80%BC
     */
    private List<ABDetailDTO> abResultList;

    /**
     * 布局信息，参考https://km.sankuai.com/collabpage/2688161252#id-2.1.2%20%E8%BF%94%E5%9B%9E%E5%80%BC
     */
    private PageConfigDataVO layout;

    /**
     * 数据信息，参考https://km.sankuai.com/collabpage/2695513940
     */
    private BizDataVO data;

    /**
     * 失败原因
     */
    private String msg;

    /**
     * 200成功，500失败，501部分失败
     */
    private int code;

    public static ProductDetailPageMergeResponse succeed(PageConfigDataVO layout, BizDataVO data, List<ABResultDTO> abResultList) {
        ProductDetailPageMergeResponse vo = new ProductDetailPageMergeResponse();
        vo.setCode(PageResponseCodeEnum.SUCCESS.getCode());
        vo.setMsg(null);
        vo.setLayout(layout);
        vo.setData(data);
        List<ABDetailDTO> mergeABDetailDTOList = Optional.ofNullable(abResultList)
                .orElse(new ArrayList<>())
                .stream()
                .map(ABResultDTO::getAbDetails)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        vo.setAbResultList(mergeABDetailDTOList);
        return vo;
    }

    public static ProductDetailPageMergeResponse fail(String msg) {
        ProductDetailPageMergeResponse vo = new ProductDetailPageMergeResponse();
        vo.setCode(PageResponseCodeEnum.FAILURE.getCode());
        vo.setMsg(msg);
        return vo;
    }
}
