package com.sankuai.dz.product.detail.metadata.sdk.page.config.merge.handler;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.dz.product.detail.metadata.sdk.enums.ConfigMergeTypeEnum;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.PageConfigDataDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.group.ModuleGroupConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.layout.dto.module.ModuleConfigDTO;
import com.sankuai.dz.product.detail.metadata.sdk.page.config.model.merge.rule.ModuleInsertRule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: guangyujie
 * @Date: 2025/1/15 21:15
 */
public class ModuleInsertHandler implements ConfigMergeHandler<ModuleInsertRule> {

    @Override
    public void merge(final PageConfigDataDTO mainConfigData, final ModuleInsertRule rule) {
        if (StringUtils.isAllBlank(rule.getFormerKey(), rule.getNextKey())
                || CollectionUtils.isEmpty(rule.getInsertModule())) {
            return;
        }
        if (StringUtils.isBlank(rule.getFormerKey())) {
            //如果former没有限制
            ModuleGroupConfigDTO moduleGroupConfigDTO = mainConfigData.getModuleGroup(
                    rule.getGroupName(), rule.getNextKey()
            );
            if (moduleGroupConfigDTO == null) {
                return;
            }
            for (int i = 0; i < moduleGroupConfigDTO.getModuleList().size(); i++) {
                ModuleConfigDTO moduleConfigDTO = moduleGroupConfigDTO.getModuleList().get(i);
                if (moduleConfigDTO.getModuleKey().equals(rule.getNextKey())) {
                    moduleGroupConfigDTO.getModuleList().addAll(i, rule.getInsertModule());
                    break;//数组长度发生变更，直接break避免死循环
                }
            }
        } else if (StringUtils.isBlank(rule.getNextKey())) {
            //如果next没有限制
            ModuleGroupConfigDTO moduleGroupConfigDTO = mainConfigData.getModuleGroup(
                    rule.getGroupName(), rule.getFormerKey()
            );
            if (moduleGroupConfigDTO == null) {
                return;
            }
            for (int i = 0; i < moduleGroupConfigDTO.getModuleList().size(); i++) {
                ModuleConfigDTO moduleConfigDTO = moduleGroupConfigDTO.getModuleList().get(i);
                if (moduleConfigDTO.getModuleKey().equals(rule.getFormerKey())) {
                    moduleGroupConfigDTO.getModuleList().addAll(i + 1, rule.getInsertModule());
                    break;//数组长度发生变更，直接break避免死循环
                }
            }
        } else {
            //former和next都有限制
            ModuleGroupConfigDTO moduleGroupConfigDTO = mainConfigData.getModuleGroup(
                    rule.getGroupName(), rule.getFormerKey()
            );
            if (moduleGroupConfigDTO == null) {
                return;
            }
            for (int i = 0; i < moduleGroupConfigDTO.getModuleList().size(); i++) {
                ModuleConfigDTO formerModuleConfigDTO = moduleGroupConfigDTO.getModuleList().get(i);
                if (!formerModuleConfigDTO.getModuleKey().equals(rule.getFormerKey())) {
                    continue;
                }
                if (i >= moduleGroupConfigDTO.getModuleList().size() - 1) {
                    continue;
                }
                ModuleConfigDTO nextModuleConfigDTO = moduleGroupConfigDTO.getModuleList().get(i + 1);
                if (nextModuleConfigDTO.getModuleKey().equals(rule.getNextKey())) {
                    moduleGroupConfigDTO.getModuleList().addAll(i + 1, rule.getInsertModule());
                    break;//数组长度发生变更，直接break避免死循环
                }
            }
        }
    }

    @Override
    @JsonIgnore
    public ConfigMergeTypeEnum getMergeType() {
        return ConfigMergeTypeEnum.MODULE_INSERT;
    }

}
