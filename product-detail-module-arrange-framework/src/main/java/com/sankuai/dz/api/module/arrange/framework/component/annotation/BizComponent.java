package com.sankuai.dz.api.module.arrange.framework.component.annotation;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/4/15 17:12
 */
@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Component
@Scope("prototype")
@SuppressWarnings("rawtypes")
public @interface BizComponent {

    /**
     * 依赖的Fetcher开始节点
     */
    Class<? extends BaseFetcherContext> startFetcher();

    /**
     * startFetcher的DAG中所有依赖的Fetcher
     */
    Class<? extends BaseFetcherContext>[] dependentFetchers();

}
