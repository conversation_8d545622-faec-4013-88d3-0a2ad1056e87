package com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.definition.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.exception.FetcherDefinitionException;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.AggregateFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.AggregateFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.Data;

/**
 * @Author: guangyujie
 * @Date: 2025/3/14 13:05
 */
@Data
public class FetcherDefinitionInitParam {

    private final boolean isStartFetcher;

    private final long timeout;

    public FetcherDefinitionInitParam(final Class<? extends NormalFetcherContext> fetcherClass,
                                      final Fetcher annotation) {
        if (annotation == null) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)没有标注注解", fetcherClass.getName()));
        }
        this.isStartFetcher = annotation.isStartFetcher();
        this.timeout = annotation.timeout();
    }

    public FetcherDefinitionInitParam(final Class<? extends ComponentFetcherContext> fetcherClass,
                                      final ComponentFetcher annotation) {
        if (annotation == null) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)没有标注注解", fetcherClass.getName()));
        }
        this.isStartFetcher = false;
        this.timeout = 1000L;
    }

    public FetcherDefinitionInitParam(final Class<? extends AggregateFetcherContext> fetcherClass,
                                      final AggregateFetcher annotation) {
        if (annotation == null) {
            throw new FetcherDefinitionException(String.format("非法Fetcher定义!!!该Fetcher(%s)没有标注注解", fetcherClass.getName()));
        }
        this.isStartFetcher = annotation.isStartFetcher();
        this.timeout = annotation.timeout();
    }

}
